# 锻星 MLOps 平台

锻星是一个全流程 MLOps 平台，提供从数据接入、模型训练、模型管理到模型服务的完整开发流程。

## 功能特点

- **模型训练**：支持任务管理、任务运行和在线开发
- **系统管理**：用户管理、系统配置和 Token 管理
- **YAML 配置**：内置 YAML 编辑器和查看器，支持语法检查
- **实时监控**：支持 Ray 框架的实时监控

## 技术栈

- [React](https://react.dev/) - 用户界面库
- [Rspack](https://rspack.dev/) - 高性能打包工具
- [Biome](https://biomejs.dev/) - 代码检查和格式化
- [TanStack Query](https://tanstack.com/query/v5/docs/framework/react/overview) - 数据获取和状态管理
- [Ant Design](https://ant.design/) - UI 组件库
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Zustand](https://zustand-demo.pmnd.rs/) - 状态管理
- [React Router](https://reactrouter.com/) - 路由管理
- [Husky](https://typicode.github.io/husky/) - Git 钩子工具
- [Commitlint](https://commitlint.js.org/) - 提交信息规范检查

## 开发环境设置

### 前提条件

- [Node.js](https://nodejs.org/)
- [pnpm](https://pnpm.io/installation)

### 安装步骤

1. 安装 [pnpm](https://pnpm.io/installation)

2. 安装 [Biome](https://biomejs.dev/) 扩展，例如：[VSCode 扩展](https://marketplace.visualstudio.com/items?itemName=biomejs.biome)

3. 安装依赖并启动开发服务器：

```bash
pnpm i
pnpm dev
```

### 可用脚本

- `pnpm dev` - 启动开发服务器
- `pnpm build` - 构建生产版本
- `pnpm build:staging` - 构建预发布版本
- `pnpm preview` - 预览构建结果
- `pnpm check` - 运行 Biome 代码检查
- `pnpm fix` - 自动修复 Biome 检查出的问题

## 提交规范

项目使用 Commitlint 强制执行提交消息格式。提交类型包括：

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码风格变更（不影响功能）
- `refactor`: 代码重构
- `test`: 测试相关
- `revert`: 回退提交
- `chore`: 构建过程或辅助工具变动
- `perf`: 性能优化
- `types`: 类型定义变更

## 项目结构

- `src/` - 源代码
  - `components/` - 可复用组件
  - `pages/` - 页面组件
  - `api/` - API 请求
  - `router/` - 路由配置
  - `stores/` - 状态管理
  - `assets/` - 静态资源

## 代理配置

开发环境已配置代理，可在 `rsbuild.config.ts` 中修改代理设置。
