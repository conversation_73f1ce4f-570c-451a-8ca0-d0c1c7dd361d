{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "space"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"useImportType": {"level": "error", "fix": "safe"}, "noNonNullAssertion": "warn", "useNodejsImportProtocol": {"level": "error", "fix": "safe"}, "noUnusedTemplateLiteral": {"level": "error", "fix": "safe"}, "useTemplate": "off"}, "suspicious": {"noExplicitAny": {"level": "off"}}, "a11y": {"useKeyWithClickEvents": "off"}}}, "javascript": {"formatter": {"semicolons": "asNeeded", "quoteStyle": "single"}}}