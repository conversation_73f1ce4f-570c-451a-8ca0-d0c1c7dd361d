{"name": "ant-design-admin-2024", "private": true, "version": "1.0.0", "scripts": {"dev": "rsbuild dev --open", "build": "rsbuild build", "build:staging": "rsbuild build --env-mode staging", "preview": "rsbuild preview", "prepare": "husky", "check": "biome check", "fix": "biome check --fix --unsafe"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/pro-components": "^2.8.7", "@codemirror/lang-yaml": "^6.1.2", "@codemirror/lint": "^6.8.5", "@radix-ui/react-scroll-area": "^1.2.0", "@rsbuild/plugin-svgr": "^1.2.0", "@tanstack/react-query": "^5.59.15", "@uiw/codemirror-theme-vscode": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "antd": "^5.25.3", "antd-style": "^3.7.1", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "lodash-es": "^4.17.21", "lucide-react": "^0.453.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.5", "yaml": "^2.8.0", "zustand": "^5.0.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.5.0", "@rsbuild/core": "^1.0.13", "@rsbuild/plugin-react": "^1.0.3", "@types/lodash-es": "^4.17.12", "@types/node": "^22.7.9", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "husky": "^9.1.6", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}