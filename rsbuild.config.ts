import path from 'node:path'
import { env } from 'node:process'
import { defineConfig } from '@rsbuild/core'
import { pluginReact } from '@rsbuild/plugin-react'
import { pluginSvgr } from '@rsbuild/plugin-svgr'

export default defineConfig({
  plugins: [pluginReact(), pluginSvgr()],
  source: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    proxy: {
      // '/api/v1': {
      //   target: 'http://192.168.56.75:8008',
      // },
      // '/api/v1': {
      //   target: 'http://192.168.56.64:8008',
      // },
      '/api/v1': {
        // target: 'https://mlops.ttyuyin.com',
        target: 'http://cicd-test3.ttyuyin.com',
        // target: 'http://192.168.56.20:8000', // 俊峰本地
        // target: 'http://192.168.66.210:8000', // 广哥本地
        // target: 'http://192.168.38.4:8000', // 子健本地
        // target: 'http://192.168.45.46:8000', // 小宇本地
      },
      '/api': {
        target: 'https://http.dog',
        pathRewrite: { '^/api': '' },
      },
    },
  },
  html: {
    favicon: './src/assets/pureLogo.png',
    title: '锻星',
    template: './src/index.html',
  },
})
