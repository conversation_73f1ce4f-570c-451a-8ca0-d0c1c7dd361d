@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  /** 帮助tailwind锁定默认边距为16，系统字体为14px*/
  font-size: 16px;
}

@layer utilities {
  .scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
  }
  .scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  .scrollbar::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  .scrollbar::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
  }
  .scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --ring: 0 0% 3.9%;

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --ring: 0 0% 83.1%;

    --radius: 0.5rem;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%;
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

html,
body {
  padding: 0;
  margin: 0;
}

.common-right-card > div.ant-card-head > div.ant-card-head-wrapper > div {
  padding-top: 0 !important;
}

.common-right-card > div.ant-card-head {
  @apply shrink-0;
  justify-content: start !important;
  padding: 0 !important;
}

.common-right-card > div.ant-card-body {
  @apply flex-1 overflow-y-hidden;
  padding: 0 !important;
}

/* modal title 下面加横线分隔的 hack ↓ */
.ant-modal-close {
  top: 7px !important;
}
.ant-modal-content {
  padding-top: 12px !important;
}
.ant-modal-header::after {
  content: "";
  display: block;
  width: calc(100% + 48px);
  position: relative;
  left: -24px;
  height: 1px;
  background-color: #f0f0f0;
  margin-top: 12px;
  margin-bottom: 20px;
}
/* modal title 下面加横线分隔的 hack ↑ */

.ant-form-item-explain-error {
  font-size: 12px;
  line-height: 1.1;
}

/** 使用radio group 爆改的简单样式，用于表格左侧的 “ 全部 | 我创建的 ” 选择控件 */
.simple-radio-group {
  > label {
    border: none !important;
    &:not(.ant-radio-button-wrapper-checked) {
      .ant-radio-button-label {
        color: #666666 !important;
      }
    }
    > div {
      display: none !important;
    }
    &::before {
      background-color: #e4e4e4 !important;
      height: 50% !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      z-index: 1 !important;
    }
  }
}
