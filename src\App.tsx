import { ConfigProvider } from 'antd'
import type React from 'react'
import { RouterProvider } from 'react-router-dom'
import { createDynamicRouter } from './router'
import './App.css'
import './fonts.css'
import './ant-design-customize.css'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import zhCN from 'antd/locale/zh_CN'
import { useEffect, useMemo } from 'react'
import { useUserProfile } from './api/user'
import useUserStore from './stores/user'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
})

const App: React.FC = () => {
  const { data } = useUserProfile()
  const setUserInfo = useUserStore((state) => state.setUserInfo)
  const currentRoleAtTeam = useUserStore((state) => state.currentRoleAtTeam)
  const userInfo = useUserStore((state) => state.userInfo)
  
  console.log(setUserInfo, 'setUserInfo')
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (data) {
      setUserInfo(data)
      console.log(data)
    }
  }, [data])
  
  // 根据用户角色动态创建路由，应用与LeftMenu.tsx相同的过滤逻辑
  const router = useMemo(() => {
    return createDynamicRouter()
  }, [currentRoleAtTeam, userInfo])
  
  return <RouterProvider router={router} />
}

export default function WrappedApp() {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#7569f0',
            colorInfo: '#7569f0',
            colorSuccess: '#52c41a',
            colorError: '#f1786d',
            colorWarning: '#faad14',
            colorTextBase: '#333333',
            colorBgBase: '#ffffff',
            borderRadius: 4,
            wireframe: false,
            fontFamily:
              'Microsoft Yahei,LarkHackSafariFont,LarkEmojiFont,LarkChineseQuote,-apple-system,BlinkMacSystemFont,Helvetica Neue,Tahoma,PingFang SC,Arial,Hiragino Sans GB,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji',
          },
          components: {
            Breadcrumb: {
              itemColor: '#333333',
              separatorColor: '#333333',
            },
            Menu: {
              // itemBg: '#f7f7f7',
              // itemSelectedBg: '#036afd',
              // itemSelectedColor: 'rgb(245, 246, 248)',
              itemMarginInline: 15,
              itemMarginBlock: 10,
              itemPaddingInline: 8,
            },
            Layout: {
              // lightSiderBg: '#f7f7f7',
              bodyBg: '#F3F5F9',
            },
            Tabs: {
              horizontalItemPadding: '12px',
            },
            Table: {
              headerBg: '#FAFAFA',
              headerBorderRadius: 0,
            },
            Form: { itemMarginBottom: 16 },
          },
        }}
      >
        <App />
      </ConfigProvider>
    </QueryClientProvider>
  )
}
