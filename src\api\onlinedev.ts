import { useQuery } from '@tanstack/react-query'
import type { CommonList } from './common'
import { req } from './req'
import type {
  TrainTaskClusterResource,
  TrainTaskVolumeMount,
} from './traintask'

// 在线开发环境状态枚举
export type EnumOnlineDevState = 'running' | 'shutdown' | 'pending'
export type EnumOnlineDevEnvironment = 'jupyter' | 'code-server'

// 在线开发项目接口
export interface OnlineDevItem {
  id: number
  devName: string
  environment: string
  imageUrl: string
  status: EnumOnlineDevState
  clusterId: number
  clusterName: string
  namespace: string
  priority: string
  teamId: number
  teamName: string
  createdAt: string
  createdByUserName: string
  updatedAt: string
  updatedByUserName: string
  devUrl?: string
  type: EnumOnlineDevEnvironment
  password: string
}

// 在线开发列表查询参数
export interface OnlineDevListParams {
  page: number
  pageSize: number
  teamId?: number
  onlyMy?: boolean
  onlyRunning?: boolean
  devName?: string
}

// 获取在线开发列表
export const getOnlineDevList = (params: OnlineDevListParams) => {
  return req.get<never, CommonList<OnlineDevItem>>(
    '/api/v1/online-development/list',
    {
      params,
    },
  )
}

// 使用在线开发列表的Hook
export const useOnlineDevList = (params: OnlineDevListParams) => {
  return useQuery({
    queryKey: ['OnlineDevList', JSON.stringify(params)],
    queryFn: () => getOnlineDevList(params),
    enabled: !!params.teamId,
  })
}

// 在线开发详情
export interface OnlineDevDetail {
  id?: number
  clusterId: number
  clusterName: string
  clusterResource: TrainTaskClusterResource
  devName: string
  teamId: number
  namespace: string
  imageUrl: string
  devUrl?: string
  type: string
  status?: string
  password?: string
  appName?: string
  cmdbId?: number
  envVars: Record<string, string>
  volumeMounts: TrainTaskVolumeMount[]
}

// 创建在线开发环境
export const createOnlineDev = (data: OnlineDevDetail) => {
  return req.post<never, never>('/api/v1/online-development/create', data)
}

// 更新在线开发环境
export const updateOnlineDev = (id: string, data: OnlineDevDetail) => {
  return req.patch<never, never>(
    `/api/v1/online-development/update/${id}`,
    data,
  )
}

// 获取在线开发详情
export const getOnlineDevDetail = (id: number) => {
  return req.get<never, { data: OnlineDevDetail }>(
    `/api/v1/online-development/${id}`,
  )
}

export const waitingForRunning = async (id: number, signal: AbortSignal) => {
  const res = await getOnlineDevDetail(id)
  if (res.data.status === 'running') {
    return res.data
  }
  return new Promise<OnlineDevDetail>((resolve, reject) => {
    const interval = setInterval(async () => {
      if (signal?.aborted) {
        clearInterval(interval)
        reject(new Error('Operation aborted'))
        return
      }

      const res = await getOnlineDevDetail(id)
      if (res.data.status === 'running') {
        clearInterval(interval)
        resolve(res.data)
      }
    }, 3000)
  })
}
// 获取在线开发镜像列表
const getOnlineDevImageList = (type: string) => {
  return req.get<never, { images: string[] }>(
    `/api/v1/online-development/image/list?type=${type}`,
  )
}

export const useOnlineDevImageList = (type?: string) => {
  return useQuery({
    queryKey: ['OnlineDevImageList', type],
    queryFn: () => getOnlineDevImageList(type!),
    enabled: !!type,
  })
}

// 启动在线开发环境
export const startOnlineDev = (id: number) => {
  return req.post<never, never>(`/api/v1/online-development/start/${id}`)
}

// 停止在线开发环境
export const stopOnlineDev = (id: number) => {
  return req.post<never, never>(`/api/v1/online-development/stop/${id}`)
}

// 删除在线开发环境
export const deleteOnlineDev = (id: number) => {
  return req.delete<never, never>(`/api/v1/online-development/delete/${id}`)
}
