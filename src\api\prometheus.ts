/**
 * Prometheus 查询语句管理工具
 * 提供各种监控指标的 PromQL 查询语句构建函数
 */

import { req } from './req'

// Prometheus API 响应类型定义
export interface PrometheusData {
  explanation: null
  result: PrometheusResult[]
  resultType: string
  [property: string]: any
}

export interface PrometheusResult {
  metric?: { [key: string]: any }
  values?: Array<[number, string]> // number是时间戳，string是数字字符串
  [property: string]: any
}

const isProd = window.location.host === 'mlops.ttyuyin.com'
/**
 * Prometheus 数据请求 API 函数
 * 替代原来的 usePrometheusReq Hook，使用全局 API 函数
 * @param query PromQL 查询语句
 * @param start 开始时间
 * @param end 结束时间
 * @param step 时间步长
 * @param debugLabel 调试标注，用于前端调试，后端不会使用
 * @returns Promise<PrometheusData>
 */
export const getPrometheusDetail = (
  query: string,
  start: string,
  end: string,
  step: string,
  debugLabel?: string,
) => {
  return req.get<never, { data: PrometheusData }>(
    '/api/v1/sre-monitor/query_range',
    {
      params: {
        query,
        start,
        end,
        step,
        timeout: '60s', // 控制从普罗的查询中，后端过期时间为60s
        ...(debugLabel && { debugLabel }), // 只有当 debugLabel 存在时才添加到参数中
      },
      timeout: 60000, // 控制从普罗的查询中，前端过期时间为60s
    },
  )
}

/**
 * 构建团队 GPU 利用率查询语句
 * @param teamId 团队ID
 * @returns PromQL 查询语句
 */
export const getTeamGpuUtilizationQuery = (teamId: string): string => {
  // GPU 利用率查询模板
  const queryTemplate = `avg(
  max(kube_pod_labels{
    cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
  }) by(pod)
  * on (pod) group_right() avg by (gpu, pod) (pod_core_utilization)
) * 100`
    .trim()
    .replace(/\s+/g, ' ')

  // 替换模板变量
  return queryTemplate.replace(/\$teamId/g, teamId)
}

/**
 * 构建团队 GPU 申请率查询语句
 * @param teamId 团队ID
 * @param teamQuota 团队配额
 * @returns PromQL 查询语句
 */
export const getTeamGpuRequestRateQuery = (
  teamId: string,
  teamQuota: number,
): string => {
  // GPU 申请率查询模板
  const queryTemplate = `sum(
  max(kube_pod_labels{
    cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
  }) by(pod,label_ml_team_id)
  * on (pod) group_right(label_ml_team_id)
  (
    max(kube_pod_container_resource_requests{
      cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
      resource=~"vke_volcengine_com_mgpu_core|tke_cloud_tencent_com_qgpu_core"
    }) by(pod)  
  )
) / $teamQuota `
    .trim()
    .replace(/\s+/g, ' ')

  // 替换模板变量
  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$teamQuota/g, teamQuota.toString())
}

/**
 * 构建团队资源实际使用率查询语句
 * @param teamId 团队ID
 * @param teamQuota 团队配额
 * @returns PromQL 查询语句
 */
export const getTeamActualUsageRateQuery = (
  teamId: string,
  teamQuota: number,
): string => {
  // 资源实际使用率查询模板（GPU利用率 * GPU申请率）
  const queryTemplate = `avg(
  max(kube_pod_labels{
    cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
  }) by(pod)
  * on (pod) group_right()
    avg by (gpu, pod) (pod_core_utilization)
) * 100 * 
sum(
  max(kube_pod_labels{
    cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
  }) by(pod,label_ml_team_id)
  * on (pod) group_right(label_ml_team_id)
  (
    max(kube_pod_container_resource_requests{
      cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
      resource=~"vke_volcengine_com_mgpu_core|tke_cloud_tencent_com_qgpu_core"
    } ) by(pod)  
  )
) / $teamQuota  / 100`
    .trim()
    .replace(/\s+/g, ' ')

  // 替换模板变量
  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$teamQuota/g, teamQuota.toString())
    .replace(/\s+/g, ' ')
}

/**
 * 验证查询语句是否有效
 * @param query PromQL 查询语句
 * @returns 是否有效
 */
export const validatePromQLQuery = (query: string): boolean => {
  // 基本的 PromQL 语法检查
  if (!query || query.trim().length === 0) {
    return false
  }

  // 检查是否包含基本的 Prometheus 函数或指标名
  const hasValidSyntax =
    /^[a-zA-Z_:][a-zA-Z0-9_:]*/.test(query.trim()) ||
    /^(avg|sum|rate|increase|max|min|count)\s*\(/.test(query.trim())

  return hasValidSyntax
}

/**
 * 格式化查询语句（移除多余空格和换行）
 * @param query PromQL 查询语句
 * @returns 格式化后的查询语句
 */
export const formatPromQLQuery = (query: string): string => {
  return query
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/\s*([(){}[\],])\s*/g, '$1')
}

/**
 * 构建集团 GPU 利用率查询语句
 * @returns PromQL 查询语句
 */
export const getGroupGpuUtilizationQuery = (): string => {
  // 集团 GPU 利用率查询模板（聚合所有团队的 GPU 利用率）
  const queryTemplate = `avg(
      max(kube_pod_labels{
        cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
        label_ml_team_id!="",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
      }) by(pod)
      * on (pod) group_right()
    avg by (gpu, pod) (pod_core_utilization)
) * 100
  `
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
}

/**
 * 构建集团 GPU 申请率查询语句
 * @returns PromQL 查询语句
 */
export const getGroupGpuRequestRateQuery = (totalQuota: number): string => {
  const queryTemplate = ` sum(
    max(kube_pod_labels{
        cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
        label_ml_team_id!="",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
    }) by(pod,label_ml_team_id)
    * on (pod) group_right(label_ml_team_id)
    (
        max(kube_pod_container_resource_requests{
        cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
        resource=~"vke_volcengine_com_mgpu_core|tke_cloud_tencent_com_qgpu_core"
        }) by(pod)   
    )
    ) / $totalQuota
    `
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$totalQuota/g, totalQuota.toString())
    .replace(/\s+/g, ' ')
}

/**
 * 构建集团平台资源实际使用率查询语句
 * @returns PromQL 查询语句
 */
export const getGroupPlatformResourceUtilizationQuery = (
  totalQuota: number,
): string => {
  const queryTemplate = `  avg(
  max(kube_pod_labels{
    cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
    label_ml_team_id!="",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
  }) by(pod)
  * on (pod) group_right()
  (
    avg by (gpu, pod) (pod_core_utilization)
  )
)  * 100 * 
sum(
  max(kube_pod_labels{
    cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
    label_ml_team_id!="",
    label_ml_env="${isProd ? 'prod' : 'dev'}"
  }) by(pod,label_ml_team_id)
  * on (pod) group_right(label_ml_team_id)
  max(
    kube_pod_container_resource_requests{
      cluster=~"k8s-hs-bj-1-prod|k8s-tc-bj-1-prod",
      resource=~"vke_volcengine_com_mgpu_core|tke_cloud_tencent_com_qgpu_core"
    } 
  )by(pod)
) / $totalQuota  / 100
  `
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$totalQuota/g, totalQuota.toString())
    .replace(/\s+/g, ' ')
}

/**
 * 构建 GPU 核心利用率查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句
 */
export const getGpuCoreUtilizationQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `avg(
  max(kube_pod_labels{
    cluster="$cluster",
    namespace="$namespace",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}",
    label_ml_exec_name="$execName",
    pod=${podName}
  }) by(pod)
  * on (pod) group_right()
  (
    avg by (gpu, pod) (pod_core_utilization{
    cluster="$cluster",
    namespace="$namespace",
    pod=${podName}
  } *100)
  )
) `
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
}

/**
 * 构建 GPU 显存利用率查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句
 */
export const getGpuMemoryUtilizationQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `avg(
  max(kube_pod_labels{
    cluster="$cluster",
    namespace="$namespace",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}",
    label_ml_exec_name="${execName}",
    pod=${podName}
  }) by(pod)
  
  * on (pod) group_right() 
  (
    avg by (pod) (
      pod_mem_utilization{
    cluster="$cluster",
    namespace="$namespace",
    pod=${podName}
  } * 100
    )
  )
)`
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
}

/**
 * 构建 GPU 显存核心用量查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句
 */
export const getGpuMemoryCoreUsageQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `sum(
  max(kube_pod_labels{
        cluster="$cluster",
    namespace="$namespace",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}",
    label_ml_exec_name="${execName}",
    pod=${podName}
  }) by(pod)
  
  * on (pod) group_right() 
  (
    label_replace(
      avg by (exported_pod) (pod_core_usage{
        cluster="$cluster",
    namespace="$namespace",
  }),
      "pod",
      "$1",
      "exported_pod",
      "(.+)"
    )
  )
)`
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
}

/**
 * 构建 GPU 显存用量查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句
 */
export const getGpuMemoryUsageQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `sum(
  max(kube_pod_labels{
        cluster="$cluster",
    namespace="$namespace",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}",
    label_ml_exec_name="${execName}",
    pod=${podName}
  }) by(pod)
  
  * on (pod) group_right() 
  (
    avg by (gpu, pod) (
      DCGM_FI_DEV_FB_USED{
        cluster="$cluster",
  }
    )
    
    or on (pod)
    label_replace(
      avg by (exported_pod) (pod_mem_usage{
        cluster="$cluster",
  }),
      "pod",
      "$1",
      "exported_pod",
      "(.+)"
    )
  )
) / 1024`
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
}

/**
 * 构建 CPU 利用率查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句
 */
export const getCpuUtilizationQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `max(
  kube_pod_labels{
    cluster="$cluster",
    namespace="$namespace",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}",
    label_ml_exec_name="${execName}",
    pod=${podName}
  }
) by(pod)
* on (pod) group_right()
(sum(
  rate(
    container_cpu_usage_seconds_total{
      cluster="$cluster",
      namespace="$namespace",
      pod=${podName}, 
      container!="POD", 
      container!="", 
      image!=""
    }[$__rate_interval]
  )
) by(pod)
/  
sum(kube_pod_container_resource_limits{
  cluster="$cluster",
  namespace="$namespace",
  pod=${podName},
  resource="cpu"
}) by(pod)) * 100`
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
    .replace(/\$__rate_interval/g, '5m')
}

/**
 * 构建内存利用率查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句（返回 GB 单位）
 */
export const getMemoryUtilizationQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `max(
  kube_pod_labels{
    cluster="$cluster",
    namespace="$namespace",
    label_ml_team_id="$teamId",
    label_ml_env="${isProd ? 'prod' : 'dev'}",
    label_ml_exec_name="${execName}",
    pod=${podName}
  }
) by(pod)
* on (pod) group_right()
(sum(
  container_memory_working_set_bytes{
    cluster="$cluster",
    namespace="$namespace",
    pod=${podName}, 
    container!="POD", 
    container!="", 
    image!=""
  }
) by(pod)
/  
sum(kube_pod_container_resource_limits{
  cluster="$cluster",
  namespace="$namespace",
  pod=${podName},
  resource="memory"
}) by(pod)) * 100`
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
}

/**
 * 构建 CPU 用量查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句（返回 CPU 核心数）
 */
export const getCpuUsageQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `  max(
    kube_pod_labels{
      cluster="$cluster",
      namespace="$namespace",
      label_ml_team_id="$teamId",
      label_ml_env="${isProd ? 'prod' : 'dev'}",
      label_ml_exec_name="${execName}",
      pod=${podName}
    }
  ) by(pod)
  * on (pod) group_right()
  sum(
    rate(
      container_cpu_usage_seconds_total{
      cluster="$cluster",
      namespace="$namespace", 
        pod=${podName}, 
        container!="POD", 
        container!="", 
        image!=""
      }[$__rate_interval]
    )
  ) by(pod)
`
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
    .replace(/\$__rate_interval/g, '5m')
}

/**
 * 构建内存用量查询语句
 * @param teamId 团队ID
 * @param execName 执行名称
 * @param podName Pod名称
 * @param cluster 集群名称（必填）
 * @param namespace 命名空间（必填）
 * @returns PromQL 查询语句（返回 GB 单位）
 */
export const getMemoryUsageQuery = (
  teamId: string,
  execName: string,
  podName: string,
  cluster: string,
  namespace: string,
): string => {
  const queryTemplate = `  max(
    kube_pod_labels{
      cluster="$cluster",
      namespace="$namespace",
      label_ml_team_id="$teamId",
      label_ml_env="${isProd ? 'prod' : 'dev'}",
      label_ml_exec_name="${execName}",
      pod=${podName}
    }
  ) by(pod)
  * on (pod) group_right()
  sum(
    container_memory_working_set_bytes{
      cluster="$cluster",
      namespace="$namespace",
      pod=${podName},
      container!="POD",
      container!="",
      image!=""
    }
  ) by(pod)
 / (1024 * 1024 * 1024)`
    .trim()
    .replace(/\s+/g, ' ')

  return queryTemplate
    .replace(/\$teamId/g, teamId)
    .replace(/\$execName/g, execName)
    .replace(/\$podName/g, podName)
    .replace(/\$cluster/g, cluster)
    .replace(/\$namespace/g, namespace)
}
