import { message } from 'antd'
import axios from 'axios'
import useUserStore from '../stores/user'

console.log(import.meta.env.MODE)
console.log(import.meta.env.PUBLIC_SSO_URL)
const API_ROOT = ''
const requestHandleError = (e: any) => {
  if (e.response.status === 401) {
    window.location.href = `${import.meta.env.PUBLIC_SSO_URL}/?sc=mlops&target=${window.location.href}`
    message.warning('您尚未登录，正在跳转SSO登录页面，请稍等...')
  } else {
    message.error(e.response.data.message)
  }
  return Promise.reject(e)
}

// 开发环境使用的固定token
const DEV_TOKEN =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVhdGVkQXQiOiIyMDI0LTEwLTIzIDE3OjI5OjM1IiwiZGVsZXRlZEF0IjpudWxsLCJlbWFpbCI6ImFkbWluQDUydHQuY29tIiwiZW1wbG95ZWVObyI6IlQwMDAwIiwiZXhwIjoxNzU3MDQyOTgyMDc2LCJpZCI6MTgsImlzQWN0aXZlIjoxLCJuaWNrTmFtZSI6IueuoeeQhuWRmCIsIm9yaWdfaWF0IjoxNzU0NDUwOTgyMDc2LCJwYXNzd29yZCI6IjZlMmVhOWU4MzA4YjcwMDFlODNkNDU2MGM4MDA5ZTEyIiwidWlkIjoiMTAwMDEiLCJ1cGRhdGVkQXQiOiIyMDI1LTA0LTE2IDExOjIyOjQyIiwidXNlcm5hbWUiOiJhZG1pbiJ9.iy7I8AWoWJKqpPDjwLWjmV3uAHpthsqBQ2__vmInQS0'
// 非管理员
// const DEV_TOKEN =
//   'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZoYicbu7g15rhsVxytZsKR3Lwxa-0CIReC_9uMRKi6Y'

// 根据环境获取token
const getToken = () => {
  // 开发环境使用固定token，生产环境使用用户store中的token
  if (import.meta.env.MODE === 'development') {
    return DEV_TOKEN
  }
  return useUserStore.getState().token
}

export const req = axios.create({
  baseURL: API_ROOT || '/',
  timeout: 20000,
})
req.interceptors.request.use(
  (config) => {
    config.headers.Authorization = `Bearer ${getToken()}`
    return config
  },
  (error) => Promise.reject(error),
)
req.interceptors.response.use(
  (response) => {
    return response.data.data
  },
  (e) => requestHandleError(e),
)
