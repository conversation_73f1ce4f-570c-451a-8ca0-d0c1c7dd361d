import { useQuery } from '@tanstack/react-query'
import { req } from './req'

export const getClusterList = () => {
  return req.get<never, { clusterList?: string[] }>(
    '/api/v1/resource/cluster/list',
  )
}

export const getNsList = (cluster: string) => {
  return req.get<never, { namespaceList?: string[] }>(
    '/api/v1/resource/namespace/list',
    { params: { cluster } },
  )
}

export const getGpuList = () => {
  return req.get<
    never,
    {
      gpuList?: {
        gpuCore?: string
        gpuMemory?: string
        gpuType?: string
        gpuAlias?: string
      }[]
    }
  >('/api/v1/resource/gpu/list')
}

export const getGpuOverviewList = () => {
  return req.get<
    never,
    {
      gpuOverview?: {
        gpuType?: string
        gpuCore?: string
        gpuMemory?: string
        gpuAlias?: string
        budget?: number
        allocation?: number
      }[]
    }
  >('/api/v1/resource/gpu-overview/list')
}

export const getTeamOverviewList = () => {
  return req.get<
    never,
    {
      teamOverview?: {
        id?: number
        name?: string
        teamId?: string
        clusterNamespaces?: {
          cluster?: string
          namespaces?: string[]
        }[]
        gpuQuotas?: {
          gpuType?: string
          gpuCore?: string
          gpuMemory?: string
          gpuAlias?: string
          nums?: number
        }[]
      }[]
    }
  >('/api/v1/resource/team-overview/list')
}

// Custom hooks
export const useClusterList = () => {
  return useQuery({
    queryKey: ['clusterList'],
    queryFn: getClusterList,
  })
}

export const useNamespaceList = (cluster: string) => {
  return useQuery({
    queryKey: ['namespaceList', cluster],
    queryFn: () => getNsList(cluster),
    enabled: !!cluster,
  })
}

export const useGpuList = () => {
  return useQuery({
    queryKey: ['gpuList'],
    queryFn: getGpuList,
  })
}

export const useGpuOverviewList = () => {
  return useQuery({
    queryKey: ['gpuOverviewList'],
    queryFn: getGpuOverviewList,
  })
}

export const useTeamOverviewList = () => {
  return useQuery({
    queryKey: ['teamOverviewList'],
    queryFn: getTeamOverviewList,
  })
}

// 资源概览详情响应接口
export interface ResourceOverviewDetailResponse {
  teamOverview?: MlopsInternalModelDtoTeamOverview[]
  totalQuota: number // API 层计算的总配额值
  [property: string]: any
}

export interface MlopsInternalModelDtoTeamOverview {
  clusterNamespaces?: MlopsInternalModelDtoClusterNamespace[]
  gpuQuotas?: MlopsInternalModelDtoGpuQuota[]
  id?: number
  name?: string
  teamId?: string
}

export interface MlopsInternalModelDtoClusterNamespace {
  cluster?: string
  namespaces?: string[]
}

export interface MlopsInternalModelDtoGpuQuota {
  gpuCore?: string
  gpuMemory?: string
  gpuType?: string
  nums?: number
}

// 资源使用详情响应接口（从 team.ts 迁移）
export interface ResourceUsageDetailResponse {
  running: MlopsInternalModelDtoGpuQuota[]
  pending: MlopsInternalModelDtoGpuQuota[]
  idle: MlopsInternalModelDtoGpuQuota[] // 注意：团队模式不需要此数据
  runningTrainTask: MlopsInternalModelDtoGpuQuota[]
  runningOnlineDevelopment: MlopsInternalModelDtoGpuQuota[]
  estimatedPendingTimeSeconds: number // 排队预计等待时长（秒）
}

/**
 * 获取资源概览详情
 * @param teamId 可选参数，如果是集团配额获取则不传参
 * @returns Promise<ResourceOverviewDetailResponse>
 */
export const getResourceOverviewDetail = async (
  teamId?: number,
): Promise<ResourceOverviewDetailResponse> => {
  try {
    // 调用原始 API
    const response = await req.get<never, ResourceOverviewDetailResponse>(
      '/api/v1/resource/team-overview/list',
      {
        params: teamId ? { teamId } : {},
      },
    )

    // 计算总配额 - 从组件层移动到 API 层
    let totalQuota = 0
    if (response?.teamOverview && Array.isArray(response.teamOverview)) {
      totalQuota = response.teamOverview.reduce((totalQuotaAcc, team) => {
        if (team.gpuQuotas && Array.isArray(team.gpuQuotas)) {
          const teamQuota = team.gpuQuotas.reduce((teamTotal, gpuQuota) => {
            const nums = gpuQuota.nums || 0
            // 数据验证：确保 nums 是有效的数字
            if (typeof nums === 'number' && nums >= 0) {
              return teamTotal + nums
            }
            console.warn(
              '[getResourceOverviewDetail] 发现无效的配额数值:',
              gpuQuota,
            )
            return teamTotal
          }, 0)
          return totalQuotaAcc + teamQuota
        }
        return totalQuotaAcc
      }, 0)
    }

    // 确保 totalQuota 是有效的非负数
    totalQuota = Math.max(0, totalQuota || 0)

    // 返回增强的响应数据
    return {
      ...response,
      totalQuota,
    }
  } catch (error) {
    console.error('[getResourceOverviewDetail] API 调用失败:', error)
    throw error
  }
}

/**
 * 获取资源使用详情（从 team.ts 迁移）
 * @param teamId 可选参数，不传则获取集团数据
 * @returns Promise<ResourceUsageDetailResponse>
 */
export const getResourceUsageDetail = (teamId?: number | string) => {
  return req.get<never, ResourceUsageDetailResponse>(
    '/api/v1/resource/gpu-monitor/list',
    {
      params: teamId ? { teamId: teamId.toString() } : {},
    },
  )
}

/**
 * 使用 React Query 获取资源使用详情的 Hook
 * @param teamId 可选参数，不传则获取集团数据
 * @param options React Query 配置选项
 * @returns React Query 结果
 */
export const useResourceUsageDetail = (
  teamId?: number | string,
  options?: {
    enabled?: boolean
    staleTime?: number
    gcTime?: number
    refetchOnWindowFocus?: boolean
    retry?: number | ((failureCount: number, error: any) => boolean)
  },
) => {
  return useQuery({
    queryKey: ['resourceUsageDetail', teamId],
    queryFn: () => getResourceUsageDetail(teamId),
    staleTime: 30 * 1000, // 30秒内数据被认为是新鲜的
    gcTime: 5 * 60 * 1000, // 缓存5分钟
    refetchOnWindowFocus: false, // 窗口聚焦时不自动重新请求
    ...options, // 允许覆盖默认配置
  })
}
