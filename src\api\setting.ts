import { useQuery } from '@tanstack/react-query'
import type { CommonList, ListQuery } from './common'
import { req } from './req'

type SettingListQuery = ListQuery & { s_key: string }

export interface SettingItem {
  category: string
  created_at: string
  deleted_at: null
  desc: string
  id: number
  key: string
  updated_at: string
  value: string
}

type SettingList = CommonList<SettingItem>

const getSettingList = (params: SettingListQuery) => {
  return req.get<never, SettingList>('/api/v1/setting/list', { params })
}

export const useSettingList = (params: SettingListQuery) => {
  return useQuery({
    queryKey: ['settingList', params.page, params.size],
    queryFn: () => getSettingList(params),
  })
}

export const delSetting = (id: number) => {
  return req.delete<never, SettingList>(`/api/v1/setting/delete/${id}`)
}

export type SettingCreate = {
  key: string
  value: string
  desc: string
  category: string
}

export const createSetting = (data: SettingCreate) => {
  return req.post<never, SettingList>('/api/v1/setting/create', data)
}

export const updateSetting = (id: number, data: SettingCreate) => {
  return req.patch<never, SettingList>(`/api/v1/setting/update/${id}`, data)
}
