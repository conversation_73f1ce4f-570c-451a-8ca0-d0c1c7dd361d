import { useMutation, useQuery } from '@tanstack/react-query'
import { req } from './req'

// Cluster Namespace interfaces
export interface ClusterNamespaceResponse {
  clusterNamespaces?: MlopsInternalModelDtoClusterNamespace[]
}

export interface MlopsInternalModelDtoClusterNamespace {
  cluster?: string
  namespaces?: string[]
}

// GPU Quota interfaces
export interface GpuQuotaResponse {
  gpuQuota?: MlopsInternalModelDtoGpuQuota[]
}

export interface MlopsInternalModelDtoGpuQuota {
  gpuCore?: string
  gpuMemory?: string
  gpuType?: string
  gpuAlias?: string
  nums?: number
}

// Request interfaces for CRUD operations
export interface CreateClusterNamespaceReq {
  teamId: number
  clusterNamespaces: MlopsInternalModelDtoClusterNamespace
}

export interface CreateGpuQuotaReq {
  teamId: number
  gpuQuota: MlopsInternalModelDtoGpuQuota
}

export interface UpdateGpuQuotaReq {
  teamId: number
  gpuQuota: MlopsInternalModelDtoGpuQuota
}

export interface DeleteClusterNamespaceParams {
  teamId: number
  cluster?: string
  namespace?: string
}

export interface DeleteGpuQuotaParams {
  teamId: number
  gpuType?: string
}

// API functions
export const getClusterNamespaceList = (params: { teamId: number }) => {
  return req.get<never, ClusterNamespaceResponse>(
    '/api/v1/team/cluster-namespace/list',
    { params },
  )
}

export const getGpuQuotaList = (params: { teamId: number }) => {
  return req.get<never, GpuQuotaResponse>('/api/v1/team/gpu-quota/list', {
    params,
  })
}

// Cluster Namespace CRUD operations
export const createClusterNamespace = (data: CreateClusterNamespaceReq) => {
  return req.post<CreateClusterNamespaceReq, never>(
    '/api/v1/team/cluster-namespace/create',
    data,
  )
}

export const deleteClusterNamespace = (
  params: DeleteClusterNamespaceParams,
) => {
  return req.delete<never, never>(
    '/api/v1/team/cluster-namespace/delete/' + params.teamId,
    { params },
  )
}

// GPU Quota CRUD operations
export const createGpuQuota = (data: CreateGpuQuotaReq) => {
  return req.post<CreateGpuQuotaReq, never>(
    '/api/v1/team/gpu-quota/create',
    data,
  )
}

export const updateGpuQuota = (data: UpdateGpuQuotaReq) => {
  return req.patch<UpdateGpuQuotaReq, never>(
    '/api/v1/team/gpu-quota/update',
    data,
  )
}

export const deleteGpuQuota = (params: DeleteGpuQuotaParams) => {
  return req.delete<never, never>(
    '/api/v1/team/gpu-quota/delete/' + params.teamId,
    { params },
  )
}

// Custom hooks
export const useClusterNamespaceList = (params: { teamId: number }) => {
  return useQuery({
    queryKey: ['ClusterNamespaceList', params.teamId],
    queryFn: () => getClusterNamespaceList(params),
    enabled: !!params.teamId,
  })
}

export const useGpuQuotaList = (params: { teamId: number }) => {
  return useQuery({
    queryKey: ['GpuQuotaList', params.teamId],
    queryFn: () => getGpuQuotaList(params),
    enabled: !!params.teamId,
  })
}

// Mutation hooks
export const useCreateClusterNamespace = () => {
  return useMutation({
    mutationFn: createClusterNamespace,
  })
}

export const useCreateGpuQuota = () => {
  return useMutation({
    mutationFn: createGpuQuota,
  })
}

export const useUpdateGpuQuota = () => {
  return useMutation({
    mutationFn: updateGpuQuota,
  })
}

export const useDeleteClusterNamespace = () => {
  return useMutation({
    mutationFn: deleteClusterNamespace,
  })
}

export const useDeleteGpuQuota = () => {
  return useMutation({
    mutationFn: deleteGpuQuota,
  })
}
