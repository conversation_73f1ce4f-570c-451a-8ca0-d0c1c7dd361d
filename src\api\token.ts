import { useQuery } from '@tanstack/react-query'
import type { CommonList, ListQuery } from './common'
import { req } from './req'

type TokenListQuery = ListQuery & { s_name: string }

export interface TokenItem {
  created_at: string
  deleted_at: null
  desc: string
  id: number
  name: string
  token: string
  updated_at: string
}

type TokenList = CommonList<TokenItem>

const getTokenList = (params: TokenListQuery) => {
  return req.get<never, TokenList>('/api/v1/openapi-token/list', { params })
}

export const useTokenList = (params: TokenListQuery) => {
  return useQuery({
    queryKey: ['settingList', params.page, params.size],
    queryFn: () => getTokenList(params),
  })
}

export const delToken = (id: number) => {
  return req.delete<never, TokenList>(`/api/v1/openapi-token/delete/${id}`)
}

export type TokenCreate = {
  name: string
  desc: string
}

export const createToken = (data: TokenCreate) => {
  return req.post<never, TokenList>('/api/v1/openapi-token/create', data)
}

export const getCicdToken = () => {
  return req.post<
    never,
    {
      cicdExpire: number
      cicdToken: string
    }
  >('/api/v1/openapi-token/third')
}
