import type { TaskPriority } from '@/constants/taskPriority'
import type {
  EnumTrainTaskExecutionState,
  TrainTaskExecution,
} from '@/pages/TrainTaskExecutionManagement/setting'
import { useQuery } from '@tanstack/react-query'
import type { AxiosRequestConfig } from 'axios'
import type { CommonList } from './common'
import { req } from './req'

export const getTrainTaskExecutionList = (config: AxiosRequestConfig<any>) => {
  return req.get<never, CommonList<TrainTaskExecution>>(
    '/api/v1/traintaskexecution/list',
    config,
  )
}

export const interruptTrainTaskExecution = (id: number) => {
  return req.post<never, never>(`/api/v1/traintaskexecution/interrupt/${id}`)
}
export const updateTaskExecutionPriority = (id: number, priority: string) => {
  return req.patch<never, never>(`/api/v1/traintaskexecution/priority/${id}`, {
    priority,
  })
}

export type TrainingFramework = 'TFJOB' | 'PYTORCH' | 'MPI' | 'RAY' | 'CUSTOM'

export interface TrainTaskItem {
  clusterId: number
  clusterName: string
  completeCount: number
  createdAt: string
  createdByUserName: string
  deletedAt: null
  id: number
  imageUrl: string
  namespace: string
  priority: TaskPriority
  startCmd: string
  taskName: string
  taskType: string
  trainingFramework: TrainingFramework
  taskYaml: string
  teamId: number
  triggerCount: number
  updatedAt: string
  updatedByUserName: string
  lastStatus: EnumTrainTaskExecutionState
}

export interface TrainTaskListParams {
  page: number
  pageSize: number
  teamId: number
  onlyMy?: boolean
  onlyUnfinished?: boolean
  taskName?: string
}

export const getTrainTaskList = (params: TrainTaskListParams) => {
  return req.get<never, CommonList<TrainTaskItem>>('/api/v1/traintask/list', {
    params,
  })
}

export const useTrainTaskList = (params: TrainTaskListParams) => {
  return useQuery({
    queryKey: ['TrainTaskList', JSON.stringify(params)],
    queryFn: () => getTrainTaskList(params),
    enabled: !!params.teamId,
  })
}

export interface TrainTaskDetail {
  clusterId: number
  clusterName: string
  clusterResource: TrainTaskClusterResource
  imageUrl: string
  namespace: string
  priority: string
  startCmd: string
  taskName: string
  taskType: string
  trainingFramework: TrainingFramework
  taskYaml: string
  teamId: number
  volumeMounts: TrainTaskVolumeMount[]
  envVars: Record<string, string>
  appName?: string
  cmdbId?: number
}

export interface TrainTaskClusterResource {
  limitCpu: string
  limitGpuCore: number
  limitGpuMemory: string
  limitMemory: string
  maxReplicas: number
  minReplicas: number
  requestCpu: string
  requestGpuCore?: number
  requestGpuMemory?: number
  requestMemory: string
  gpuType: string
}

export interface TrainTaskVolumeMount {
  mountPath: string
  name: string
  subPath: string
  volumeName: string
  volumeType: string
}

export const createTrainTask = (data: TrainTaskDetail) => {
  return req.post<never, never>('/api/v1/traintask/create', data)
}

export const updateTrainTask = (id: string, data: TrainTaskDetail) => {
  return req.patch<never, never>(`/api/v1/traintask/update/${id}`, data)
}

export const triggerTrainTask = (id: number) => {
  return req.post<never, never>(`/api/v1/traintask/trigger/${id}`, {
    triggerSource: 'MANUAL',
  })
}

export const getMonitorUrl = (id: number) => {
  return req.get<never, { url: string }>(
    `/api/v1/traintaskexecution/monitor-url/${id}`,
  )
}

export const getTrainTaskExecutionDetail = (id: number) => {
  return req.get<never, { data: TrainTaskExecution }>(
    `/api/v1/traintaskexecution/${id}`,
  )
}

export const getTrainTaskExecutionWorkers = (id: number) => {
  return req.get<never, { workers: string[] }>(
    `/api/v1/traintaskexecution/workers/${id}`,
  )
}

export interface TrainTaskExecutionCondition {
  status:
    | 'SUBMITTED'
    | 'PENDING'
    | 'RUNNING'
    | 'SUCCEEDED'
    | 'FAILED'
    | 'CANCELLED'
  timestamp: string
  operatorName: string
  operatorAccount: string
}

export const getTrainTaskExecutionConditions = (id: number) => {
  return req.get<never, { conditions: TrainTaskExecutionCondition[] }>(
    `/api/v1/traintaskexecution/conditions/${id}`,
  )
}

export const useTrainTaskExecutionDetail = (id: number) => {
  return useQuery({
    queryKey: ['TrainTaskExecutionDetail', id],
    queryFn: () => getTrainTaskExecutionDetail(id),
    enabled: !!id,
  })
}

export const useTrainTaskExecutionWorkers = (id: number) => {
  return useQuery({
    queryKey: ['TrainTaskExecutionWorkers', id],
    queryFn: () => getTrainTaskExecutionWorkers(id),
    enabled: !!id,
  })
}

export const useTrainTaskExecutionConditions = (id: number) => {
  return useQuery({
    queryKey: ['TrainTaskExecutionConditions', id],
    queryFn: () => getTrainTaskExecutionConditions(id),
    enabled: !!id,
  })
}

export const getTrainTaskDetail = (id: number) => {
  return req.get<never, { data: TrainTaskDetail }>(`/api/v1/traintask/${id}`)
}

export const deleteTrainTask = (id: number) => {
  return req.delete<never, never>(`/api/v1/traintask/delete/${id}`)
}

export const useConfigmapList = (params: {
  clusterName: string
  namespace: string
}) => {
  return useQuery({
    queryKey: ['ConfigmapList', params.clusterName, params.namespace],
    queryFn: () =>
      req.get<never, { configmapNames: string[] }>(
        '/api/v1/traintask/configmap/list',
        {
          params,
        },
      ),
    enabled: !!params.clusterName && !!params.namespace,
  })
}

export const usePvcList = (params: {
  clusterName: string
  namespace: string
}) => {
  return useQuery({
    queryKey: ['PvcList', params.clusterName, params.namespace],
    queryFn: () =>
      req.get<never, { pvcNames: string[] }>('/api/v1/traintask/pvc/list', {
        params,
      }),
    enabled: !!params.clusterName && !!params.namespace,
  })
}

export const useSecretList = (params: {
  clusterName: string
  namespace: string
}) => {
  return useQuery({
    queryKey: ['SecretList', params.clusterName, params.namespace],
    queryFn: () =>
      req.get<never, { secretNames: string[] }>(
        '/api/v1/traintask/secret/list',
        {
          params,
        },
      ),
    enabled: !!params.clusterName && !!params.namespace,
  })
}

export interface ClusterNsList {
  clusterNamespaces?: ClusterNamespace[]
}

/**
 * mlops.internal.model.dto.ClusterNamespace
 */
export interface ClusterNamespace {
  cluster?: string
  namespaces?: string[]
}

export const useClusterNsList = (params: { teamId: number }) => {
  return useQuery({
    queryKey: ['ClusterNsList', params.teamId],
    queryFn: () =>
      req.get<never, ClusterNsList>('/api/v1/team/cluster-namespace/list', {
        params,
      }),
  })
}
