import type { User } from '@/pages/MemberManagement/setting'
import type { FormValue } from '@/pages/MemberManagement/useMemberManagementService'
import { useQuery } from '@tanstack/react-query'
import type { CommonList, ListQuery } from './common'
import { req } from './req'

export type Role = 'admin' | 'user'
export const roleMap = {
  admin: '管理员',
  user: '工作台',
}
export const teamTypeMap = {
  feature: '特性',
  organization: '组织架构',
}

export interface UserProfile {
  id: number
  chineseName: string
  email: string
  employeeNo: string
  role: Role[]
  team: Team[]
  uid: number
  username: string
  createdAt: number
}

export interface Team {
  id: number
  teamId: number
  teamName: string
  teamType: string
  role: string
}

// export interface Team {
//   id: number
//   role: string
//   teamId: string
//   teamName: string
//   teamType: 'feature' | 'organization'
// }

const getUserProfile = () => {
  return req.get<never, UserProfile>('/api/v1/user/profile')
}

export const useUserProfile = () => {
  return useQuery({
    queryKey: ['userProfile'],
    queryFn: () => getUserProfile(),
  })
}

export type UserList = CommonList<UserProfile>

type UserListQuery = ListQuery & {
  search?: string
}

export const getUserList = (params: UserListQuery) => {
  return req.get<never, UserList>('/api/v1/user/list', { params })
}

export const useUserList = (params: UserListQuery) => {
  return useQuery({
    queryKey: ['userList', params.page, params.size, params.search],
    queryFn: () => getUserList(params),
  })
}

export const getTeams = (userId: number) => {
  return req.get<never, { teams: Team[] }>(`/api/v1/user/team/list/${userId}`)
}

export const getUserInfo = (userId: number) => {
  return req.get<never, UserProfile>(`/api/v1/user/profile/get/${userId}`)
}

export const setAdmin = (data: { userId: number; isAdmin: boolean }) => {
  return req.post<never, never>('/api/v1/user/set-admin', data)
}

export const setKnowledgebaseAdmin = (data: {
  userId: number
  isKnowledgebaseAdmin: boolean
}) => {
  return req.post<never, never>('/api/v1/user/set-knowledgebase-admin', data)
}

export const setConsoleAdmin = (data: {
  userId: number
  isConsoleAdmin: boolean
}) => {
  return req.post<never, never>('/api/v1/user/set-console-admin', data)
}

export const setTeamAuth = (data: {
  userId: number
  teamId: number
  teamRoleKind: 'admin' | 'user'
}) => {
  return req.post<never, never>('/api/v1/user/set-team-auth', data)
}

export const unsetTeamAuth = (data: {
  userId: number
  teamId: number
  teamRoleKind: 'admin' | 'user'
}) => {
  return req.post<never, never>('/api/v1/user/unset-team-auth', data)
}

export const syncTeam = () => {
  return req.post<never, never>('/api/v1/user/sync-team')
}

export const getTeamUserList = (teamId: number) => {
  return req.get<never, { users: User[] }>(`/api/v1/team/user/list/${teamId}`)
}

export const removeUserAuth = (data: {
  teamId: number
  teamRoleKind?: string
  userId: number
}) => {
  return req.post('/api/v1/user/unset-team-auth', data)
}

export const setUserAuth = (params: FormValue & { teamId: number }) => {
  return req.post<never, Team[]>('/api/v1/user/set-team-auth', {
    teamRoleKind: params.role,
    teamId: params.teamId,
    userId: params.userId,
  })
}

interface TeamApp {
  id: number
  name: string
  cmdbId: number
}
export const getTeamAppList = (teamId: number) => {
  return req.get<never, { apps: TeamApp[] }>('/api/v1/team/app/list', {
    params: { teamId, size: 99 },
  })
}

export const useAppList = (teamId: number) => {
  return useQuery({
    queryKey: ['appList', teamId],
    queryFn: () => getTeamAppList(teamId),
  })
}
