import { Radio } from 'antd'
import type React from 'react'
import { useCallback, useEffect, useState } from 'react'

interface CachedRadioGroupProps<T> {
  value: T // 当前选中的值
  onChange: (value: T) => void // 值变化时的回调
  cacheKey?: string // 用于缓存的唯一标记,如果不传或者为空，则使用defaultValue
  options: { label: string; value: T }[] // Radio 按钮选项
}

function CachedRadioGroup<T>(
  props: CachedRadioGroupProps<T>,
): React.ReactElement {
  const { value, onChange, cacheKey, options } = props

  const getValueFromCache: () => T = () => {
    if (!cacheKey) return value // 如果没有缓存键，直接返回默认值

    const cachedValue = localStorage.getItem(cacheKey)
    if (cachedValue === null) return value // 如果缓存为空，返回默认值

    switch (typeof value) {
      case 'boolean':
        return (cachedValue === 'true') as T // 将字符串转换为布尔值
      case 'number':
        return Number(cachedValue) as T // 将字符串转换为数字
      case 'string':
        return cachedValue as T // 直接返回字符串
      default:
        return value // 如果类型不匹配，返回默认值
    }
  }
  const defaultValue = getValueFromCache()
  const [selectedValue, setSelectedValue] = useState<T>(defaultValue)

  const handleChange = useCallback(
    (newValue: T) => {
      setSelectedValue(newValue) // 更新内部状态
      cacheKey &&
        localStorage.setItem(
          cacheKey,
          typeof newValue === 'string' ? newValue : JSON.stringify(newValue),
        ) // 缓存用户选择
      onChange?.(newValue) // 触发外部回调
    },
    [cacheKey, onChange],
  )

  useEffect(() => {
    if (defaultValue !== value) {
      handleChange(defaultValue) // 如果默认值变化，更新内部状态
    }
  }, [defaultValue, handleChange, value])

  return (
    <Radio.Group
      className="simple-radio-group"
      value={selectedValue}
      onChange={(e) => handleChange(e.target.value)}
    >
      {options.map((option) => (
        <Radio.Button key={Math.random()} value={option.value}>
          {option.label}
        </Radio.Button>
      ))}
    </Radio.Group>
  )
}

export default CachedRadioGroup
