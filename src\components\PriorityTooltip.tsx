import { TASK_PRIORITY_OPTIONS, type TaskPriority } from '@/constants/taskPriority'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import type React from 'react'

interface PriorityTooltipProps {
    priority?: TaskPriority
    placement?: 'topLeft' | 'top' | 'topRight' | 'left' | 'right' | 'bottomLeft' | 'bottom' | 'bottomRight'
    showIcon?: boolean
    children?: React.ReactNode
}

export const PriorityTooltip: React.FC<PriorityTooltipProps> = ({
    priority,
    placement = 'topLeft',
    showIcon = false,
    children,
}) => {
    const priorityOption = priority
        ? TASK_PRIORITY_OPTIONS.find((option) => option.value === priority)
        : null

    const tooltipContent = (
        <div className="text-[#666666]">
            {priorityOption ? (
                <div className="flex items-center">
                    <div
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: priorityOption.color }}
                    />
                    <span className="font-medium">{priorityOption.label}</span>
                    <span className="ml-2">（{priorityOption.description}）</span>
                </div>
            ) : (
                TASK_PRIORITY_OPTIONS.map((option) => (
                    <div key={option.value} className="flex items-center mb-1">
                        <div
                            className="w-2 h-2 rounded-full mr-2"
                            style={{ backgroundColor: option.color }}
                        />
                        <span className="font-medium">{option.label}</span>
                        <span className="ml-2">（{option.description}）</span>
                    </div>
                ))
            )}
        </div>
    )

    return (
        <Tooltip
            placement={placement}
            color="white"
            arrow={{
                pointAtCenter: true,
            }}
            overlayStyle={{ maxWidth: 300 }}
            title={tooltipContent}
        >
            <div className="flex items-center">
                {children}
                {showIcon && <QuestionCircleOutlined className="ml-2" />}
            </div>
        </Tooltip>
    )
}

export default PriorityTooltip