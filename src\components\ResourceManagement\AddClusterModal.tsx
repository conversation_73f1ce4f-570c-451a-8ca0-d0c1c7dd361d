import { useClusterList, useNamespaceList } from '@/api/resource'
import { useCreateClusterNamespace } from '@/api/team'
import { SearchOutlined } from '@ant-design/icons'
import { Alert, Button, Input, Modal, Radio, Space, Spin, message } from 'antd'
import type React from 'react'
import { useMemo, useState, useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'

interface AddClusterModalProps {
  open: boolean
  onCancel: () => void
  onConfirm: (data: { cluster: string; namespaces: string[] }) => void
  teamId: number
  defaultCluster?: string
}

interface ClusterOption {
  value: string
  label: string
}

interface NamespaceOption {
  value: string
  label: string
  description?: string
}

export const AddClusterModal: React.FC<AddClusterModalProps> = ({
  open,
  onCancel,
  onConfirm,
  teamId,
  defaultCluster,
}) => {
  const [environment, setEnvironment] = useState<string>('production')
  const [businessUnit, setBusinessUnit] = useState<string>('')
  const [selectedCluster, setSelectedCluster] = useState<string>('')
  const [selectedNamespaces, setSelectedNamespaces] = useState<string[]>([])
  const [clusterSearchText, setClusterSearchText] = useState<string>('')
  const [namespaceSearchText, setNamespaceSearchText] = useState<string>('')

  const queryClient = useQueryClient()

  // API calls
  const { data: clusterData, isLoading: clusterLoading } = useClusterList()
  const { data: namespaceData, isLoading: namespaceLoading } =
    useNamespaceList(selectedCluster)

  // Mutation for creating cluster namespace
  const createClusterNamespaceMutation = useCreateClusterNamespace()

  // Transform API data to options
  const clusterOptions = useMemo<ClusterOption[]>(() => {
    if (!clusterData?.clusterList) return []
    return clusterData.clusterList.map((cluster) => ({
      value: cluster,
      label: cluster,
    }))
  }, [clusterData])

  const namespaceOptions = useMemo<NamespaceOption[]>(() => {
    if (!namespaceData?.namespaceList) return []
    return namespaceData.namespaceList.map((namespace) => ({
      value: namespace,
      label: namespace,
      description: namespace === 'default' ? '默认' : undefined,
    }))
  }, [namespaceData])

  // Filter clusters based on search text
  const filteredClusters = clusterOptions.filter((cluster) =>
    cluster.label.toLowerCase().includes(clusterSearchText.toLowerCase()),
  )

  // Filter namespaces based on search text
  const filteredNamespaces = namespaceOptions.filter((namespace) =>
    namespace.label.toLowerCase().includes(namespaceSearchText.toLowerCase()),
  )

  const handleConfirm = async () => {
    if (selectedCluster && selectedNamespaces.length > 0) {
      try {
        await createClusterNamespaceMutation.mutateAsync({
          teamId,
          clusterNamespaces: {
            cluster: selectedCluster,
            namespaces: selectedNamespaces,
          },
        })
        
        message.success('集群命名空间添加成功')
        queryClient.invalidateQueries({ queryKey: ['ClusterNamespaceList', teamId] })
        onConfirm({
          cluster: selectedCluster,
          namespaces: selectedNamespaces,
        })
        handleReset()
      } catch (error: any) {
      }
    }
  }

  const handleCancel = () => {
    onCancel()
    handleReset()
  }

  // Set default cluster when modal opens
  useEffect(() => {
    if (open && defaultCluster) {
      setSelectedCluster(defaultCluster)
    } else if (open && !defaultCluster) {
      // Reset form when opening without default
      setSelectedCluster('')
      setSelectedNamespaces([])
    }
  }, [open, defaultCluster])

  const handleReset = () => {
    setEnvironment('production')
    setBusinessUnit('')
    setSelectedCluster('')
    setSelectedNamespaces([])
    setClusterSearchText('')
    setNamespaceSearchText('')
  }

  const handleClusterSelect = (cluster: string) => {
    setSelectedCluster(cluster)
    setSelectedNamespaces([]) // Reset namespace selection when cluster changes
  }

  const handleNamespaceSelect = (namespace: string) => {
    setSelectedNamespaces([namespace]) // Single selection for now
  }

  return (
    <Modal
      title="添加集群与命名空间"
      open={open}
      onCancel={handleCancel}
      width={800}
      footer={
        <div className="flex justify-end gap-2">
          <Button onClick={handleCancel}>取消</Button>
          <Button
            type="primary"
            onClick={handleConfirm}
            disabled={!selectedCluster || selectedNamespaces.length === 0}
          >
            确定
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Alert */}
        <Alert
          message="为了避免资源碎片化，每个团队只允许绑定一个集群。若无数据，请先申请权限。"
          type="info"
          showIcon
        />

        {/* Cluster and Namespace Selection */}
        <div className="grid grid-cols-2 gap-4">
          {/* Cluster Selection */}
          <div>
            <div className="mb-3">
              <Input
                placeholder="请输入关键词"
                prefix={<SearchOutlined />}
                value={clusterSearchText}
                onChange={(e) => setClusterSearchText(e.target.value)}
              />
            </div>
            <div className="border rounded-md p-3 h-64 overflow-y-auto">
              {clusterLoading ? (
                <div className="flex justify-center items-center h-full">
                  <Spin size="large" />
                </div>
              ) : (
                <Radio.Group
                  value={selectedCluster}
                  onChange={(e) => handleClusterSelect(e.target.value)}
                  className="w-full"
                >
                  <Space direction="vertical" className="w-full">
                    {filteredClusters.map((cluster) => (
                      <Radio
                        key={cluster.value}
                        value={cluster.value}
                        className="w-full"
                      >
                        <span className="text-sm">{cluster.label}</span>
                      </Radio>
                    ))}
                  </Space>
                </Radio.Group>
              )}
            </div>
          </div>

          {/* Namespace Selection */}
          <div>
            <div className="mb-3">
              <Input
                placeholder="请输入命名空间"
                prefix={<SearchOutlined />}
                value={namespaceSearchText}
                onChange={(e) => setNamespaceSearchText(e.target.value)}
              />
            </div>
            <div className="border rounded-md p-3 h-64 overflow-y-auto">
              <Radio.Group
                value={selectedNamespaces[0]}
                onChange={(e) => handleNamespaceSelect(e.target.value)}
                className="w-full"
                disabled={!selectedCluster}
              >
                <Space direction="vertical" className="w-full">
                  {filteredNamespaces.map((namespace) => (
                    <Radio
                      key={namespace.value}
                      value={namespace.value}
                      className="w-full"
                    >
                      <span className="text-sm">
                        {namespace.label}
                        {namespace.description && (
                          <span className="text-gray-500 ml-2">
                            {namespace.description}
                          </span>
                        )}
                      </span>
                    </Radio>
                  ))}
                </Space>
              </Radio.Group>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default AddClusterModal
