import { useGpuList } from '@/api/resource'
import type { MlopsInternalModelDtoGpuQuota } from '@/api/team'
import { Button, InputNumber, Modal, Select } from 'antd'
import type React from 'react'
import { useEffect, useMemo, useState } from 'react'

interface AddGpuQuotaModalProps {
  open: boolean
  onCancel: () => void
  onConfirm: (
    data: Required<MlopsInternalModelDtoGpuQuota>,
  ) => Promise<void> | void
  teamId: number
  initialData?: MlopsInternalModelDtoGpuQuota | null
}

interface GpuOption {
  value: string
  label: string
  gpuCore?: string
  gpuMemory?: string
  gpuType?: string
  gpuAlias?: string
}

export const AddGpuQuotaModal: React.FC<AddGpuQuotaModalProps> = ({
  open,
  onCancel,
  onConfirm,
  teamId,
  initialData,
}) => {
  const [selectedGpuType, setSelectedGpuType] = useState<string | undefined>(
    undefined,
  )
  const [quantity, setQuantity] = useState<number>(1)
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)

  // API calls
  const { data: gpuData, isLoading: gpuLoading } = useGpuList()

  // Set initial data when editing
  useEffect(() => {
    if (open && initialData) {
      setSelectedGpuType(initialData.gpuType || undefined)
      setQuantity(initialData.nums || 1)
    } else if (open && !initialData) {
      // Reset form when opening for new creation
      setSelectedGpuType(undefined)
      setQuantity(1)
    }
  }, [open, initialData])

  // Transform API data to options
  const gpuOptions = useMemo<GpuOption[]>(() => {
    if (!gpuData?.gpuList) return []
    return gpuData.gpuList.map((gpu) => ({
      value: gpu.gpuType || '',
      label: gpu.gpuAlias || gpu.gpuType || '',
      gpuCore: gpu.gpuCore,
      gpuMemory: gpu.gpuMemory,
      gpuType: gpu.gpuType,
      gpuAlias: gpu.gpuAlias,
    }))
  }, [gpuData])

  // Get selected GPU details
  const selectedGpu = useMemo(() => {
    return gpuOptions.find((gpu) => gpu.value === selectedGpuType)
  }, [gpuOptions, selectedGpuType])

  const handleConfirm = async () => {
    if (selectedGpu && quantity > 0 && !confirmLoading) {
      try {
        setConfirmLoading(true)
        await onConfirm({
          gpuType: selectedGpu.gpuType || '',
          gpuCore: selectedGpu.gpuCore || '',
          gpuMemory: selectedGpu.gpuMemory || '',
          gpuAlias: selectedGpu.gpuAlias || '',
          nums: quantity,
        })
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        setConfirmLoading(false)
      }
    }
  }

  const handleCancel = () => {
    if (!confirmLoading) {
      onCancel()
      handleReset()
    }
  }

  const handleReset = () => {
    setSelectedGpuType('')
    setQuantity(1)
    setConfirmLoading(false)
  }

  return (
    <Modal
      title={initialData ? '编辑显卡类型与数量' : '添加显卡类型与数量'}
      open={open}
      onCancel={handleCancel}
      width={600}
      footer={
        <div className="flex justify-end gap-2">
          <Button onClick={handleCancel} disabled={confirmLoading}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={handleConfirm}
            loading={confirmLoading}
            disabled={!selectedGpuType || quantity <= 0 || confirmLoading}
          >
            确定
          </Button>
        </div>
      }
    >
      <div className="space-y-6 py-4">
        {/* GPU Type Selection */}
        <div>
          <div className="flex items-center mb-3">
            <span className="text-red-500 mr-1">*</span>
            <label htmlFor="gpu-type-select" className="text-sm font-medium">
              类型：
            </label>
          </div>
          <Select
            id="gpu-type-select"
            value={selectedGpuType}
            onChange={setSelectedGpuType}
            placeholder="请选择GPU类型"
            className="w-full"
            loading={gpuLoading}
            disabled={confirmLoading}
            options={gpuOptions.map((gpu) => ({
              value: gpu.value,
              label: gpu.label,
            }))}
          />
        </div>

        {/* GPU Details Display */}
        {selectedGpu && (
          <div className="bg-gray-50 p-4 rounded-md space-y-3">
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-600 w-20">
                型号：
              </span>
              <span className="text-sm">{selectedGpu.gpuType}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-600 w-20">
                计算资源：
              </span>
              <span className="text-sm">{selectedGpu.gpuCore} vCPU</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-600 w-20">
                内存：
              </span>
              <span className="text-sm">{selectedGpu.gpuMemory} GiB</span>
            </div>
          </div>
        )}

        {/* Quantity Selection */}
        <div>
          <div className="flex items-center mb-3">
            <span className="text-red-500 mr-1">*</span>
            <label htmlFor="quantity-input" className="text-sm font-medium">
              数量：
            </label>
          </div>
          <InputNumber
            id="quantity-input"
            value={quantity}
            onChange={(value) => setQuantity(value || 1)}
            min={1}
            step={1}
            precision={0}
            className="w-full"
            placeholder="请输入数量"
            addonAfter="卡"
            disabled={confirmLoading}
          />
        </div>
      </div>
    </Modal>
  )
}

export default AddGpuQuotaModal
