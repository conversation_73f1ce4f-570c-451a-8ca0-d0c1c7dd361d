import { TASK_PRIORITY_OPTIONS } from '@/constants/taskPriority'
import { InfoCircleOutlined } from '@ant-design/icons'
import { Alert, Button, Modal, Radio, Space } from 'antd'
import { useEffect, useState } from 'react'

interface TaskAdjustDialogProps {
  open: boolean
  onCancel: () => void
  onConfirm: (priority: string) => void
  currentPriority?: string
  loading?: boolean
}

const TaskAdjustDialog: React.FC<TaskAdjustDialogProps> = ({
  open,
  onCancel,
  onConfirm,
  currentPriority = 'P1',
  loading = false,
}) => {
  const [selectedPriority, setSelectedPriority] = useState(currentPriority)
  useEffect(() => {
    setSelectedPriority(currentPriority)
  }, [currentPriority])
  const handleConfirm = () => {
    onConfirm(selectedPriority)
  }

  const handleCancel = () => {
    setSelectedPriority(currentPriority) // 重置为当前优先级
    onCancel()
  }

  return (
    <Modal
      title="调整任务"
      open={open}
      onCancel={handleCancel}
      width={600}
      footer={
        <div className="flex justify-end gap-3">
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleConfirm} loading={loading}>
            确定
          </Button>
        </div>
      }
    >
      <div className="pb-4">
        {/* 温馨提示 */}
        <Alert
          message="温馨提示：若任务排队时间过长，可提高任务优先级重新排队。"
          type="info"
          icon={<InfoCircleOutlined />}
          showIcon
          className="mb-6"
          style={{
            backgroundColor: '#D6D3FA',
          }}
        />

        {/* 优先级选择 */}
        <div className="mb-4 flex gap-3">
          <div className="mb-3 font-medium text-gray-700">
            优先级 <span className="text-red-500">*</span>
          </div>
          <Radio.Group
            value={selectedPriority}
            onChange={(e) => setSelectedPriority(e.target.value)}
            className="flex flex-col gap-2"
          >
            {TASK_PRIORITY_OPTIONS.map((option) => (
              <Radio
                key={option.value}
                value={option.value}
                className="flex items-center"
              >
                <span className="flex items-center">
                  <span
                    className="inline-block w-2 h-2 rounded-full mr-2"
                    style={{ backgroundColor: option.color }}
                  />
                  {option.label}{' '}
                  <span className="text-gray-500 ml-1">
                    ({option.description})
                  </span>
                </span>
              </Radio>
            ))}
          </Radio.Group>
        </div>
      </div>
    </Modal>
  )
}

export default TaskAdjustDialog
