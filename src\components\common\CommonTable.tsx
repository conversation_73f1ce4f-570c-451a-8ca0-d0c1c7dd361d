import { Button, Space, Table, Tag } from "antd";
import type { TableProps } from "antd";
import { createStyles } from "antd-style";
import type { AnyObject } from "antd/es/_util/type";

export const CommonTable = <T extends AnyObject>({
  pagination,
  className,
  ...props
}: TableProps<T>) => {
  // const useStyle = createStyles(({ css }) => ({
  //   customTable: css`
  //       .ant-table {
  //         .ant-table-container {
  //           .ant-table-body,
  //           .ant-table-content {
  //             scrollbar-width: thin;
  //             scrollbar-color: #eaeaea transparent;
  //             scrollbar-gutter: stable;
  //           }
  //         }
  //       }
  //     `,
  // }))
  // const { styles } = useStyle()
  // ${styles.customTable}
  // scroll={{ y: 'calc(100vh - 420px)' }}

  return (
    <Table<T>
      rowKey="id"
      {...props}
      className={`${className}`}
      pagination={
        pagination
          ? {
              position: ["bottomRight"],
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条`,
              ...pagination,
            }
          : false
      }
    />
  );
};

export default CommonTable;
