import { QuestionCircleOutlined } from '@ant-design/icons'
import { Tooltip, type TooltipProps } from 'antd'
import type React from 'react'

interface InfoTooltipProps {
  title: React.ReactNode
  children?: React.ReactNode
  placement?: TooltipProps['placement']
  iconPosition?: 'left' | 'right'
  iconClassName?: string
  maxWidth?: number
}

/**
 * 信息提示组件，用于显示带有问号图标的提示信息
 * @param title 提示内容
 * @param children 子元素
 * @param placement 提示框位置
 * @param iconPosition 图标位置，默认为左侧
 * @param iconClassName 图标样式类名
 * @param maxWidth 提示框最大宽度
 */
const InfoTooltip: React.FC<InfoTooltipProps> = ({
  title,
  children,
  placement = 'topLeft',
  iconPosition = 'left',
  iconClassName = 'mr-2',
  maxWidth = 300,
}) => {
  const tooltipContent = (
    <div className="text-[#666666]">
      {title}
    </div>
  )

  // 只有图标触发提示，文字不触发
  return (
    <div className="flex items-center">
      {iconPosition === 'left' && (
        <Tooltip
          placement={placement}
          color="white"
          arrow={{
            pointAtCenter: true,
          }}
          overlayStyle={{ maxWidth }}
          title={tooltipContent}
        >
          <QuestionCircleOutlined className={iconClassName} />
        </Tooltip>
      )}
      {children}
      {iconPosition === 'right' && (
        <Tooltip
          placement={placement}
          color="white"
          arrow={{
            pointAtCenter: true,
          }}
          overlayStyle={{ maxWidth }}
          title={tooltipContent}
        >
          <QuestionCircleOutlined className={iconClassName} />
        </Tooltip>
      )}
    </div>
  )
}

export default InfoTooltip