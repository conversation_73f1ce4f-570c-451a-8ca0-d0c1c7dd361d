import PriorityTooltip from '@/components/PriorityTooltip'
import {
  TASK_PRIORITY_OPTIONS,
  type TaskPriority,
} from '@/constants/taskPriority'
import type React from 'react'

interface PriorityDisplayProps {
  priority?: TaskPriority | null
  placement?:
    | 'top'
    | 'left'
    | 'right'
    | 'bottom'
    | 'topLeft'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomRight'
  className?: string
}

export const PriorityDisplay: React.FC<PriorityDisplayProps> = ({
  priority,
  placement = 'right',
  className = '',
}) => {
  if (!priority) return <span>-</span>

  const priorityOption = TASK_PRIORITY_OPTIONS.find(
    (option) => option.value === priority,
  )

  if (!priorityOption) return <span>-</span>

  return (
    <PriorityTooltip placement={placement}>
      <div
        className={`flex items-center flex-1 justify-center cursor-help ${className}`}
      >
        <div
          className="w-2 h-2 rounded-full mr-1"
          style={{ backgroundColor: priorityOption.color }}
        />
        <span>{priorityOption.label}</span>
      </div>
    </PriorityTooltip>
  )
}

export default PriorityDisplay
