import InfoTooltip from '@/components/common/InfoTooltip'
import { cn } from '@/lib/utils'
import type React from 'react'
interface SectionTitleProps {
  title: string | React.ReactNode
  className?: string
  desc?: string | React.ReactNode
}

const SectionTitle: React.FC<SectionTitleProps> = ({
  title,
  className = '',
  desc,
}) => {
  return (
    <div className={cn('flex items-center mb-4', className)}>
      <div
        className={`text-[14px] border-l-[6px] pl-3 border-[#7367EF] ${className}`}
      >
        {title}
      </div>
      {desc && (
        <InfoTooltip
          title={desc}
          placement="right"
          iconPosition="right"
          iconClassName="ml-2"
        />
      )}
    </div>
  )
}

export default SectionTitle
