import { Tooltip } from 'antd'
import type React from 'react'
import { useEffect, useMemo, useRef, useState } from 'react'

interface TruncatedTagsProps {
  items: Array<{
    key: string
    label: string
  }>
  maxVisible?: number
  tagClassName?: string
  autoResize?: boolean // 新增：是否启用自动调整
}

export const TruncatedTags: React.FC<TruncatedTagsProps> = ({
  items,
  maxVisible = 2,
  tagClassName = 'inline-block text-xs text-black bg-[#DBD6FA] rounded-lg px-2 py-0 mr-2 mb-2',
  autoResize = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [calculatedMaxVisible, setCalculatedMaxVisible] = useState(maxVisible)

  // 计算容器宽度能容纳多少个标签
  useEffect(() => {
    if (!autoResize || items.length === 0) return

    const calculateVisibleItems = () => {
      const container = containerRef.current
      if (!container) return

      const containerWidth = container.offsetWidth
      if (containerWidth === 0) return

      // 创建临时元素来测量标签宽度
      const tempContainer = document.createElement('div')
      tempContainer.style.position = 'absolute'
      tempContainer.style.visibility = 'hidden'
      tempContainer.style.whiteSpace = 'nowrap'
      document.body.appendChild(tempContainer)

      let totalWidth = 0
      let visibleCount = 0
      const moreTagWidth = 60 // "+N" 标签的预估宽度

      for (let i = 0; i < items.length; i++) {
        const tempTag = document.createElement('span')
        tempTag.className = tagClassName
        tempTag.textContent = items[i].label
        tempContainer.appendChild(tempTag)

        const tagWidth = tempTag.offsetWidth + 15

        // 如果不是最后一个标签，需要考虑 "+N" 标签的宽度
        const needMoreTag = i < items.length - 1
        const requiredWidth =
          totalWidth + tagWidth + (needMoreTag ? moreTagWidth : 0)

        if (requiredWidth <= containerWidth) {
          totalWidth += tagWidth
          visibleCount++
        } else {
          break
        }
      }

      document.body.removeChild(tempContainer)

      // 至少显示一个标签
      setCalculatedMaxVisible(Math.max(1, visibleCount))
    }

    // 初始计算
    calculateVisibleItems()

    // 监听容器大小变化
    const resizeObserver = new ResizeObserver(calculateVisibleItems)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [items, tagClassName, autoResize])

  const effectiveMaxVisible = autoResize ? calculatedMaxVisible : maxVisible

  const { visibleItems, hiddenItems } = useMemo(() => {
    if (items.length <= effectiveMaxVisible) {
      return { visibleItems: items, hiddenItems: [] }
    }
    return {
      visibleItems: items.slice(0, effectiveMaxVisible),
      hiddenItems: items.slice(effectiveMaxVisible),
    }
  }, [items, effectiveMaxVisible])
  console.log(visibleItems)
  if (items.length === 0) return <span>-</span>

  return (
    <div ref={containerRef} className="space-y-1">
      {visibleItems.map((item) => (
        <span key={item.key} className={tagClassName}>
          {item.label}
        </span>
      ))}
      {hiddenItems.length > 0 && (
        <Tooltip
          title={
            <div className="flex flex-col items-start">
              {hiddenItems.map((item) => (
                <span key={item.key} className={tagClassName}>
                  {item.label}
                </span>
              ))}
            </div>
          }
          color="#ffffff"
        >
          <span className={tagClassName}>+{hiddenItems.length}</span>
        </Tooltip>
      )}
    </div>
  )
}

export default TruncatedTags
