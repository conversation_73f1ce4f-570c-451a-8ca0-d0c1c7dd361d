import { yaml } from '@codemirror/lang-yaml'
import { type Diagnostic, linter } from '@codemirror/lint'
import { vscodeDark } from '@uiw/codemirror-theme-vscode'
import CodeMirror, { type Extension } from '@uiw/react-codemirror'
import { forwardRef, useImperativeHandle, useRef } from 'react'
import * as YAML from 'yaml'

export interface YamlEditorRef {
  getValue: () => string
  setValue: (value: string) => void
  focus: () => void
  getSelection: () => string
}

export interface YamlEditorProps {
  /** YAML内容 */
  value?: string
  /** 默认值 */
  defaultValue?: string
  /** 占位符文本 */
  placeholder?: string
  /** 是否可编辑 */
  editable?: boolean
  /** 是否只读 */
  readOnly?: boolean
  /** 编辑器高度 */
  height?: string
  /** 编辑器宽度 */
  width?: string
  /** 主题 */
  theme?: 'light' | 'dark' | any
  /** 是否启用语法检查 */
  enableLinting?: boolean
  /** 自定义扩展 */
  extensions?: Extension[]
  /** 值变化回调 */
  onChange?: (value: string) => void
  /** 焦点事件 */
  onFocus?: () => void
  /** 失焦事件 */
  onBlur?: () => void
  /** 自定义类名 */
  className?: string
}

/**
 * YAML语法检查器
 */
function createYamlLinter() {
  return linter(
    (view) => {
      const diagnostics: Diagnostic[] = []
      const content = view.state.doc.toString()

      if (!content.trim()) {
        return diagnostics
      }

      try {
        YAML.parse(content)
      } catch (e: any) {
        // YAML 库的错误对象结构
        if (e.pos && Array.isArray(e.pos) && e.pos.length >= 2) {
          // e.pos 是 [start, end] 格式
          const [start, end] = e.pos
          diagnostics.push({
            from: start,
            to: end,
            message: e.message || 'YAML语法错误',
            severity: 'error',
          })
        } else if (
          e.linePos &&
          Array.isArray(e.linePos) &&
          e.linePos.length >= 1
        ) {
          // e.linePos 是 [LinePos, LinePos?] 格式，其中 LinePos = { line: number, col: number }
          const linePos = e.linePos[0]
          if (
            linePos &&
            typeof linePos.line === 'number' &&
            typeof linePos.col === 'number'
          ) {
            try {
              const lineInfo = view.state.doc.line(linePos.line)
              const from = lineInfo.from + linePos.col - 1 // col 是 1-based，需要转换为 0-based
              const to = Math.min(from + 1, lineInfo.to)

              diagnostics.push({
                from: Math.max(0, from),
                to: Math.max(from + 1, to),
                message: e.message || 'YAML语法错误',
                severity: 'error',
              })
            } catch (lineError) {
              // 如果行号超出范围，回退到简单错误处理
              diagnostics.push({
                from: 0,
                to: Math.min(content.length, 1),
                message: e.message || 'YAML语法错误',
                severity: 'error',
              })
            }
          } else {
            diagnostics.push({
              from: 0,
              to: Math.min(content.length, 1),
              message: e.message || 'YAML语法错误',
              severity: 'error',
            })
          }
        } else {
          // 回退到简单错误处理
          diagnostics.push({
            from: 0,
            to: Math.min(content.length, 1),
            message: e.message || 'YAML语法错误',
            severity: 'error',
          })
        }
      }
      return diagnostics
    },
    {
      delay: 100,
    },
  )
}

/**
 * 通用YAML编辑器组件
 */
const YamlEditor = forwardRef<YamlEditorRef, YamlEditorProps>(
  (
    {
      value,
      defaultValue = '',
      placeholder = '请输入YAML内容...',
      editable = true,
      readOnly = false,
      height = '400px',
      width = '100%',
      theme = vscodeDark,
      enableLinting = true,
      extensions = [],
      onChange,
      onFocus,
      onBlur,
      className = '',
    },
    ref,
  ) => {
    const editorRef = useRef<any>(null)

    // 构建扩展数组
    const editorExtensions = [
      yaml(),
      ...(enableLinting ? [createYamlLinter()] : []),
      ...extensions,
    ]

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      getValue: () => {
        return editorRef.current?.view?.state?.doc?.toString() || ''
      },
      setValue: (newValue: string) => {
        if (editorRef.current?.view) {
          const transaction = editorRef.current.view.state.update({
            changes: {
              from: 0,
              to: editorRef.current.view.state.doc.length,
              insert: newValue,
            },
          })
          editorRef.current.view.dispatch(transaction)
        }
      },
      focus: () => {
        editorRef.current?.view?.focus()
      },
      getSelection: () => {
        const view = editorRef.current?.view
        if (view) {
          const { from, to } = view.state.selection.main
          return view.state.doc.sliceString(from, to)
        }
        return ''
      },
    }))

    return (
      <div className={className}>
        <CodeMirror
          ref={editorRef}
          value={value}
          defaultValue={defaultValue}
          placeholder={placeholder}
          height={height}
          width={width}
          theme={theme}
          extensions={editorExtensions}
          editable={editable && !readOnly}
          readOnly={readOnly}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
        />
      </div>
    )
  },
)

YamlEditor.displayName = 'YamlEditor'

export default YamlEditor
