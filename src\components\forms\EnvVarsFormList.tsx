import SectionTitle from '@/components/common/SectionTitle'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Form } from 'antd'
import InfoTooltip from '../common/InfoTooltip'
import { WrappedInput } from './wrapped'

export interface EnvVar {
  key?: string
  value?: string
}

interface EnvVarsFormListProps {
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义类名 */
  className?: string
  /** 是否必填，默认为 true */
  required?: boolean
}

const EnvVarsFormList: React.FC<EnvVarsFormListProps> = ({
  disabled = false,
  className = '',
}) => {
  const form = Form.useFormInstance()

  const handleAdd = () => {
    const envVars = form.getFieldValue('envVars') || []
    form.setFieldValue('envVars', [
      ...envVars,
      {
        key: '',
        value: '',
      },
    ])
  }

  return (
    <div className={`mb-4 ${className}`}>
      <Form.Item
        label={
          <InfoTooltip title="环境变量将注入到容器实例，环境变量 key 值仅支持大小写字母、数字、下划线，且不能以数字作为开头">
            环境变量
          </InfoTooltip>
        }
      >
        <Form.List name="envVars">
          {(fields, { remove }) => (
            <div>
              {fields.map(({ key, name: fieldName, ...restField }) => {
                return (
                  <div key={key} className="flex">
                    <Form.Item
                      {...restField}
                      name={[fieldName, 'key']}
                      rules={[
                        { required: true, message: '请输入 Key' },
                        {
                          pattern: /^[A-Za-z_][A-Za-z0-9_]*$/,
                          message:
                            'Key 只能包含字母、数字、下划线，且必须以字母或下划线开头',
                        },
                      ]}
                      className="w-[203px]"
                    >
                      <WrappedInput
                        placeholder="KEY（参考 MLP_HOST）"
                        disabled={disabled}
                        displayOnly={disabled}
                      />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[fieldName, 'value']}
                      rules={[{ required: true, message: '请输入 Value' }]}
                      className="w-[203px] ml-2"
                    >
                      <WrappedInput 
                        placeholder="VALUE" 
                        disabled={disabled}
                        displayOnly={disabled}
                      />
                    </Form.Item>

                    <Button
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      disabled={disabled}
                      onClick={() => remove(fieldName)}
                    />
                  </div>
                )
              })}
            </div>
          )}
        </Form.List>
        <Button
          type="link"
          className="px-0"
          icon={<PlusOutlined />}
          disabled={disabled}
          onClick={handleAdd}
        >
          添加环境变量
        </Button>
      </Form.Item>
    </div>
  )
}

export default EnvVarsFormList
