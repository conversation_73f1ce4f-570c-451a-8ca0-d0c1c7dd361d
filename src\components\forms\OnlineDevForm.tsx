import { type OnlineDevDetail, useOnlineDevImageList } from '@/api/onlinedev'
import { useClusterNsList } from '@/api/traintask'
import { useAppList } from '@/api/user'
import SectionTitle from '@/components/common/SectionTitle'
import useUserStore from '@/stores/user'
import type { ModalType } from '@/types/common'
import { convertToOnlineDevDetail } from '@/utils/dataConvert'
import {
  Button,
  Cascader,
  Form,
  type FormInstance,
  Input,
  Radio,
  Select,
} from 'antd'
import TextArea from 'antd/es/input/TextArea'
import { forwardRef, useImperativeHandle } from 'react'
import InfoTooltip from '../common/InfoTooltip'
import EnvVarsFormList from './EnvVarsFormList'
import ResourceConfigForm from './ResourceConfigForm'
import VolumeMountsFormList from './VolumeMountsFormList'

export interface OnlineDevFormData {
  devName?: string
  namespace?: string
  clusterNs: string[]
  type?: 'jupyter' | 'code-server'
  appName?: string
  cmdbId?: number
  imageUrl?: string
  startCmd?: string
  cpu?: {
    requests?: number
    limits?: number
  }
  memory?: {
    requests?: number
    limits?: number
  }
  gpuCore?: string
  gpuMemory?: number
  gpuType?: string
  maxReplicas?: number
  volumeMounts?: Array<{
    volumeType?: 'pvc' | 'configmap' | 'secret'
    name?: string
    volumeName?: string
    mountPath?: string
    subPath?: string
  }>
  envVars?: Array<{
    key?: string
    value?: string
  }>
}

export interface TaskFormRef {
  form: FormInstance
  submit: () => void
  reset: () => void
  getValues: () => OnlineDevFormData
  setValues: (values: OnlineDevFormData) => void
}

interface TaskFormProps {
  initialValues?: OnlineDevFormData
  onFinish?: (values: OnlineDevDetail) => void
  onFinishFailed?: (errorInfo: any) => void
  disabled?: boolean
  className?: string
  type?: ModalType
}

const TaskForm = forwardRef<TaskFormRef, TaskFormProps>(
  (
    {
      initialValues,
      onFinish,
      onFinishFailed,
      disabled = false,
      className = '',
      type,
    },
    ref,
  ) => {
    const currentTeam = useUserStore((state) => state.currentTeam)
    const [form] = Form.useForm<OnlineDevFormData>()
    const clusterNs = Form.useWatch('clusterNs', form)
    const devType = Form.useWatch('type', form)
    const { data: clusterNsList } = useClusterNsList({
      teamId: currentTeam,
    })
    const { data: appList } = useAppList(currentTeam)

    const options = clusterNsList?.clusterNamespaces?.map((item) => ({
      value: item.cluster,
      label: item.cluster,
      children: item.namespaces?.map((ns) => ({
        value: ns,
        label: ns,
      })),
    }))

    const appOptions = appList?.apps?.map((app) => ({
      value: app.cmdbId,
      label: app.name,
    }))

    const { data: imageList } = useOnlineDevImageList(devType)
    const defaultInitialValues: OnlineDevFormData = {
      type: 'jupyter',
      volumeMounts: [{ volumeType: 'pvc' }],
      clusterNs: [],
      ...initialValues,
    }

    const handleFinish = (values: OnlineDevFormData) => {
      const OnlineDevDetail = convertToOnlineDevDetail(values, currentTeam, 0)
      console.log('转换后的数据:', OnlineDevDetail)

      // 调用原始的 onFinish，但传入转换后的数据
      onFinish?.(OnlineDevDetail)
    }

    const handleFinishFailed = (errorInfo: any) => {
      onFinishFailed?.(errorInfo)
    }

    useImperativeHandle(ref, () => ({
      form,
      submit: () => form.submit(),
      reset: () => form.resetFields(),
      getValues: () => form.getFieldsValue(),
      setValues: (values: OnlineDevFormData) => form.setFieldsValue(values),
    }))

    return (
      <Form
        form={form}
        layout="horizontal"
        onFinish={handleFinish}
        onFinishFailed={handleFinishFailed}
        initialValues={defaultInitialValues}
        disabled={disabled}
        className={className}
        labelCol={{ flex: '140px' }}
      >
        {/* 基础配置 */}
        <div className="mb-4">
          <SectionTitle title="基础配置" />

          <div className="grid grid-cols-2 gap-x-4 bg-[#FAFAFA] p-4 pb-0 rounded-md">
            <Form.Item label="名称" name="devName" rules={[{ required: true }]}>
              <Input placeholder="请输入开发机名称" maxLength={30} />
            </Form.Item>

            <Form.Item
              label={
                <InfoTooltip
                  maxWidth={600}
                  title={
                    <div>
                      找不到资源? 请联系团队管理员至
                      <Button
                        type="link"
                        size="small"
                        onClick={() =>
                          window.open(
                            '/team/resource-management?team=' + currentTeam,
                            '_blank',
                          )
                        }
                      >
                        资源管理
                      </Button>
                      处配置
                    </div>
                  }
                >
                  集群 / 命名空间
                </InfoTooltip>
              }
              name="clusterNs"
              rules={[{ required: true, message: '请选择集群 / 命名空间' }]}
            >
              <Cascader options={options} placeholder="请选择集群 / 命名空间" />
            </Form.Item>

            <Form.Item label="开发环境" name="type" required>
              <Radio.Group
                disabled={type !== 'create'}
                onChange={() => form.setFieldValue('imageUrl', undefined)}
              >
                <Radio value="jupyter">Jupyter Notebook</Radio>
                <Radio value="code-server">Visual Studio Code</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              label={
                <InfoTooltip
                  maxWidth={400}
                  title={
                    <div>
                      用于明确当前任务归属的应用；
                      <br />
                      若不填，则以关联的ns（命名空间）分摊成本。
                      <br />* 数据来源：CICD
                    </div>
                  }
                >
                  归属应用
                </InfoTooltip>
              }
              name="cmdbId"
            >
              <Select
                placeholder="请选择归属应用"
                options={appOptions}
                showSearch
                optionFilterProp="label"
                onChange={(value) => {
                  const selectedApp = appList?.apps?.find(
                    (app) => app.cmdbId === value,
                  )
                  if (selectedApp) {
                    form.setFieldsValue({
                      appName: selectedApp.name,
                    })
                  }
                }}
              />
            </Form.Item>
            <Form.Item name="appName" hidden>
              <Input />
            </Form.Item>
          </div>
        </div>

        <div className="mb-4">
          <SectionTitle title="环境配置" />

          <div className="grid grid-cols-1 gap-x-4 bg-[#FAFAFA] p-4 pb-0 rounded-md">
            <Form.Item
              label="镜像地址"
              name="imageUrl"
              rules={[{ required: true, message: '请输入镜像地址' }]}
            >
              <Select
                styles={{
                  popup: {
                    root: { width: 'fit-content' },
                  },
                }}
                options={
                  imageList?.images.map((item) => ({
                    label: item,
                    value: item,
                  })) || []
                }
                placeholder="请选择镜像地址"
              />
            </Form.Item>

            <div className="pb-4">
              <EnvVarsFormList disabled={disabled} />
            </div>
          </div>
        </div>

        <ResourceConfigForm
          disabled={disabled}
          teamId={currentTeam}
          showWorkerCount={false}
        />

        {/* 存储卷挂载 */}
        <VolumeMountsFormList disabled={disabled} clusterNs={clusterNs} />
      </Form>
    )
  },
)

TaskForm.displayName = 'TaskForm'

export default TaskForm
