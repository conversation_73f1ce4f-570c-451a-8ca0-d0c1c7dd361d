import { useGpuList } from '@/api/resource'
import { useGpuQuotaList } from '@/api/team'
import SectionTitle from '@/components/common/SectionTitle'
import { Button, Form, Radio, Tooltip } from 'antd'
import { useMemo } from 'react'
import type { TaskFormData } from './TaskForm'
import { WrappedInputNumber, WrappedRadioGroup, WrappedSelect } from './wrapped'

export interface ResourceConfig {
  cpu?: {
    requests?: number
    limits?: number
  }
  memory?: {
    requests?: number
    limits?: number
  }
  gpuCore?: string
  gpuMemory?: number
  gpuType?: string
}

interface ResourceConfigFormProps {
  disabled?: boolean
  showTitle?: boolean
  teamId?: number
  trainingFramework?: TaskFormData['trainingFramework']
  showWorkerCount?: boolean
}

const gpuCoreOptions = ['1/4', '1/2', '1', '2', '3', '4', '5', '6', '7', '8']

const ResourceConfigForm = ({
  disabled = false,
  showTitle = true,
  teamId,
  trainingFramework,
  showWorkerCount = true,
}: ResourceConfigFormProps) => {
  const form = Form.useFormInstance()
  const gpuMemory = Form.useWatch('gpuMemory', form)
  const gpuCore = Form.useWatch('gpuCore', form)
  const gpuType = Form.useWatch('gpuType', form)

  // 判断GPU核心是否>=1
  const isGpuCoreGreaterThanOne = () => {
    if (!gpuCore) return false
    // 处理分数形式的GPU核心值
    if (gpuCore.includes('/')) {
      const [numerator, denominator] = gpuCore.split('/').map(Number)
      return numerator / denominator >= 1
    }
    return Number.parseFloat(gpuCore) >= 1
  }

  // 当GPU核心>=1时，清空显存字段
  const handleGpuCoreChange = (value: string) => {
    if (!value) return

    let numericValue = 0
    // 处理分数形式的GPU核心值
    if (value.includes('/')) {
      const [numerator, denominator] = value.split('/').map(Number)
      numericValue = numerator / denominator
    } else {
      numericValue = Number.parseFloat(value)
    }

    if (numericValue >= 1) {
      form.setFieldValue('gpuMemory', undefined)
    }
  }

  // 处理显卡类型点击，支持取消选中
  const handleGpuTypeClick = (value: string) => {
    if (gpuType === value) {
      // 如果点击的是已选中的选项，则取消选中
      form.setFieldValue('gpuType', undefined)
    } else {
      // 否则选中该选项
      form.setFieldValue('gpuType', value)
    }
  }
  // 获取GPU列表和团队配额
  const { data: gpuListData } = useGpuList()
  const { data: gpuQuotaData } = useGpuQuotaList({ teamId: teamId || 0 })

  // 动态生成GPU选项
  const gpuOptions = useMemo(() => {
    if (!gpuListData?.gpuList) {
      return []
    }

    // 获取团队有配额的GPU类型
    const teamGpuTypes = new Set(
      gpuQuotaData?.gpuQuota?.map((quota) => quota.gpuType) || [],
    )

    return gpuListData.gpuList.map((gpu) => ({
      label: gpu.gpuAlias,
      value: gpu.gpuAlias,
      disabled: teamId ? !teamGpuTypes.has(gpu.gpuType) : false,
    }))
  }, [gpuListData, gpuQuotaData, teamId])

  return (
    <div className="mb-4">
      {showTitle && (
        <SectionTitle title={showWorkerCount ? 'workers 配置' : '资源配置'} />
      )}

      <div className="grid grid-cols-2 gap-x-4 bg-[#FAFAFA] p-4 pb-0 rounded-md">
        {showWorkerCount && (
          <>
            <Form.Item
              label="worker 数"
              name="maxReplicas"
              rules={[
                { required: true },
                { type: 'number', min: 1, message: 'worker 数需要 ≥ 1' },
              ]}
            >
              <WrappedInputNumber
                className="w-full flex-grow"
                min={0}
                placeholder="请输入 worker 数"
                disabled={trainingFramework === 'CUSTOM' || disabled}
                value={trainingFramework === 'CUSTOM' ? 1 : undefined}
                displayOnly={disabled}
              />
            </Form.Item>
            <div>{/* 占位元素，保持布局平衡 */}</div>
          </>
        )}
        <Form.Item label="CPU" className="!mb-0" required>
          <div className="flex gap-1">
            <Form.Item
              label={null}
              name={['cpu', 'requests']}
              rules={[{ required: true, message: '请输入' }]}
            >
              <WrappedInputNumber
                className="w-full flex-grow"
                placeholder="Requests 资源保障值"
                min={0}
                precision={0}
                suffix="核"
                disabled={disabled}
                displayOnly={disabled}
                onChange={() => {
                  form.validateFields([['cpu', 'limits']])
                }}
              />
            </Form.Item>
            <span
              className={
                'text-gray-500 leading-loose' + disabled ? ' leading-[2.3]' : ''
              }
            >
              ~
            </span>
            <Form.Item
              label={null}
              name={['cpu', 'limits']}
              rules={[
                { required: true, message: '请输入' },
                {
                  validator: (_, value) => {
                    const requests = form.getFieldValue(['cpu', 'requests'])
                    if (value && requests && value < requests) {
                      return Promise.reject(new Error('Limits必须大于Requests'))
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <WrappedInputNumber
                className="w-full flex-grow"
                placeholder="Limits 最大限制值"
                min={0}
                precision={0}
                suffix="核"
                disabled={disabled}
                displayOnly={disabled}
              />
            </Form.Item>
          </div>
        </Form.Item>

        {/* 内存 */}
        <Form.Item label="内存" className="!mb-0" required>
          <div className="flex gap-1">
            <Form.Item
              label={null}
              name={['memory', 'requests']}
              rules={[{ required: true, message: '请输入' }]}
            >
              <WrappedInputNumber
                className="w-full flex-grow"
                placeholder="Requests 资源保障值"
                min={0}
                precision={0}
                suffix="GiB"
                disabled={disabled}
                displayOnly={disabled}
                onChange={() => {
                  form.validateFields([['memory', 'limits']])
                }}
              />
            </Form.Item>
            <span
              className={
                'text-gray-500 leading-loose' + disabled ? ' leading-[2.3]' : ''
              }
            >
              ~
            </span>
            <Form.Item
              label={null}
              name={['memory', 'limits']}
              rules={[
                { required: true, message: '请输入' },
                {
                  validator: (_, value) => {
                    const requests = form.getFieldValue(['memory', 'requests'])
                    if (value && requests && value < requests) {
                      return Promise.reject(new Error('Limits必须大于Requests'))
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <WrappedInputNumber
                className="w-full flex-grow"
                placeholder="Limits 最大限制值"
                min={0}
                precision={0}
                suffix="GiB"
                disabled={disabled}
                displayOnly={disabled}
              />
            </Form.Item>
          </div>
        </Form.Item>

        <Form.Item label="GPU核心" name="gpuCore">
          <WrappedSelect
            placeholder="Requests 资源保障值"
            disabled={disabled}
            allowClear
            displayOnly={disabled}
            onChange={handleGpuCoreChange}
            options={gpuCoreOptions.map((item) => ({
              label: item,
              value: item,
            }))}
          />
        </Form.Item>

        <Form.Item label="GPU显存" name="gpuMemory">
          <WrappedInputNumber
            className="w-full flex-grow"
            placeholder="Requests 资源保障值"
            min={0}
            precision={0}
            suffix="GiB"
            disabled={disabled || isGpuCoreGreaterThanOne()}
            displayOnly={disabled}
          />
        </Form.Item>

        <Form.Item
          label="显卡类型"
          name="gpuType"
          rules={[{ required: !!gpuMemory || !!gpuCore }]}
        >
          <WrappedRadioGroup
            className="w-full"
            displayOnly={disabled}
            options={gpuOptions.map((option) => ({
              label: option.label,
              value: option.value,
            }))}
          >
            {gpuOptions.map((option) => {
              const radioElement = (
                <Radio
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled || disabled}
                  onClick={() =>
                    !option.disabled &&
                    !disabled &&
                    handleGpuTypeClick(option.value!)
                  }
                >
                  {option.label}
                </Radio>
              )

              if (option.disabled && teamId) {
                return (
                  <Tooltip
                    color="white"
                    key={option.value}
                    title={
                      <div className="text-[#666666]">
                        团队配额不支持该显卡，请联系团队管理员至
                        <Button
                          type="link"
                          size="small"
                          onClick={() =>
                            window.open(
                              '/team/resource-management?team=' + teamId,
                              '_blank',
                            )
                          }
                        >
                          资源管理
                        </Button>
                        处配置
                      </div>
                    }
                  >
                    {radioElement}
                  </Tooltip>
                )
              }

              return radioElement
            })}
          </WrappedRadioGroup>
        </Form.Item>
      </div>
    </div>
  )
}

export default ResourceConfigForm
