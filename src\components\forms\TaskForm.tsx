import {
  type TrainTaskClusterResource,
  type TrainTaskDetail,
  type TrainTaskVolumeMount,
  type TrainingFramework,
  useClusterNsList,
} from '@/api/traintask'
import { useAppList } from '@/api/user'
import InfoTooltip from '@/components/common/InfoTooltip'
import SectionTitle from '@/components/common/SectionTitle'
import YamlEditor from '@/components/common/YamlEditor'
import { TASK_PRIORITY_OPTIONS } from '@/constants/taskPriority'
import { TRAINING_FRAMEWORK_MAP } from '@/constants/trainingFramework'
import { trainTaskTemplate } from '@/pages/TrainTaskManagement/trainTaskTemplate'
import useUserStore from '@/stores/user'
import type { ModalType } from '@/types/common'
import { convertToTrainTaskDetail } from '@/utils/dataConvert'

import { customYaml } from '@/pages/TrainTaskManagement/yaml/custom-job'
import { mpiYaml } from '@/pages/TrainTaskManagement/yaml/mpi'
import { rayYaml } from '@/pages/TrainTaskManagement/yaml/ray'
import { tfjobYaml } from '@/pages/TrainTaskManagement/yaml/tfjob'
import { torchjobYaml } from '@/pages/TrainTaskManagement/yaml/torchjob'
import {
  Button,
  Form,
  type FormInstance,
  Radio,
  type RadioChangeEvent,
} from 'antd'
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import * as YAML from 'yaml'
import EnvVarsFormList from './EnvVarsFormList'
import ResourceConfigForm from './ResourceConfigForm'
import VolumeMountsFormList from './VolumeMountsFormList'
import {
  WrappedCascader,
  WrappedInput,
  WrappedInputNumber,
  WrappedRadioGroup,
  WrappedSelect,
  WrappedTextArea,
} from './wrapped'
export interface TaskFormData {
  taskName?: string
  namespace?: string
  clusterNs: string[]
  priority?: 'P0' | 'P1' | 'P2' | 'P3'
  taskType?: 'FORM' | 'YAML'
  trainingFramework?: TrainingFramework
  appName?: string
  cmdbId?: number
  imageUrl?: string
  startCmd?: string
  cpu?: {
    requests?: number
    limits?: number
  }
  memory?: {
    requests?: number
    limits?: number
  }
  gpuCore?: string
  gpuMemory?: number
  gpuType?: string
  maxReplicas?: number
  volumeMounts?: Array<{
    volumeType?: 'pvc' | 'configmap' | 'secret'
    name?: string
    volumeName?: string
    mountPath?: string
    subPath?: string
  }>
  taskYaml?: string
  envVars?: Array<{
    key?: string
    value?: string
  }>
}

export interface TaskFormRef {
  form: FormInstance
  submit: () => void
  reset: () => void
  getValues: () => TaskFormData
  setValues: (values: TaskFormData) => void
}

interface TaskFormProps {
  initialValues?: TaskFormData
  onFinish?: (values: TrainTaskDetail) => void
  onFinishFailed?: (errorInfo: any) => void
  disabled?: boolean
  className?: string
  type?: ModalType
}

const startCmdMap = {
  RAY: '/app/start.sh',
  TFJOB:
    'python /multi_worker_strategy-with-keras.py --saved_model_dir /train/saved_model/ --checkpoint_dir /train/checkpoint',
  PYTORCH: 'python /opt/pytorch-mnist/mnist.py --epochs=1',
  MPI: 'deepspeed --hostfile /etc/mpi/hostfile train.py --deepspeed_config=ds_config.json -p 2 --steps=200',
  CUSTOM: '',
}
const yamlMap = {
  RAY: rayYaml,
  TFJOB: tfjobYaml,
  PYTORCH: torchjobYaml,
  MPI: mpiYaml,
  CUSTOM: customYaml,
}

const customTaskTip = (
  <div className="p-2">
    <div className="font-bold mb-2">「自定义」任务</div>
    <div className="mb-2">
      适用于那些不完全契合平台现有主流框架（如TensorFlow、PyTorch）预设模板的特殊训练场景。
    </div>

    <div className="font-semibold mb-1">选择此选项当且仅当：</div>
    <ol className="list-decimal list-inside mb-2 pl-2">
      <li>
        <span className="font-semibold">新兴框架</span>
        ：需使用平台未集成的新兴框架（如新型分布式训练框架）。
      </li>
      <li>
        <span className="font-semibold">高度定制</span>
        ：训练逻辑远超标准模板能力（如多阶段混合训练、自定义通信协议）。
      </li>
      <li>
        <span className="font-semibold">自研框架</span>
        ：已有成熟自研训练体系需无缝集成至平台。
      </li>
    </ol>

    <div className="font-semibold mb-1">选择后需承担以下风险：</div>
    <ol className="list-decimal list-inside pl-2">
      <li>
        <span className="font-semibold">技术依赖</span>
        ：失去平台自动化配置校验与最佳实践指导，配置正确性完全依赖自身经验。
      </li>
      <li>
        <span className="font-semibold">运行风险</span>
        ：自定义配置易引发错误（如依赖缺失、资源分配不合理），问题诊断依赖个人能力。
      </li>
      <li>
        <span className="font-semibold">支持受限</span>
        ：标准监控/调试工具无法适配，自动化运维功能（如扩缩容）效果降低。
      </li>
      <li>
        <span className="font-semibold">性能风险</span>
        ：无法利用平台针对主流框架优化的调度算法，可能导致训练效率下降。
      </li>
    </ol>
  </div>
)

const TaskForm = forwardRef<TaskFormRef, TaskFormProps>(
  (
    {
      initialValues,
      onFinish,
      onFinishFailed,
      disabled = false,
      className = '',
      type,
    },
    ref,
  ) => {
    const currentTeam = useUserStore((state) => state.getCurrentTeam())
    const [form] = Form.useForm<TaskFormData>()
    const taskType = Form.useWatch('taskType', form)
    const clusterNs = Form.useWatch('clusterNs', form)
    const taskYaml = Form.useWatch('taskYaml', form)
    const taskName = Form.useWatch('taskName', form)
    const trainingFramework = Form.useWatch('trainingFramework', form)

    const { data: clusterNsList } = useClusterNsList({
      teamId: currentTeam,
    })
    const { data: appList } = useAppList(currentTeam)

    const options = clusterNsList?.clusterNamespaces?.map((item) => ({
      value: item.cluster,
      label: item.cluster,
      children: item.namespaces?.map((ns) => ({
        value: ns,
        label: ns,
      })),
    }))

    const appOptions = appList?.apps?.map((app) => ({
      value: app.cmdbId,
      label: app.name,
    }))

    const defaultInitialValues: TaskFormData = {
      priority: 'P2',
      taskType: 'FORM',
      trainingFramework: 'TFJOB',
      volumeMounts: [{ volumeType: 'pvc' }],
      clusterNs: [],
      startCmd: startCmdMap.TFJOB,

      taskYaml: trainTaskTemplate,
      ...initialValues,
    }

    const handleFinish = (values: TaskFormData) => {
      const TrainTaskDetail = convertToTrainTaskDetail(values, currentTeam, 0)
      console.log('转换后的数据:', TrainTaskDetail)

      // 调用原始的 onFinish，但传入转换后的数据
      onFinish?.(TrainTaskDetail)
    }

    const handleFinishFailed = (errorInfo: any) => {
      onFinishFailed?.(errorInfo)
    }

    useImperativeHandle(ref, () => ({
      form,
      submit: () => form.submit(),
      reset: () => form.resetFields(),
      getValues: () => form.getFieldsValue(),
      setValues: (values: TaskFormData) => form.setFieldsValue(values),
    }))

    const yamlToDoc = (yaml?: string) => YAML.parseDocument(yaml || '{}') // 使用 parseDocument 保留注释

    const getNewYamlByChangeName = (name: string | undefined) => {
      const yamlDoc = yamlToDoc(taskYaml)

      // 确保 metadata 节点存在
      if (!yamlDoc.has('metadata')) {
        yamlDoc.set('metadata', yamlDoc.createNode({}))
      }

      // 使用 setIn 方法修改 metadata.name，保留注释
      yamlDoc.setIn(['metadata', 'name'], name ?? null)

      // 转回 YAML 字符串
      return yamlDoc.toString()
    }

    const getYamlName = () => {
      const yamlDoc = yamlToDoc(taskYaml)
      return yamlDoc.getIn(['metadata', 'name']) || '' // 直接获取 metadata.name
    }

    // 处理任务名称变化（仅在YAML模式下同步到YAML）
    const handleTaskNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newName = e.target.value

      // 如果是YAML模式，同步更新YAML中的名称
      if (taskType === 'YAML') {
        const newYaml = getNewYamlByChangeName(newName)
        form.setFieldValue('taskYaml', newYaml)
      }
    }

    // 处理创建方式变化
    const handleTaskTypeChange = (e: RadioChangeEvent) => {
      const newTaskType = e.target.value

      // 切换到YAML方式时，根据当前训练框架加载对应模板
      if (newTaskType === 'YAML') {
        const selectedYaml = yamlMap[trainingFramework as TrainingFramework]
        if (selectedYaml) {
          const yamlDoc = yamlToDoc(selectedYaml)

          // 确保 metadata 节点存在
          if (!yamlDoc.has('metadata')) {
            yamlDoc.set('metadata', yamlDoc.createNode({}))
          }

          // 设置任务名称
          yamlDoc.setIn(['metadata', 'name'], taskName || null)

          // 更新YAML
          form.setFieldValue('taskYaml', yamlDoc.toString())
        }
      }
    }

    // 处理YAML内容变化（同步名称到表单）
    const handleYamlChange = (value: string) => {
      // 如果是YAML模式，从YAML中提取名称并同步到表单
      if (taskType === 'YAML') {
        try {
          const yamlDoc = YAML.parseDocument(value)
          const yamlName = yamlDoc.getIn(['metadata', 'name']) || ''
          if (yamlName && yamlName !== taskName) {
            form.setFieldValue('taskName', yamlName)
          }
        } catch (e) {
          // YAML解析错误时不处理
        }
      }
    }

    return (
      <Form
        form={form}
        layout="horizontal"
        onFinish={handleFinish}
        onFinishFailed={handleFinishFailed}
        initialValues={defaultInitialValues}
        disabled={disabled}
        className={className}
        labelCol={{ flex: '0 0 140px' }}
      >
        {/* 基础配置 */}
        <div className="mb-4">
          <SectionTitle title="基础配置" />

          <div className="bg-[#FAFAFA] p-4 rounded-md">
            {/* 第一行：名称和集群/命名空间 */}
            <div className="grid grid-cols-2 gap-x-4">
              <Form.Item
                label={
                  <InfoTooltip title="长度不能超过30个字符，支持中文（仅作为模板名称，任务运行时，会自动生成任务实例名）">
                    名称
                  </InfoTooltip>
                }
                name="taskName"
                rules={[
                  { required: true, message: '请输入任务名称' },
                  {
                    validator: (_, value) => {
                      if (value && value.length > 30) {
                        return Promise.reject(
                          new Error('任务名称不能超过30个字符'),
                        )
                      }

                      return Promise.resolve()
                    },
                  },
                ]}
              >
                <WrappedInput
                  placeholder="请输入任务名称"
                  maxLength={30}
                  onChange={handleTaskNameChange}
                  displayOnly={disabled}
                />
              </Form.Item>

              <Form.Item
                label={
                  <InfoTooltip
                    maxWidth={600}
                    title={
                      <>
                        <div>
                          1. 找不到资源? 请联系团队管理员至
                          <Button
                            type="link"
                            size="small"
                            onClick={() =>
                              window.open(
                                '/team/resource-management?team=' + currentTeam,
                                '_blank',
                              )
                            }
                          >
                            资源管理
                          </Button>
                          处配置
                        </div>
                        <div>2. YAML 任务将自动识别此处数据</div>
                      </>
                    }
                  >
                    集群 / 命名空间
                  </InfoTooltip>
                }
                name="clusterNs"
                rules={[{ required: true, message: '请选择集群 / 命名空间' }]}
              >
                <WrappedCascader
                  options={options}
                  placeholder="请选择集群 / 命名空间"
                  showSearch
                  displayOnly={disabled}
                />
              </Form.Item>
            </div>

            {/* 第二行：创建方式和所属应用 */}
            <div className="grid grid-cols-2 gap-x-4">
              <Form.Item
                label={
                  <InfoTooltip title="创建后不可修改">创建方式</InfoTooltip>
                }
                name="taskType"
                required
              >
                <WrappedRadioGroup
                  disabled={type !== 'create' && type !== 'copy'}
                  onChange={handleTaskTypeChange}
                  displayOnly={disabled}
                >
                  <Radio value="FORM">表单方式</Radio>
                  <Radio value="YAML">YAML方式</Radio>
                </WrappedRadioGroup>
              </Form.Item>

              <Form.Item
                label={
                  <InfoTooltip
                    maxWidth={400}
                    title={
                      <div>
                        用于明确当前任务归属的应用；
                        <br />
                        若不填，则以关联的ns（命名空间）分摊成本。
                        <br />* 数据来源：CICD
                      </div>
                    }
                  >
                    归属应用
                  </InfoTooltip>
                }
                name="cmdbId"
              >
                <WrappedSelect
                  placeholder="请选择归属应用"
                  options={appOptions}
                  showSearch
                  optionFilterProp="label"
                  displayOnly={disabled}
                  onChange={(value) => {
                    const selectedApp = appList?.apps?.find(
                      (app) => app.cmdbId === value,
                    )
                    if (selectedApp) {
                      form.setFieldsValue({
                        appName: selectedApp.name,
                      })
                    }
                  }}
                />
              </Form.Item>

              <Form.Item name="appName" hidden>
                <WrappedInput displayOnly={disabled} />
              </Form.Item>
            </div>

            {/* 第三行：任务类型 */}
            <div className="">
              <Form.Item
                label={
                  <InfoTooltip title="创建后不可修改">任务类型</InfoTooltip>
                }
                name="trainingFramework"
                required
              >
                <WrappedRadioGroup
                  disabled={type !== 'create' && type !== 'copy'}
                  displayOnly={disabled}
                  onChange={(e) => {
                    if (e.target.value === 'CUSTOM') {
                      form.setFieldValue('maxReplicas', 1)
                    }
                    // 根据选择的框架自动更新启动命令
                    form.setFieldValue(
                      'startCmd',
                      startCmdMap[e.target.value as TrainingFramework],
                    )

                    // 如果是YAML类型，根据选择的框架自动更新YAML内容
                    if (taskType === 'YAML') {
                      const selectedYaml =
                        yamlMap[e.target.value as TrainingFramework]
                      if (selectedYaml) {
                        // 保留原有的任务名称
                        const currentName = getYamlName()
                        const yamlDoc = yamlToDoc(selectedYaml)

                        // 确保 metadata 节点存在
                        if (!yamlDoc.has('metadata')) {
                          yamlDoc.set('metadata', yamlDoc.createNode({}))
                        }

                        // 设置任务名称
                        yamlDoc.setIn(
                          ['metadata', 'name'],
                          currentName || taskName || null,
                        )

                        // 更新YAML
                        form.setFieldValue('taskYaml', yamlDoc.toString())
                      }
                    }
                  }}
                >
                  <Radio value="TFJOB">{TRAINING_FRAMEWORK_MAP.TFJOB}</Radio>
                  <Radio value="PYTORCH">
                    {TRAINING_FRAMEWORK_MAP.PYTORCH}
                  </Radio>
                  <Radio value="MPI">{TRAINING_FRAMEWORK_MAP.MPI}</Radio>
                  <Radio value="RAY">{TRAINING_FRAMEWORK_MAP.RAY}</Radio>
                  <Radio value="CUSTOM">
                    <InfoTooltip
                      title={customTaskTip}
                      iconPosition="right"
                      iconClassName="ml-2"
                      placement="bottom"
                      maxWidth={1000}
                    >
                      {TRAINING_FRAMEWORK_MAP.CUSTOM}
                    </InfoTooltip>
                  </Radio>
                </WrappedRadioGroup>
              </Form.Item>
            </div>

            {/* 第四行：优先级 */}
            <div>
              <Form.Item
                label={
                  <InfoTooltip title="影响资源调度逻辑：优先级越高越先安排资源，在资源不足时，高级别任务会抢占低级别任务的资源。">
                    优先级
                  </InfoTooltip>
                }
                name="priority"
                required
              >
                <WrappedRadioGroup
                  className="flex flex-col gap-2 pt-1"
                  displayOnly={disabled}
                  options={TASK_PRIORITY_OPTIONS.map((option) => ({
                    label: (
                      <span className="flex items-center">
                        <span
                          className="inline-block w-2 h-2 rounded-full mr-2"
                          style={{ backgroundColor: option.color }}
                        />
                        {option.label}{' '}
                        <span className="text-gray-500 ml-1">
                          ({option.description})
                        </span>
                      </span>
                    ),
                    value: option.value,
                  }))}
                >
                  {TASK_PRIORITY_OPTIONS.map((option) => (
                    <Radio
                      key={option.value}
                      value={option.value}
                      className="flex items-center"
                    >
                      <span className="flex items-center">
                        <span
                          className="inline-block w-2 h-2 rounded-full mr-2"
                          style={{ backgroundColor: option.color }}
                        />
                        {option.label}{' '}
                        <span className="text-gray-500 ml-1">
                          ({option.description})
                        </span>
                      </span>
                    </Radio>
                  ))}
                </WrappedRadioGroup>
              </Form.Item>
            </div>
          </div>
        </div>

        {taskType !== 'YAML' ? (
          <>
            {/* 环境配置 */}
            <div className="mb-4">
              <SectionTitle title="环境配置" />

              <div className="grid grid-cols-1 gap-x-4 bg-[#FAFAFA] p-4 pb-0 rounded-md">
                <Form.Item
                  label="镜像地址"
                  name="imageUrl"
                  rules={[{ required: true }]}
                >
                  <WrappedInput
                    placeholder="请输入镜像地址"
                    displayOnly={disabled}
                  />
                </Form.Item>

                <Form.Item
                  label="启动命令"
                  name="startCmd"
                  rules={[{ required: true }]}
                >
                  <WrappedTextArea
                    className="!min-h-[80px] !max-h-[240px]"
                    placeholder="请输入启动命令"
                    autoSize
                    displayOnly={disabled}
                  />
                </Form.Item>

                <div className="pb-4">
                  <EnvVarsFormList disabled={disabled} />
                </div>
              </div>
            </div>

            {/* 资源配置 */}
            <ResourceConfigForm
              trainingFramework={trainingFramework}
              disabled={disabled}
              teamId={currentTeam}
            />

            {/* 存储卷挂载 */}
            <VolumeMountsFormList disabled={disabled} clusterNs={clusterNs} />
          </>
        ) : (
          <Form.Item
            name="taskYaml"
            noStyle
            rules={[
              { required: true, message: '请输入YAML配置' },
              {
                validator: (_, value) => {
                  try {
                    YAML.parse(value)
                    return Promise.resolve()
                  } catch (e) {
                    return Promise.reject(new Error('YAML格式错误'))
                  }
                },
              },
            ]}
          >
            <YamlEditor
              height="calc(100vh - 320px)"
              readOnly={disabled}
              onChange={handleYamlChange}
            />
          </Form.Item>
        )}
      </Form>
    )
  },
)

TaskForm.displayName = 'TaskForm'

export default TaskForm
