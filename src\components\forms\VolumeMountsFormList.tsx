import { useConfigmapList, usePvcList, useSecretList } from '@/api/traintask'
import SectionTitle from '@/components/common/SectionTitle'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Form, Radio } from 'antd'
import { type FC, useContext } from 'react'
import { WrappedInput, WrappedSelect, WrappedRadioGroup } from './wrapped'

export interface VolumeMount {
  volumeType?: 'pvc' | 'configmap' | 'secret'
  name?: string
  volumeName?: string
  mountPath?: string
  subPath?: string
}

interface VolumeMountsFormListProps {
  disabled?: boolean
  clusterNs: string[]
}

const VolumeMountsFormList: FC<VolumeMountsFormListProps> = ({
  disabled = false,
  clusterNs,
}) => {
  const form = Form.useFormInstance()
  const volumeMounts = Form.useWatch('volumeMounts', form)
  const { data: configmapList } = useConfigmapList({
    clusterName: clusterNs?.[0],
    namespace: clusterNs?.[1],
  })
  const { data: pvcList } = usePvcList({
    clusterName: clusterNs?.[0],
    namespace: clusterNs?.[1],
  })
  const { data: secretList } = useSecretList({
    clusterName: clusterNs?.[0],
    namespace: clusterNs?.[1],
  })

  // 根据存储类型获取对应的选项列表的辅助函数
  const getVolumeNameOptions = (volumeType?: string) => {
    switch (volumeType) {
      case 'pvc':
        return (
          pvcList?.pvcNames?.map((name) => ({
            label: name,
            value: name,
          })) || []
        )
      case 'configmap':
        return (
          configmapList?.configmapNames?.map((name) => ({
            label: name,
            value: name,
          })) || []
        )
      case 'secret':
        return (
          secretList?.secretNames?.map((name) => ({
            label: name,
            value: name,
          })) || []
        )
      default:
        return []
    }
  }

  return (
    <div className="mb-4">
      <div className="flex items-center justify-between">
        <SectionTitle title="存储卷挂载" className="mb-0" />
        <Button
          type="link"
          icon={<PlusOutlined />}
          disabled={disabled}
          onClick={() => {
            const volumeMounts = form.getFieldValue('volumeMounts') || []
            form.setFieldValue('volumeMounts', [
              ...volumeMounts,
              { volumeType: 'pvc' },
            ])
          }}
        >
          添加
        </Button>
      </div>

      <Form.List name="volumeMounts">
        {(fields, { remove }) => (
          <div className="max-h-[600px] overflow-auto bg-[#FAFAFA]  rounded-lg p-4  relative">
            {fields.map(({ key, name, ...restField }) => {
              // 获取当前存储卷挂载项的存储类型
              const currentVolumeType = volumeMounts?.[name]?.volumeType

              return (
                <div
                  key={key}
                  className=" bg-white rounded-lg p-4 pr-[32px] pb-0 relative last:mb-0 mb-4 border-dashed border border-gray-300 overflow-visible "
                >
                  <Button
                    type="link"
                    danger
                    icon={<DeleteOutlined />}
                    className="absolute  right-[-4] top-[-4] z-10"
                    disabled={disabled}
                    onClick={() => remove(name)}
                  />

                  <div className="grid grid-cols-2 gap-x-4 pb-0">
                    <Form.Item
                      {...restField}
                      label="存储类型"
                      name={[name, 'volumeType']}
                      rules={[{ required: true, message: '请选择存储类型' }]}
                    >
                      <WrappedRadioGroup
                        displayOnly={disabled}
                        onChange={() => {
                          // 当存储类型变更时，清空存储卷名称
                          form.setFieldValue(
                            ['volumeMounts', name, 'volumeName'],
                            undefined,
                          )
                        }}
                      >
                        <Radio value="pvc">PVC</Radio>
                        <Radio value="configmap">ConfigMap</Radio>
                        <Radio value="secret">Secret</Radio>
                      </WrappedRadioGroup>
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      label={
                        currentVolumeType === 'pvc'
                          ? 'PVC 名称'
                          : currentVolumeType === 'configmap'
                            ? 'ConfigMap 名称'
                            : 'Secret 名称'
                      }
                      name={[name, 'volumeName']}
                      rules={[{ required: true, message: '请输入存储卷名称' }]}
                    >
                      <WrappedSelect
                        placeholder={
                          !clusterNs?.[0] || !clusterNs?.[1]
                            ? '请先选择集群 / 命名空间'
                            : '请选择存储卷名称'
                        }
                        showSearch
                        options={getVolumeNameOptions(currentVolumeType)}
                        disabled={
                          !currentVolumeType ||
                          !clusterNs?.[0] ||
                          !clusterNs?.[1] ||
                          disabled
                        }
                        notFoundContent={'暂无数据'}
                        displayOnly={disabled}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      label="子路径"
                      name={[name, 'subPath']}
                    >
                      <WrappedInput 
                        placeholder="请输入子路径" 
                        displayOnly={disabled}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      label="容器内挂载路径"
                      name={[name, 'mountPath']}
                      rules={[
                        {
                          required: true,
                          message: '请输入容器内挂载路径',
                        },
                      ]}
                    >
                      <WrappedInput 
                        placeholder="请输入容器内挂载路径" 
                        displayOnly={disabled}
                      />
                    </Form.Item>

                    {volumeMounts?.[name]?.name ? (
                      <Form.Item
                        {...restField}
                        label="挂载名称"
                        name={[name, 'name']}
                      >
                        {volumeMounts?.[name]?.name}
                      </Form.Item>
                    ) : null}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </Form.List>
    </div>
  )
}

export default VolumeMountsFormList
