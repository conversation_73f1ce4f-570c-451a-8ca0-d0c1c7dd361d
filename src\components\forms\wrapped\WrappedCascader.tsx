import { Cascader } from 'antd'
import { forwardRef } from 'react'

// Cascader 类型太复杂了
const WrappedCascader = forwardRef<any, any>(
  ({ displayOnly, value, placeholder, options, ...props }, ref) => {
    if (displayOnly) {
      if (value && Array.isArray(value) && value.length > 0) {
        return <span className="text-gray-900">{value.join(' / ')}</span>
      }

      return <span className="text-gray-400">-</span>
    }

    return (
      <Cascader
        ref={ref}
        value={value}
        placeholder={placeholder}
        options={options}
        {...props}
      />
    )
  },
)

WrappedCascader.displayName = 'WrappedCascader'

export default WrappedCascader
