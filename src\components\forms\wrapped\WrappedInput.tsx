import { Input, type InputProps } from 'antd'
import { forwardRef } from 'react'

interface WrappedInputProps extends InputProps {
  displayOnly?: boolean
}

const WrappedInput = forwardRef<any, WrappedInputProps>(
  ({ displayOnly, value, placeholder, ...props }, ref) => {
    if (displayOnly) {
      return (
        <span className="text-gray-900">
          {(value as string) || <span className="text-gray-400">-</span>}
        </span>
      )
    }

    return (
      <Input ref={ref} value={value} placeholder={placeholder} {...props} />
    )
  },
)

WrappedInput.displayName = 'WrappedInput'

export default WrappedInput
