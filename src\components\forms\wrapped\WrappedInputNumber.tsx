import { InputNumber, type InputNumberProps } from 'antd'
import { forwardRef } from 'react'

interface WrappedInputNumberProps extends InputNumberProps {
  displayOnly?: boolean
}

const WrappedInputNumber = forwardRef<any, WrappedInputNumberProps>(
  ({ displayOnly, value, placeholder, suffix, ...props }, ref) => {
    if (displayOnly) {
      if (value !== undefined && value !== null && value !== '') {
        return (
          <span className="text-gray-900">
            {value}
            {suffix && ` ${suffix}`}
          </span>
        )
      }
      return <span className="text-gray-400">-</span>
    }

    return (
      <InputNumber
        ref={ref}
        value={value}
        placeholder={placeholder}
        suffix={suffix}
        {...props}
      />
    )
  },
)

WrappedInputNumber.displayName = 'WrappedInputNumber'

export default WrappedInputNumber
