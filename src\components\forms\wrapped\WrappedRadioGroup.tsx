import { Radio, type RadioGroupProps } from 'antd'
import { type ReactNode, forwardRef } from 'react'

interface WrappedRadioGroupProps extends RadioGroupProps {
  displayOnly?: boolean
  options?: Array<{ label: ReactNode; value: any; [key: string]: any }>
}

const WrappedRadioGroup = forwardRef<any, WrappedRadioGroupProps>(
  ({ displayOnly, value, children, options, ...props }, ref) => {
    if (displayOnly) {
      // 如果有 options 属性，从中查找对应的 label
      if (options) {
        const selectedOption = options.find((option) => option.value === value)
        return (
          <div className="text-gray-900">
            {selectedOption?.label || <span className="text-gray-400">-</span>}
          </div>
        )
      }

      // 如果没有 options，从 children 中查找对应的 label
      if (children) {
        const childrenArray = Array.isArray(children) ? children : [children]
        const selectedChild = childrenArray.find(
          (child: any) => child?.props?.value === value,
        )

        return (
          <div className="text-gray-900">
            {selectedChild?.props?.children || (
              <span className="text-gray-400">-</span>
            )}
          </div>
        )
      }

      return <span className="text-gray-400">-</span>
    }

    return (
      <Radio.Group ref={ref} value={value} {...props}>
        {children}
      </Radio.Group>
    )
  },
)

WrappedRadioGroup.displayName = 'WrappedRadioGroup'

export default WrappedRadioGroup
