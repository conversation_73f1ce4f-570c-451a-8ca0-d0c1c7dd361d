import { Select, type SelectProps } from 'antd'
import { forwardRef } from 'react'

interface WrappedSelectProps extends SelectProps {
  displayOnly?: boolean
}

const WrappedSelect = forwardRef<any, WrappedSelectProps>(
  ({ displayOnly, value, placeholder, options, ...props }, ref) => {
    if (displayOnly) {
      if (options) {
        const selectedOption = options.find(
          (option: any) => option.value === value,
        )
        return (
          <span className="text-gray-900">
            {selectedOption?.label || <span className="text-gray-400">-</span>}
          </span>
        )
      }

      return (
        <span className="text-gray-900">
          {value || <span className="text-gray-400">-</span>}
        </span>
      )
    }

    return (
      <Select
        ref={ref}
        value={value}
        placeholder={placeholder}
        options={options}
        {...props}
      />
    )
  },
)

WrappedSelect.displayName = 'WrappedSelect'

export default WrappedSelect
