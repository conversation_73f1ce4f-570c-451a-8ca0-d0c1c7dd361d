import { Input } from 'antd'
import type { TextAreaProps } from 'antd/es/input'
import { forwardRef } from 'react'

const { TextArea } = Input

interface WrappedTextAreaProps extends TextAreaProps {
  displayOnly?: boolean
}

const WrappedTextArea = forwardRef<any, WrappedTextAreaProps>(
  ({ displayOnly, value, placeholder, ...props }, ref) => {
    if (displayOnly) {
      return (
        <div className="text-gray-900 whitespace-pre-wrap">
          {(value as string) || <span className="text-gray-400">-</span>}
        </div>
      )
    }

    return (
      <TextArea ref={ref} value={value} placeholder={placeholder} {...props} />
    )
  },
)

WrappedTextArea.displayName = 'WrappedTextArea'

export default WrappedTextArea
