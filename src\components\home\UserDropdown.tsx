import { req } from '@/api/req'
import useUserStore from '@/stores/user'
import { DownOutlined, SmileOutlined } from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { Dropdown, Space } from 'antd'

const items: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a
        rel="noopener noreferrer"
        href="/api/v1/auth/logout"
        onClick={async (e) => {
          e.preventDefault()
          await req.post<never, never>('/api/v1/auth/logout')
          window.location.href = 'https://yw-sso.ttyuyin.com/'
        }}
      >
        退出
      </a>
    ),
  },
]

const UserDropdown = ({ className }: { className?: string }) => {
  const { userInfo } = useUserStore()
  return (
    <div className={`${className} flex items-center gap-3`}>
      <Dropdown menu={{ items }}>
        {/* biome-ignore lint/a11y/useValidAnchor: <explanation> */}
        <a onClick={(e) => e.preventDefault()} className="text-[#333333]">
          <Space>
            {userInfo?.chineseName} / {userInfo?.employeeNo}
            <DownOutlined />
          </Space>
        </a>
      </Dropdown>
    </div>
  )
}

export default UserDropdown
