import { Button, Result } from 'antd'
import { useNavigate } from 'react-router-dom'

const Error403 = () => {
  const navi = useNavigate()
  return (
    <Result
      status="403"
      title="403"
      subTitle="Sorry, you are not authorized to access this page."
      extra={
        <Button type="primary" onClick={() => navi('/')}>
          Back Home
        </Button>
      }
    />
  )
}

export default Error403
