import useUserStore from '@/stores/user'
import { processLinkTo } from '@/utils/navigation'
import { Menu, type MenuProps } from 'antd'
import { useEffect, useState } from 'react'
import { NavLink, type RouteObject, useMatches } from 'react-router-dom'
import { admin, user } from '../../router'
import { ScrollArea } from '../ui/scroll-area'

type MenuItem = Required<MenuProps>['items'][number]

const LeftMenu = () => {
  const matches = useMatches()
  const [openkeys, setOpenKeys] = useState<string[]>([])
  const [selectKey, setActiveKey] = useState<string[]>([])
  const userInfo = useUserStore((state) => state.userInfo)
  const isAdmin = userInfo?.role.includes('admin')
  const currentRoleAtTeam = useUserStore((state) => state.currentRoleAtTeam)
  useEffect(() => {
    const l = matches.length
    const opened = (matches[l - 2].handle as { name: string })?.name
    const actived = (matches[l - 1].handle as { name: string })?.name
    setOpenKeys([opened])
    setActiveKey([actived])
  }, [matches])
  const menu = isAdmin ? [...user, ...admin] : user
  const items: MenuItem[] = menu
    .filter((route) => route.handle)
    .map((route: RouteObject) => {
      const handle = route.handle as { name: string }
      return {
        key: handle.name,
        type: route.children ? 'group' : null,
        icon: route.handle?.icon,
        label: route.children ? (
          handle.name
        ) : (
          <NavLink
            to={processLinkTo(route.path!) || ''}
            className={({ isActive, isPending }) =>
              isPending ? 'pending' : isActive ? 'active' : ''
            }
          >
            {handle.name}
          </NavLink>
        ),
        children: route.children
          ?.filter((child) => {
            if (!child.handle) return false
            return child.handle.teamAdminOnly
              ? currentRoleAtTeam === 'admin'
              : true
          })
          .map((child) => {
            const handle = child.handle as { name: string }
            return {
              key: handle.name,
              icon: child.handle?.icon,
              label: (
                <NavLink
                  to={processLinkTo(child.path!) || ''}
                  className={({ isActive, isPending }) =>
                    isPending ? 'pending' : isActive ? 'active' : ''
                  }
                >
                  {handle.name}
                </NavLink>
              ),
            } as MenuItem
          }),
      } as MenuItem
    })
  return (
    <ScrollArea className="h-[calc(100vh-100px)]">
      <Menu
        openKeys={openkeys}
        selectedKeys={selectKey}
        onOpenChange={(openKeys) => setOpenKeys(openKeys)}
        onSelect={({ selectedKeys }) => setActiveKey(selectedKeys)}
        mode="inline"
        items={items}
        inlineIndent={8}
      />
    </ScrollArea>
  )
}
export default LeftMenu
