import { type Role, type Team, roleMap } from '@/api/user'
import { DownOutlined } from '@ant-design/icons'
import { ConfigProvider, Select, theme } from 'antd'
import { useEffect, useState } from 'react'
import { Navigate, useMatches, useNavigate } from 'react-router-dom'
import useUserStore from '../../stores/user'
import { navigateWithTeam } from '../../utils/navigation'

const ModuleSelector = () => {
  const userInfo = useUserStore((state) => state.userInfo)
  const setCurrentTeam = useUserStore((state) => state.setCurrentTeam)
  const currentTeam = useUserStore((state) => state.currentTeam)
  const [options, setOptions] = useState<Team[]>([])
  const navigate = useNavigate()
  useEffect(() => {
    if (userInfo) {
      console.log(userInfo)
      const team = userInfo.team
      console.log(team)
      if (team.length === 0) {
        navigate('/403', { replace: true })
      }
      setOptions(team)

      const searchParams = new URLSearchParams(window.location.search)
      const teamFromQuery = searchParams.get('team')
      const localTeam = Number(localStorage.getItem('team'))
      // debugger
      if (teamFromQuery && team.some((t) => t.id === +teamFromQuery)) {
        setCurrentTeam(+teamFromQuery)
        localStorage.setItem('team', teamFromQuery)
      } else if (localTeam && team.some((t) => t.id === +localTeam)) {
        // 确保存储的 team 在当前用户的团队列表中
        setCurrentTeam(localTeam)
        localStorage.setItem('team', localTeam + '')
        searchParams.set('team', localTeam + '')
        navigateWithTeam(navigate, window.location.pathname, {
          replace: true,
        })
      } else if (team.length > 0) {
        setCurrentTeam(team[0].id)
        localStorage.setItem('team', team[0].id + '')
        searchParams.set('team', team[0].id + '')
        navigateWithTeam(navigate, window.location.pathname, {
          replace: true,
        })
      } else {
        navigate('/403', { replace: true })
      }
    }
  }, [userInfo, navigate, setCurrentTeam])
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      className="ml-2"
    >
      <ConfigProvider
        theme={{
          components: {
            Select: {
              selectorBg: '#f2f2f2',
            },
          },
        }}
      >
        {!!currentTeam && (
          <Select
            variant="filled"
            className="my-4"
            value={currentTeam}
            popupMatchSelectWidth={false}
            options={options.map((team) => ({
              label: team.teamName || team.id.toString(),
              value: team.id,
            }))}
            filterSort={(optionA, optionB) =>
              (optionA?.label ?? '')
                .toLowerCase()
                .localeCompare((optionB?.label ?? '').toLowerCase())
            }
            onChange={(value) => {
              setCurrentTeam(value)
              localStorage.setItem('team', value + '')
              navigateWithTeam(navigate, window.location.pathname)
            }}
            showSearch
            optionFilterProp="label"
          />
        )}
      </ConfigProvider>
    </div>
  )
}

export default ModuleSelector
