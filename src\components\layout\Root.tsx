import logo from '@/assets/logo.png'
import type { MenuProps } from 'antd'
import { Breadcrumb, Layout, Menu, theme } from 'antd'
import type React from 'react'
import { useState } from 'react'
import {
  NavLink,
  Outlet,
  Router,
  type UIMatch,
  useMatches,
} from 'react-router-dom'
import UserDropdown from '../home/<USER>'
import LeftMenu from './LeftMenu'
import ModuleSelector from './ModuleSelector'

const { Header, Content, Footer, Sider } = Layout

const skipTeamSelectorPathnameList = [
  '/workspace',
  '/admin/user-management',
  '/admin/sys-config',
  '/admin/resource-dashboard',
  '/admin/resource-management',
  '/admin/token-management',
]

const skipBackGroundPathnameList = [
  '/workspace',
  '/resource-dashboard',
  '/admin/resource-dashboard',
]

const Root: React.FC = () => {
  const matches = useMatches()
  const [collapsed, setCollapsed] = useState(false)
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken()

  const isSkipTeamSelectorPathname = matches.some((match) =>
    skipTeamSelectorPathnameList.includes(match.pathname),
  )

  const isSkipBackGroundPathname = matches.some((match) =>
    skipBackGroundPathnameList.includes(match.pathname),
  )

  return (
    <Layout className="h-screen">
      <Layout>
        <Sider
          className="shadow-menu"
          width={220}
          theme="light"
          collapsible={false}
          collapsed={collapsed}
          onCollapse={(value) => setCollapsed(value)}
        >
          <div className="flex text-xl font-semibold items-center justify-center ">
            <img className="w-[132px] mb-[-32px]" src={logo} alt="logo" />
          </div>
          <LeftMenu />
        </Sider>
        <Layout>
          <div className="flex items-center justify-between h-16 px-4 bg-white m-[16px] rounded-lg">
            <div className="flex items-center justify-between">
              <Breadcrumb
                separator=">"
                items={matches
                  .slice(1)
                  .filter((match) => match.handle)
                  .map((match) => {
                    const { pathname, handle } = match
                    const data = handle as { name: string }
                    return {
                      title: data.name,
                    }
                  })}
              />
              <div
                className={
                  isSkipTeamSelectorPathname ? 'hidden' : 'flex items-center'
                }
              >
                <div className="ml-2 color-[#333333] h-[22px]">{'>'}</div>
                <ModuleSelector />
              </div>
            </div>
            <UserDropdown />
          </div>
          <Content
            className="m-[16px] mt-0 gap-2"
            style={{
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <div
              className={
                isSkipBackGroundPathname ? '' : 'bg-white p-4 rounded-md'
              }
              style={{
                flexGrow: 1,
                overflow: 'auto',
              }}
            >
              <Outlet />
            </div>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  )
}

export default Root
