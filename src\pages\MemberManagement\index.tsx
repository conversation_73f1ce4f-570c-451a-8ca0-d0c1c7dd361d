import { SearchOutlined } from '@ant-design/icons'
import type { ProColumns } from '@ant-design/pro-components'
import {
  ModalForm,
  ProForm,
  ProFormItem,
  ProFormRadio,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components'
import { Button, Space, Tag } from 'antd'
import { EnumUserRole, type User, UserRoleMap } from './setting'
import useMemberManagementService, {
  type FormValue,
} from './useMemberManagementService'

export default () => {
  const {
    getTableRequest,
    actionRef,
    onRemove,
    contextHolder,
    monitorParams,
    onUpdateUserInfo,
    form,
    modalVisible,
    onOpenCreateOrUpdateModal,
    setModalVisible,
    onGetUserList,
    modalType,
    hasAuth,
  } = useMemberManagementService()

  const columns: ProColumns<User>[] = [
    {
      title: '成员',
      dataIndex: 'employeeNo',
      copyable: true,
      ellipsis: true,
      search: false,
      width: '25%',
      renderText: (text, record, index, action) =>
        record.nickName + ' / ' + text,
    },
    {
      title: '角色',
      dataIndex: 'role',
      search: false,
      width: '25%',
      render: (_, record) => (
        <Tag
          color={UserRoleMap[record.role].color}
          className="!text-[#666666] w-20 text-center"
        >
          {UserRoleMap[record.role].label}
        </Tag>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      ellipsis: true,
      copyable: true,
      search: false,
      width: '35%',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      align: 'center',
      fixed: 'right',
      width: '15%',

      render: (text, record, _, action) => (
        <Space size="large">
          <Button
            key="interrupt"
            type="link"
            disabled={!hasAuth}
            className="p-0"
            onClick={() => onOpenCreateOrUpdateModal('update', record)}
          >
            编辑
          </Button>
          <Button
            key="remove"
            type="link"
            danger
            disabled={!hasAuth}
            className="p-0"
            onClick={() => onRemove(record)}
          >
            移除
          </Button>
        </Space>
      ),
    },
  ]
  return (
    <>
      <ProTable<any>
        className="mt-[-16px]"
        columns={columns}
        actionRef={actionRef}
        search={false}
        scroll={{ x: 'max-content', y: 'calc(100vh - 282px)' }}
        ghost
        params={monitorParams}
        debounceTime={800}
        request={getTableRequest}
        // manualRequest
        rowKey="userId"
        options={false}
        pagination={false}
        toolbar={{
          title: (
            <Button
              key="interrupt"
              type="primary"
              disabled={!hasAuth}
              onClick={() => onOpenCreateOrUpdateModal('create')}
            >
              添加
            </Button>
          ),
          filter: (
            <Space>
              <Button
                type="primary"
                shape="circle"
                icon={<SearchOutlined />}
                onClick={() => actionRef.current?.reload(true)}
              />
            </Space>
          ),
        }}
      />
      <ModalForm<FormValue>
        title={modalType === 'create' ? '添加成员' : '编辑成员'}
        form={form}
        submitTimeout={2000}
        autoFocusFirstInput
        onFinish={onUpdateUserInfo}
        open={modalVisible}
        onOpenChange={setModalVisible}
        layout="horizontal"
        width={600}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <ProFormSelect
          width="lg"
          name="userId"
          label="成员"
          placeholder="列表仅展示前100名用户，请用姓名或工号搜索"
          disabled={modalType === 'update'}
          required
          showSearch
          fieldProps={{ filterOption: false }}
          request={onGetUserList}
        />

        <ProFormRadio.Group
          width="lg"
          name="role"
          label="角色"
          placeholder="请选择角色"
          initialValue={EnumUserRole.User}
          required
          options={Object.values(UserRoleMap).map((item) => ({
            label: item.label,
            value: item.value,
          }))}
        />
      </ModalForm>

      {contextHolder}
    </>
  )
}
