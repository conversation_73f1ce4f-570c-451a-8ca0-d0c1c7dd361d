export type EnumToUnion<T extends string> = `${T}`

export interface User {
  email: string
  employeeNo: string
  id: number
  role: EnumUserRole
  teamId: number
  teamName: string
  teamType: string
  userId: number
  username: string
  nickName: string
}

export enum EnumUserRole {
  User = 'user',
  Admin = 'admin',
}

export const UserRoleMap = {
  [EnumUserRole.Admin]: {
    label: '管理员',
    value: EnumUserRole.Admin,
    color: '#dbd6fa',
  },
  [EnumUserRole.User]: {
    label: '普通用户',
    value: EnumUserRole.User,
    color: '#d7f1d4',
  },
}
