import {
  getTeamUserList,
  getUser<PERSON>ist,
  removeUser<PERSON><PERSON>,
  setUser<PERSON><PERSON>,
  useUserList,
} from '@/api/user'
import useUserStore from '@/stores/user'
import type { ActionType, ProTableProps } from '@ant-design/pro-components'
import { Form, Modal, message } from 'antd'
import { debounce } from 'lodash-es'
import { useCallback, useEffect, useRef, useState } from 'react'
import { EnumUserRole, type User } from './setting'

export interface FormValue {
  userId: number
  role: EnumUserRole
  userValue?: string
}

export default function useMemberManagementService() {
  const currentTeam = useUserStore((state) => state.currentTeam)
  const currentRoleAtTeam = useUserStore((state) => state.currentRoleAtTeam)

  const [modal, contextHolder] = Modal.useModal()
  const actionRef = useRef<ActionType>()
  const [form] = Form.useForm<FormValue>()

  const [modalVisible, setModalVisible] = useState<boolean>(false)
  const [modalType, setModalType] = useState<'create' | 'update'>('create')
  const [hasAuth, setHasAuth] = useState<boolean>(false)

  useEffect(() => {
    setHasAuth(currentRoleAtTeam === EnumUserRole.Admin)
  }, [currentRoleAtTeam])

  // 监听以下参数的变化，自动重新加载数据
  const monitorParams = {
    teamId: currentTeam,
  }

  const getTableRequest: ProTableProps<User, any>['request'] = async (
    params,
    sort,
    filter,
  ) => {
    // 表单搜索项会从 params 传入，传递给后端接口。
    console.log(11111, params, sort, filter)

    const msg = await getTeamUserList(params?.teamId)
    return {
      data: msg.users,
      // success 请返回 true，
      // 不然 table 会停止解析数据，即使有数据
      success: true,
      //   total: msg.total,
    }
  }

  const onRemove = async (record: User) => {
    await modal.confirm({
      title: '提示',
      centered: true,
      content: `您确定要移除用户 “${record.username}” 吗？`,
      onOk: async () => {
        await removeUserAuth({
          teamId: currentTeam,
          userId: record.userId || 0,
          teamRoleKind: record.role,
        })
        message.success(`已移除用户 ${record.username}！`)
        actionRef.current?.reload?.()
      },
    })
  }

  const onUpdateUserInfo = async (formValue: FormValue) => {
    console.log(formValue)

    await setUserAuth({
      userId: formValue.userId,
      teamId: currentTeam, // currentTeam,
      role: formValue.role,
    })
    message.success(
      modalType === 'create' ? '已添加新成员！' : '已更新成员信息！',
    )
    actionRef.current?.reload?.()
    return true
  }

  const onOpenCreateOrUpdateModal = async (
    modalType: 'create' | 'update',
    record?: User,
  ) => {
    setModalVisible(true)
    setModalType(modalType)
    if (record) {
      form.setFieldValue(
        'userValue',
        record.username + ' / ' + record.employeeNo,
      )
      form.setFieldValue('userId', record.userId)
      form.setFieldValue('role', record?.role || 'user')
    }
  }

  // 用 useCallback 保证 debounce 实例稳定
  const debouncedFetchUserList = useCallback(
    debounce(async (keyWords: string, callback: (data: any) => void) => {
      const response = await getUserList({
        page: 1,
        size: 100,
        ...(keyWords ? { search: keyWords } : {}),
      })
      const data =
        response?.list?.map((user) => ({
          label: user.chineseName + ' / ' + user.employeeNo,
          value: user.id,
        })) || []
      callback(data)
    }, 500),
    [],
  )

  const onGetUserList = async ({ keyWords }: { keyWords: string }) => {
    return new Promise<any[]>((resolve) => {
      debouncedFetchUserList(keyWords, resolve)
    })
  }

  return {
    getTableRequest,
    actionRef,
    onRemove,
    contextHolder,
    monitorParams,
    onUpdateUserInfo,
    form,
    modalVisible,
    setModalVisible,
    onOpenCreateOrUpdateModal,
    onGetUserList,
    modalType,
    hasAuth,
  }
}
