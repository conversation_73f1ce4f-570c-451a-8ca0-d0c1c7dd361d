import {
  type OnlineDevDetail,
  type OnlineDevItem,
  createOnlineDev,
  getOnlineDevDetail,
  updateOnlineDev,
} from '@/api/onlinedev'
import OnlineDevForm, {
  type OnlineDevFormData,
  type TaskFormRef,
} from '@/components/forms/OnlineDevForm'
import type { ModalType } from '@/types/common'
import {
  convertOnlineDevDetailToFormData,
  convertTrainTaskDetailToFormData,
} from '@/utils/dataConvert'
import { Button, Divider, Drawer, message } from 'antd'
import { forwardRef, useImperativeHandle, useRef, useState } from 'react'

export interface CreateDevDrawerRef {
  showDrawer: (type: ModalType, item?: OnlineDevItem) => void
}

interface CreateDevDrawerProps {
  onSubmit?: (values: OnlineDevDetail) => void
  onDelete?: (item: OnlineDevItem) => void
}

const titleMap = {
  create: '创建开发机',
  edit: '编辑开发机',
  read: '查看开发机',
  copy: '复制开发机',
}

const CreateDevDrawer = forwardRef<CreateDevDrawerRef, CreateDevDrawerProps>(
  ({ onSubmit, onDelete }, ref) => {
    const [open, setOpen] = useState(false)
    const [type, setType] = useState<ModalType>('create')
    const [taskId, setTaskId] = useState<number>()
    const taskFormRef = useRef<TaskFormRef>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [record, setRecord] = useState<OnlineDevItem>()
    const showDrawer = async (type: ModalType, item?: OnlineDevItem) => {
      setType(type)
      if ((type === 'edit' || type === 'read' || type === 'copy') && item) {
        const { data } = await getOnlineDevDetail(item.id)
        const formData = convertOnlineDevDetailToFormData(data)
        console.log('formData', formData)
        setTaskId(item.id)
        setRecord(item)
        setTimeout(() => {
          taskFormRef.current?.setValues(formData)
        }, 0)
      } else {
        taskFormRef.current?.reset()
      }
      console.log('taskFormRef.current', taskFormRef.current)
      setOpen(true)
    }

    const onClose = () => {
      setOpen(false)
    }

    const handleSubmit = () => {
      taskFormRef.current?.submit()
    }

    const onFinish = async (values: OnlineDevDetail) => {
      console.log('提交任务数据:', values)
      setIsLoading(true)
      try {
        if (type === 'create' || type === 'copy') {
          await createOnlineDev(values)
        } else {
          await updateOnlineDev(String(taskId!), values)
        }
        message.success('提交成功')
        onSubmit?.(values)
        onClose()
      } finally {
        setIsLoading(false)
      }
    }

    const onFinishFailed = (errorInfo: any) => {
      console.error('Form validation failed:', errorInfo)
    }

    useImperativeHandle(ref, () => ({
      showDrawer,
    }))

    return (
      <Drawer
        title={titleMap[type]}
        width={'65vw'}
        closable={{ 'aria-label': 'Close Button' }}
        onClose={onClose}
        open={open}
        maskClosable={type === 'read'}
        footer={
          <div className="flex justify-start gap-3">
            <Button type="primary" onClick={handleSubmit} loading={isLoading}>
              {type === 'create' ? '创建' : '保存'}
            </Button>
            <Button onClick={onClose}>取消</Button>
            {type === 'edit' && (
              <>
                <Divider type="vertical" className="h-4" />
                <Button
                  onClick={() => onDelete?.(record!)}
                  color="danger"
                  variant="outlined"
                >
                  删除
                </Button>
              </>
            )}
          </div>
        }
      >
        <OnlineDevForm
          ref={taskFormRef}
          type={type}
          disabled={type === 'read'}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
        />
      </Drawer>
    )
  },
)

export default CreateDevDrawer
