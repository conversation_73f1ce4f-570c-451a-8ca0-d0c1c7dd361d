import {
  EnumOnlineDevState,
  type OnlineDevItem,
  deleteOnlineDev,
  getOnlineDevList,
  startOnlineDev,
  stopOnlineDev,
  useOnlineDevList,
  waitingForRunning,
} from '@/api/onlinedev'
import CachedRadioGroup from '@/components/CachedRadioGroup'
import useUserStore from '@/stores/user'
import { renderCustomFilterDropdown } from '@/utils/useProTableService'
import {
  CodeOutlined,
  LoadingOutlined,
  SearchOutlined,
} from '@ant-design/icons'
import {
  type ActionType,
  type ProColumns,
  ProTable,
  type ProTableProps,
} from '@ant-design/pro-components'
import {
  Button,
  Checkbox,
  Divider,
  Form,
  Input,
  Modal,
  Radio,
  Tag,
  Tooltip,
  message,
  notification,
} from 'antd'
import dayjs from 'dayjs'
import type React from 'react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useLocation } from 'react-router-dom'
import CreateDevDrawer, { type CreateDevDrawerRef } from './CreateDevDrawer'
import { renderEnvironment, renderStatus, useEnterControll } from './util'

export const OnlineDevList: React.FC = () => {
  const [modal, contextHolder] = Modal.useModal()
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)

  const currentTeam = useUserStore((state) => state.currentTeam)
  const createDevDrawerRef = useRef<CreateDevDrawerRef>(null)
  const actionRef = useRef<ActionType>()
  const { enteringDevId, handleEnter, abortController } = useEnterControll(
    async () => actionRef.current?.reload(),
  )
  const [form] = Form.useForm<{
    onlyMy: boolean
    onlyRunning: boolean
    devName: string
  }>()
  const formVal = Form.useWatch((values) => {
    actionRef.current?.reload(true)
    return values
  }, form)

  // 清除 URL 中的 id 参数，防止带到其它页面中去
  useEffect(() => {
    if (searchParams.has('id')) {
      //   setCreateBy(EnumCreateBy.ALL)
      searchParams.delete('id')
      // 强制刷新 URL
      window.history.replaceState(null, '', `?${searchParams.toString()}`)
    }
  }, [searchParams])

  const idFromUrl = location.search.includes('id')
    ? searchParams.get('id')
    : null

  // 监听以下参数的变化，自动重新加载数据
  const monitorParams = {
    teamId: currentTeam,
    onlyMy: formVal?.onlyMy === undefined ? true : formVal.onlyMy,
    onlyRunning: formVal?.onlyRunning || false,
    devName: formVal?.devName || '',
  }

  const getTableRequest: ProTableProps<OnlineDevItem, any>['request'] = async (
    params,
    sort,
    filter,
  ) => {
    // 表单搜索项会从 params 传入，传递给后端接口。
    console.log(11111, params, sort, filter)

    const msg = await getOnlineDevList({
      ...params,
      page: params.current,
      ...(filter.id?.[0] ? { pid: filter.id[0] } : null),
    })

    return {
      data: msg.list,
      // success 请返回 true，
      // 不然 table 会停止解析数据，即使有数据
      success: true,
      total: msg.total,
    }
  }

  // 删除在线开发环境
  const handleDelete = (record: OnlineDevItem) => {
    const isRunning = record.status === 'running'
    const content = isRunning
      ? `开发环境 "${record.devName}" 正在运行中，您确定要强制停止并删除吗？`
      : `您确定要删除开发环境 "${record.devName}" 吗？`

    modal.confirm({
      title: '提示',
      content,
      centered: true,
      onOk: async () => {
        if (isRunning) {
          await stopOnlineDev(record.id)
        }
        await deleteOnlineDev(record.id)
        message.success('删除成功')
        actionRef.current?.reload(true)
      },
      onCancel: () => {},
      okText: '确定',
      cancelText: '取消',
    })
  }

  // 停止在线开发环境
  const handleStop = async (record: OnlineDevItem) => {
    modal.confirm({
      title: '提示',
      content: `您确定要停止开发环境 "${record.devName}" 吗？`,
      centered: true,
      onOk: async () => {
        await stopOnlineDev(record.id)
        message.success('停止成功')
        actionRef.current?.reload(true)
        abortController?.current?.abort()
      },
      okText: '确定',
      cancelText: '取消',
    })
  }

  // 表格列定义
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const columns = useMemo<ProColumns<OnlineDevItem>[]>(() => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        copyable: true,
        ellipsis: true,
        search: false,
        width: 80,
        fixed: 'left',
        filters: true, // 强制开启本地筛选，使defaultFilteredValue生效
        onFilter: (value, record) => true, // 启用的本地筛选不过滤接口的数据
        defaultFilteredValue: idFromUrl ? [idFromUrl] : undefined,
        filterDropdown: (args) =>
          renderCustomFilterDropdown({ ...args, type: 'Input' }),
      },
      {
        title: '名称',
        dataIndex: 'devName',
        key: 'devName',
        fixed: 'left',
        ellipsis: true,
        copyable: true,
        width: 140,
        render: (_, record) => (
          <Button
            type="link"
            className="p-0 h-auto text-left"
            onClick={() => {
              createDevDrawerRef.current?.showDrawer('read', record)
            }}
          >
            {record.devName}
          </Button>
        ),
      },
      {
        title: '运行状态',
        dataIndex: 'status',
        key: 'status',
        width: 90,
        align: 'center',
        render: (_, record) => renderStatus(record.status),
      },
      {
        title: '开发环境',
        dataIndex: 'environment',
        key: 'environment',
        width: 150,
        align: 'center',
        render: (_, record) => renderEnvironment(record.type),
      },
      {
        title: '集群 / 命名空间',
        dataIndex: 'cn',
        key: 'cn',
        ellipsis: true,
        width: 180,
        render: (_, record) => {
          return `${record.clusterName} / ${record.namespace}`
        },
      },

      {
        title: '镜像地址',
        dataIndex: 'imageUrl',
        key: 'imageUrl',
        ellipsis: true,
        width: 180,
      },
      {
        title: '创建人',
        dataIndex: 'createdByUserName',
        key: 'createdByUserName',
        align: 'center',
        width: 80,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        width: 160,
        render: (_, record) => {
          return dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss')
        },
      },
      {
        title: '更新人',
        dataIndex: 'updatedByUserName',
        key: 'updatedByUserName',
        align: 'center',
        width: 80,
        render: (_, record) => {
          return record.updatedByUserName || '-'
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        align: 'center',
        width: 160,
        render: (_, record) => {
          return dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm:ss')
        },
      },
      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        align: 'center',
        fixed: 'right',
        width: 320,
        render: (_, record) => {
          return (
            <div className="flex justify-around items-center">
              <Button
                size="small"
                color="primary"
                variant="link"
                icon={<CodeOutlined />}
                onClick={() => {
                  handleEnter(record)
                }}
                loading={
                  record.status === 'pending' || enteringDevId === record.id
                }
              >
                进入
              </Button>
              <Divider type="vertical" className="h-4" />

              <Button
                size="small"
                color="primary"
                variant="link"
                disabled={record.status === 'shutdown'}
                onClick={() => handleStop(record)}
              >
                停机
              </Button>
              {record.status === 'running' ? (
                <Tooltip
                  placement="topLeft"
                  color="white"
                  arrow={{ pointAtCenter: true }}
                  title={<div className="text-[#666666]">请先停机后再编辑</div>}
                >
                  <Button
                    size="small"
                    color="primary"
                    variant="link"
                    disabled={true}
                  >
                    编辑
                  </Button>
                </Tooltip>
              ) : (
                <Button
                  size="small"
                  color="primary"
                  variant="link"
                  onClick={() => {
                    createDevDrawerRef.current?.showDrawer('edit', record)
                  }}
                >
                  编辑
                </Button>
              )}
              <Button
                size="small"
                color="primary"
                variant="link"
                onClick={() => {
                  createDevDrawerRef.current?.showDrawer('copy', record)
                }}
              >
                复制
              </Button>
              <Button
                size="small"
                color="danger"
                variant="link"
                onClick={() => {
                  handleDelete(record)
                }}
              >
                删除
              </Button>
            </div>
          )
        },
      },
    ]
  }, [enteringDevId])

  return (
    <div>
      <Form
        form={form}
        layout="inline"
        initialValues={{ onlyMy: false, onlyRunning: false, devName: '' }}
      >
        <div className="mb-5 flex justify-between items-center w-full">
          <div>
            <Button
              type="primary"
              onClick={() => {
                createDevDrawerRef.current?.showDrawer('create')
              }}
            >
              创建
            </Button>
            <Form.Item name="onlyMy" className="mb-0 ml-4" noStyle>
              <CachedRadioGroup
                cacheKey="onlineDevList_onlyMy" // 缓存标记
                value={formVal?.onlyMy} // 双向绑定数据
                onChange={(value) => form.setFieldValue('onlyMy', value)} // 更新表单值
                options={[
                  { label: '全部', value: false },
                  { label: '我创建的', value: true },
                ]}
              />
            </Form.Item>
          </div>
          <div className="flex gap-4 items-center">
            <Form.Item name="devName" className="mb-0" noStyle>
              <Input
                className="w-[280px]"
                placeholder="请输入开发机名称"
                allowClear
              />
            </Form.Item>
            <Button
              type="primary"
              shape="circle"
              icon={<SearchOutlined />}
              onClick={() => actionRef.current?.reload(true)}
            />
          </div>
        </div>
      </Form>
      <ProTable<OnlineDevItem>
        ghost
        scroll={{ x: 1300, y: 'calc(100vh - 282px)' }}
        actionRef={actionRef}
        search={false}
        options={false}
        rowKey={'id'}
        columns={columns}
        params={monitorParams}
        debounceTime={500}
        request={getTableRequest}
        pagination={{
          defaultPageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          //   pageSize: size,
          //   current: page,
          //   total: data?.total,
          //   onChange: (page, size) => {
          //     setPage(page)
          //     setSize(size)
          //   },
        }}
      />
      <CreateDevDrawer
        ref={createDevDrawerRef}
        onSubmit={async () => {
          actionRef.current?.reload(true)
        }}
        onDelete={async (record) => {
          handleDelete(record)
        }}
      />
      {contextHolder}
    </div>
  )
}
