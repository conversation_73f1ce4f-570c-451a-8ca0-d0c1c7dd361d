import {
  type EnumOnlineDevEnvironment,
  type EnumOnlineDevState,
  type OnlineDevItem,
  startOnlineDev,
  waitingForRunning,
} from '@/api/onlinedev'
import { Tag, message } from 'antd'
import { useRef, useState } from 'react'

export const renderStatus = (status?: EnumOnlineDevState) => {
  const statusMap: Record<
    EnumOnlineDevState,
    { label: string; color: string }
  > = {
    running: { label: '开机', color: '#52C41A' },
    shutdown: { label: '关机', color: '#F1786D' },
    pending: { label: '启动中', color: '#FAAD14' },
  }
  return (
    <span className="text-[#333333] flex items-center justify-center">
      <div
        className={'rounded-full w-2 h-2 mr-2'}
        style={{ backgroundColor: statusMap?.[status || 'shutdown']?.color }}
      />
      {statusMap[status || 'shutdown']?.label}
    </span>
  )
}

export const renderEnvironment = (type?: EnumOnlineDevEnvironment) => {
  return type === 'jupyter' ? (
    <Tag color="#dbd6fa" className="!text-[#666666] w-32 text-center">
      Jupyter Notebook
    </Tag>
  ) : (
    <Tag color="#d7f1d4" className="!text-[#666666] w-32 text-center">
      Visual Studio Code
    </Tag>
  )
}

export const useEnterControll = (refetch: () => Promise<void>) => {
  const [enteringDevId, setEnteringDevId] = useState<number | null>(null)
  // 进入开发机相关
  const enterDev = async (record: {
    password?: string
    devUrl?: string
  }) => {
    message.success('已为您复制登录密码，2秒后进入开发机，请稍后...')
    navigator.clipboard.writeText(record.password!)
    setTimeout(() => {
      window.open(record.devUrl, '_blank')
    }, 2000)
  }

  const abortController = useRef<AbortController | null>(null)
  const waitStart = async (id: number) => {
    abortController?.current?.abort()
    abortController.current = new AbortController()
    const data = await waitingForRunning(id, abortController?.current.signal)
    enterDev(data)
  }
  const handleStart = async (record: OnlineDevItem) => {
    message.success('正在启动开发机，启动后将自动跳转开发环境，请稍后...')
    await startOnlineDev(record.id)
    refetch()
    await waitStart(record.id)
    refetch()
  }

  // 进入在线开发环境
  const handleEnter = async (record: OnlineDevItem) => {
    setEnteringDevId(record.id)
    try {
      if (record.status === 'running') {
        await enterDev({
          password: record.password,
          devUrl: record.devUrl,
        })
      } else {
        await handleStart(record)
      }
    } finally {
      setEnteringDevId(null)
    }
  }
  return {
    enteringDevId,
    handleEnter,
    abortController,
  }
}
