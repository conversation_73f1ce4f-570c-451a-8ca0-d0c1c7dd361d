import { Card } from '@/components/ui/card'
import ResourceManagement from '@/pages/ResourceManagement/ResourceManagement'
import { SettingOutlined } from '@ant-design/icons'
import { Button, Modal } from 'antd'

import PrometheusChart from '../components/PrometheusChart'
import ResourceUsageBar from '../components/ResourceUsageBar'
import type {
  GroupGpuUtilizationChartData,
  GroupResourceTrendChartData,
} from './types'
import useAdminResourceDashboardService from './useAdminResourceDashboardService'

const AdminResourceDashboard = () => {
  const {
    fetchGroupGpuUtilizationData,
    fetchGroupResourceTrendData,
    quotaData,
    quotaLoading,
    quotaError,
    teamManagementModalOpen,
    openTeamManagementModal,
    closeTeamManagementModal,
    createGpuUtilizationFormatOption,
    createGpuUtilizationAlertRender,
    createResourceTrendFormatOption,
    createResourceTrendAlertRender,
  } = useAdminResourceDashboardService()

  return (
    <>
      {/* 资源使用条形图 */}
      <Card className="w-full border-none p-4 mb-4 text-[#333333]">
        <ResourceUsageBar
          mode="admin"
          loadingDeps={[quotaData]}
          actionButton={
            <Button
              size="small"
              type="link"
              icon={<SettingOutlined />}
              onClick={openTeamManagementModal}
            >
              调整配额
            </Button>
          }
        />
      </Card>

      {/* 双卡片布局 */}
      <div className="flex flex-1 gap-4">
        {/* 第一个卡片：GPU 利用率 */}
        <PrometheusChart<GroupGpuUtilizationChartData>
          title="GPU 利用率"
          className="flex-1"
          height={'calc(100vh - 310px)'}
          fetchPrometheusData={fetchGroupGpuUtilizationData}
          formatOption={createGpuUtilizationFormatOption}
          alertRender={(data, timeRangeText) => {
            const alertData = createGpuUtilizationAlertRender(
              data,
              timeRangeText,
            )
            if (!alertData) return null

            return (
              <div>
                数据解读：{alertData.timeRangeText}，集团的{' '}
                <strong>GPU利用率（均值）</strong> 为{' '}
                {alertData.avgGpuUtilization}%
              </div>
            )
          }}
        />

        {/* 第二个卡片：集团资源使用趋势 */}
        <PrometheusChart<GroupResourceTrendChartData>
          title="任务资源实际使用率"
          desc={
            <>
              <div>任务资源实际使用率 = GPU利用率 * GPU申请率</div>
              <br />
              <div>GPU申请率 = Request / 配额</div>
            </>
          }
          className="flex-1"
          height={'calc(100vh - 310px)'}
          fetchPrometheusData={fetchGroupResourceTrendData}
          formatOption={createResourceTrendFormatOption}
          alertRender={(data, timeRangeText) => {
            const alertData = createResourceTrendAlertRender(
              data,
              timeRangeText,
            )
            if (!alertData) return null

            return (
              <div>
                数据解读：{alertData.timeRangeText}，集团的{' '}
                <strong>任务资源实际使用率（均值）</strong> 为{' '}
                {alertData.formattedValue}
              </div>
            )
          }}
          enabled={!quotaLoading && !!quotaData && !quotaError}
        />
      </div>

      {/* Team Management Modal */}
      <Modal
        title="团队资源管理"
        open={teamManagementModalOpen}
        onCancel={closeTeamManagementModal}
        footer={null}
        width={1400}
        centered
      >
        <ResourceManagement showTeamSection={true} />
      </Modal>
    </>
  )
}

export default AdminResourceDashboard
