/**
 * AdminResourceDashboard 业务相关的类型定义
 */

// 集团 GPU 利用率监控专用的数据类型
export interface GroupGpuUtilizationChartData {
  // ECharts 需要的时间轴数据
  times: string[]

  // ECharts 需要的 GPU 利用率数值数组
  gpuUtilizationData: number[]

  // 计算好的平均值（用于标记线）
  avgGpuUtilization: number

  // alertRender 需要的统计汇总（只保留必要字段）
  summary: {
    avgGpuUtilization: number // GPU 利用率均值，保留2位小数
  }
}

// 集团资源使用趋势专用的数据类型
export interface GroupResourceTrendChartData {
  // ECharts 需要的时间轴数据
  times: string[]

  // ECharts 需要的各指标数值数组
  actualUsageRateData: number[] // 平台资源实际使用率数据（保持字段名不变以兼容UI层）
  gpuRequestRateData: number[] // GPU 申请率数据
  gpuUtilizationData: number[] // GPU 利用率数据

  // 计算好的平均值（用于标记线）
  avgActualUsageRate: number // 平台资源实际使用率均值
  avgGpuRequestRate: number // GPU 申请率均值
  avgGpuUtilization: number // GPU 利用率均值

  // alertRender 需要的统计汇总（只保留必要字段）
  summary: {
    avgActualUsageRate: number // 平台资源实际使用率均值，保留2位小数
  }
}

// 集团资源统计数据接口
export interface GroupResourceStats {
  totalTeams: number
  totalGpuQuota: number
  totalGpuUsed: number
  totalCpuCores: number
  totalCpuUsed: number
  totalMemoryGB: number
  totalMemoryUsed: number
  avgGpuUtilization: number
  avgCpuUtilization: number
  avgMemoryUtilization: number
}
