import {
  type PrometheusData,
  getGroupGpuRequestRateQuery,
  getGroupGpuUtilizationQuery,
  getGroupPlatformResourceUtilizationQuery,
  getPrometheusDetail,
} from '@/api/prometheus'
import { getResourceOverviewDetail } from '@/api/resource'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { useCallback, useState } from 'react'
import {
  calculateAverage,
  extractTimeSeries,
  getDecimalPlaces,
  mergeTimeSeries,
} from '../utils'
import type {
  GroupGpuUtilizationChartData,
  GroupResourceTrendChartData,
} from './types'

// 处理集团 GPU 利用率数据
const processGroupGpuUtilizationData = (
  prometheusData: PrometheusData,
): GroupGpuUtilizationChartData => {
  // 提取时间序列数据
  const timeSeries = extractTimeSeries(prometheusData)

  // 转换为 ECharts 需要的格式
  const times = timeSeries.map((point) =>
    dayjs(point.timestamp).format('YYYY-MM-DD HH:mm:ss'),
  )
  const gpuUtilizationData = timeSeries.map((point) =>
    Number.parseFloat(point.value.toFixed(2)),
  )

  // 计算平均值
  const avgGpuUtilization = calculateAverage(gpuUtilizationData)

  return {
    times,
    gpuUtilizationData,
    avgGpuUtilization,
    summary: {
      avgGpuUtilization,
    },
  }
}

// 处理集团资源使用趋势数据
const processGroupResourceTrendData = (
  gpuRequestRateData: PrometheusData,
  gpuUtilizationData: PrometheusData,
  platformResourceUtilizationData: PrometheusData,
): GroupResourceTrendChartData => {
  // 提取各个指标的时间序列数据
  const gpuRequestRateTimeSeries = extractTimeSeries(gpuRequestRateData)
  const gpuUtilizationTimeSeries = extractTimeSeries(gpuUtilizationData)
  const platformResourceUtilizationTimeSeries = extractTimeSeries(
    platformResourceUtilizationData,
  )

  // 合并时间序列数据
  const mergedData = mergeTimeSeries([
    gpuRequestRateTimeSeries,
    gpuUtilizationTimeSeries,
    platformResourceUtilizationTimeSeries,
  ])

  // 转换为 ECharts 需要的格式（保留原始精度，不进行格式化）
  const times = mergedData.map((point) => point.time)
  const gpuRequestRateDataArray = mergedData.map((point) => point.values[0])
  const gpuUtilizationDataArray = mergedData.map((point) => point.values[1])
  const platformResourceUtilizationDataArray = mergedData.map(
    (point) => point.values[2],
  )

  // 计算平均值
  const avgGpuRequestRate = calculateAverage(gpuRequestRateDataArray)
  const avgGpuUtilization = calculateAverage(gpuUtilizationDataArray)
  const avgPlatformResourceUtilization = calculateAverage(
    platformResourceUtilizationDataArray,
  )

  return {
    times,
    actualUsageRateData: platformResourceUtilizationDataArray, // 平台资源实际使用率数据
    gpuRequestRateData: gpuRequestRateDataArray, // GPU 申请率数据
    gpuUtilizationData: gpuUtilizationDataArray, // GPU 利用率数据
    avgActualUsageRate: avgPlatformResourceUtilization, // 平台资源实际使用率均值
    avgGpuRequestRate, // GPU 申请率均值
    avgGpuUtilization, // GPU 利用率均值
    summary: {
      avgActualUsageRate: avgPlatformResourceUtilization, // 保留原始精度
    },
  }
}

const useAdminResourceDashboardService = () => {
  // 团队管理弹窗状态管理
  const [teamManagementModalOpen, setTeamManagementModalOpen] = useState(false)

  // 动态获取集团配额数据
  const {
    data: quotaData,
    isLoading: quotaLoading,
    error: quotaError,
    refetch: refetchQuotaData,
  } = useQuery({
    queryKey: ['groupQuota'],
    queryFn: () => getResourceOverviewDetail(), // 不传 teamId，获取集团级别数据
    staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
    gcTime: 10 * 60 * 1000, // 缓存10分钟
  })
  // 集团 GPU 利用率数据请求函数
  const fetchGroupGpuUtilizationData = useCallback(
    async (
      start: string,
      end: string,
      step: string,
    ): Promise<GroupGpuUtilizationChartData> => {
      try {
        // 构建查询语句
        const gpuUtilizationQuery = getGroupGpuUtilizationQuery()

        // 请求 Prometheus 数据
        const gpuUtilizationResponse = await getPrometheusDetail(
          gpuUtilizationQuery,
          start,
          end,
          step,
        )

        // 处理真实的 Prometheus 响应数据
        return processGroupGpuUtilizationData(gpuUtilizationResponse.data)
      } catch (error) {
        console.error('集团 GPU 利用率数据请求失败:', error)
        // 抛出错误，不再使用模拟数据作为降级方案
        throw error
      }
    },
    [],
  )

  // 集团资源使用趋势数据请求函数
  const fetchGroupResourceTrendData = useCallback(
    async (
      start: string,
      end: string,
      step: string,
    ): Promise<GroupResourceTrendChartData> => {
      try {
        // 参数验证：检查配额数据是否可用
        if (!quotaData || quotaData.totalQuota === undefined) {
          const errorMessage = '配额数据未加载完成，无法获取 Prometheus 数据'
          console.error(
            '[fetchGroupResourceTrendData] 配额数据验证失败:',
            errorMessage,
          )
          throw new Error(errorMessage)
        }

        const teamQuota = quotaData.totalQuota // 使用动态获取的真实配额
        // 构建查询语句
        const gpuRequestRateQuery = getGroupGpuRequestRateQuery(teamQuota)
        const gpuUtilizationQuery = getGroupGpuUtilizationQuery()
        const platformResourceUtilizationQuery =
          getGroupPlatformResourceUtilizationQuery(teamQuota)

        // 并行请求三个指标
        const [
          gpuRequestRateResponse,
          gpuUtilizationResponse,
          platformResourceUtilizationResponse,
        ] = await Promise.all([
          getPrometheusDetail(gpuRequestRateQuery, start, end, step),
          getPrometheusDetail(gpuUtilizationQuery, start, end, step),
          getPrometheusDetail(
            platformResourceUtilizationQuery,
            start,
            end,
            step,
          ),
        ])
        // 处理真实的 Prometheus 响应数据
        return processGroupResourceTrendData(
          gpuRequestRateResponse.data,
          gpuUtilizationResponse.data,
          platformResourceUtilizationResponse.data,
        )
      } catch (error) {
        console.error('集团资源使用趋势数据请求失败:', error)
        // 抛出错误，不再使用模拟数据作为降级方案
        throw error
      }
    },
    [quotaData],
  )

  // 弹窗控制函数
  const openTeamManagementModal = useCallback(() => {
    setTeamManagementModalOpen(true)
  }, [])

  const closeTeamManagementModal = useCallback(() => {
    setTeamManagementModalOpen(false)
    // 配额弹窗关闭后重新获取配额数据，触发相关组件数据同步
    refetchQuotaData()
  }, [refetchQuotaData])

  // 创建 GPU 利用率图表格式化配置
  const createGpuUtilizationFormatOption = useCallback(
    (chartData: GroupGpuUtilizationChartData) => {
      const {
        times = [],
        gpuUtilizationData = [],
        avgGpuUtilization = 0,
      } = chartData || {}

      return {
        tooltip: {
          trigger: 'axis' as const,
          formatter: (params: any) => {
            if (Array.isArray(params)) {
              // 提取所有数值用于统一精度计算
              const values = params.map((param: any) =>
                Number.parseFloat(param.value),
              )
              const maxDecimalPlaces = Math.min(
                Math.max(...values.map(getDecimalPlaces)),
                4,
              )

              let result = `<div style="margin-bottom: 6px; font-weight: bold;">${params[0].axisValue}</div>`
              result +=
                '<table style="width: 100%; border-collapse: collapse;">'
              for (const param of params) {
                const formattedValue = Number.parseFloat(param.value).toFixed(
                  maxDecimalPlaces,
                )
                result += `
                <tr>
                  <td style="text-align: left; padding: 2px 0; white-space: nowrap;">
                    ${param.marker}${param.seriesName}
                  </td>
                  <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                    ${formattedValue}%
                  </td>
                </tr>
              `
              }
              result += '</table>'
              return result
            }

            const value = Number.parseFloat(params.value)
            const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
            const formattedValue = value.toFixed(decimalPlaces)

            return `
            <div style="margin-bottom: 6px; font-weight: bold;">${params.axisValue}</div>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="text-align: left; padding: 2px 0; white-space: nowrap;">
                  ${params.marker}${params.seriesName}
                </td>
                <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                  ${formattedValue}%
                </td>
              </tr>
            </table>
          `
          },
        },
        legend: {
          top: 'top',
          left: 'left',
          data: ['GPU 利用率'],
        },
        grid: {
          left: '10%',
          right: '5%',
          top: '10%',
          bottom: '10%',
        },
        xAxis: {
          type: 'category' as const,
          data: times,
        },
        yAxis: {
          type: 'value' as const,
          //   min: (() => {
          //     if (gpuUtilizationData.length === 0) return 0
          //     const dataMin = Math.min(...gpuUtilizationData)
          //     return Math.floor((dataMin * 0.9) / 5) * 5
          //   })(),
          //   max: (() => {
          //     if (gpuUtilizationData.length === 0) return 100
          //     const dataMax = Math.max(...gpuUtilizationData)
          //     return Math.ceil((dataMax * 1.1) / 5) * 5
          //   })(),
          axisLabel: {
            formatter: (value: number) => value + ' %',
            //   formatter: (value: number) => Math.round(value) + ' %',
          },
        },
        series: [
          {
            name: 'GPU 利用率',
            data: gpuUtilizationData,
            type: 'line' as const,
            color: '#7367EF',
            markLine: {
              data: [
                {
                  name: '平均值',
                  type: 'average' as const,
                  lineStyle: { color: '#7367EF', type: 'dashed' as const },
                  label: {
                    show: true,
                    position: 'start' as const,
                    offset: [204, 0],
                    fontSize: 14,
                    height: 24,
                    backgroundColor: '#fafafacc',
                    color: '#666',
                    formatter: (() => {
                      const avgValue = avgGpuUtilization
                      const decimalPlaces = Math.min(
                        getDecimalPlaces(avgValue),
                        4,
                      )
                      const formattedValue = avgValue.toFixed(decimalPlaces)
                      return `利用率均值：${formattedValue}%`
                    })(),
                  },
                },
              ],
            },
            markPoint: {
              data: [
                { type: 'max' as const, name: 'Max' },
                { type: 'min' as const, name: 'Min' },
              ],
              label: {
                formatter: (params: any) => {
                  const value = params.value
                  const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
                  const formattedValue = value.toFixed(decimalPlaces)
                  return `${params.name}\n${formattedValue}%`
                },
                textAlign: 'center' as const,
                verticalAlign: 'middle' as const,
              },
            },
            symbol: 'none',
          },
        ],
      }
    },
    [],
  )

  // 创建 GPU 利用率图表警告渲染函数
  const createGpuUtilizationAlertRender = useCallback(
    (data: any, timeRangeText: string) => {
      if (!data?.summary) return null
      const { avgGpuUtilization } = data.summary

      return {
        timeRangeText,
        avgGpuUtilization: avgGpuUtilization.toFixed(2),
      }
    },
    [],
  )

  // 创建资源使用趋势图表格式化配置
  const createResourceTrendFormatOption = useCallback(
    (chartData: GroupResourceTrendChartData) => {
      const {
        times = [],
        actualUsageRateData = [],
        gpuRequestRateData = [],
        gpuUtilizationData = [],
        avgActualUsageRate = 0,
        avgGpuRequestRate = 0,
        avgGpuUtilization = 0,
      } = chartData || {}

      return {
        tooltip: {
          trigger: 'axis' as const,
          formatter: (params: any) => {
            if (Array.isArray(params)) {
              // 提取所有数值用于统一精度计算
              const values = params.map((param: any) =>
                Number.parseFloat(param.value),
              )
              const maxDecimalPlaces = Math.min(
                Math.max(...values.map(getDecimalPlaces)),
                4,
              )

              let result = `<div style="margin-bottom: 6px; font-weight: bold;">${params[0].axisValue}</div>`
              result +=
                '<table style="width: 100%; border-collapse: collapse;">'
              for (const param of params) {
                const formattedValue = Number.parseFloat(param.value).toFixed(
                  maxDecimalPlaces,
                )
                result += `
                <tr>
                  <td style="text-align: left; padding: 2px 0; white-space: nowrap;">
                    ${param.marker}${param.seriesName}
                  </td>
                  <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                    ${formattedValue}%
                  </td>
                </tr>
              `
              }
              result += '</table>'
              return result
            }

            const value = Number.parseFloat(params.value)
            const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
            const formattedValue = value.toFixed(decimalPlaces)

            return `
            <div style="margin-bottom: 6px; font-weight: bold;">${params.axisValue}</div>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="text-align: left; padding: 2px 0; white-space: nowrap;">
                  ${params.marker}${params.seriesName}
                </td>
                <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                  ${formattedValue}%
                </td>
              </tr>
            </table>
          `
          },
        },
        legend: {
          top: 'top',
          left: 'left',
          data: ['任务资源实际使用率', 'GPU 利用率', 'GPU 申请率'],
        },
        grid: {
          left: '10%',
          right: '5%',
          top: '10%',
          bottom: '10%',
        },
        xAxis: {
          type: 'category' as const,
          data: times,
        },
        yAxis: {
          type: 'value' as const,
          //   min: (() => {
          //     const allData = [
          //       ...actualUsageRateData,
          //       ...gpuUtilizationData,
          //       ...gpuRequestRateData,
          //     ]
          //     if (allData.length === 0) return 0
          //     const dataMin = Math.min(...allData)
          //     return Math.floor((dataMin * 0.9) / 5) * 5
          //   })(),
          //   max: (() => {
          //     const allData = [
          //       ...actualUsageRateData,
          //       ...gpuUtilizationData,
          //       ...gpuRequestRateData,
          //     ]
          //     if (allData.length === 0) return 100
          //     const dataMax = Math.max(...allData)
          //     return Math.ceil((dataMax * 1.1) / 5) * 5
          //   })(),
          axisLabel: {
            formatter: (value: number) => value + ' %',
            //   formatter: (value: number) => Math.round(value) + ' %',
          },
        },
        series: [
          {
            name: '任务资源实际使用率',
            data: actualUsageRateData,
            type: 'line' as const,
            color: '#7367EF',
            markLine: {
              data: [
                {
                  name: '平均值',
                  type: 'average' as const,
                  lineStyle: { color: '#7367EF', type: 'dashed' as const },
                  label: {
                    show: true,
                    position: 'start' as const,
                    offset: [204, 0],
                    fontSize: 14,
                    height: 24,
                    backgroundColor: '#fafafacc',
                    color: '#666',
                    formatter: (() => {
                      const avgValue = avgActualUsageRate
                      const decimalPlaces = Math.min(
                        getDecimalPlaces(avgValue),
                        4,
                      )
                      const formattedValue = avgValue.toFixed(decimalPlaces)
                      return `任务资源实际使用率均值：${formattedValue}%`
                    })(),
                  },
                },
              ],
            },
            markPoint: {
              data: [
                { type: 'max' as const, name: 'Max' },
                { type: 'min' as const, name: 'Min' },
              ],
              label: {
                formatter: (params: any) => {
                  const value = params.value
                  const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
                  const formattedValue = value.toFixed(decimalPlaces)
                  return `${params.name}\n${formattedValue}%`
                },
                textAlign: 'center' as const,
                verticalAlign: 'middle' as const,
              },
            },
            symbol: 'none',
          },
          {
            name: 'GPU 利用率',
            data: gpuUtilizationData,
            type: 'line' as const,
            color: '#1FD0D5',
            symbol: 'none',
            markPoint: {
              data: [
                { type: 'max' as const, name: 'Max' },
                { type: 'min' as const, name: 'Min' },
              ],
              label: {
                formatter: (params: any) => {
                  const value = params.value
                  const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
                  const formattedValue = value.toFixed(decimalPlaces)
                  return `${params.name}\n${formattedValue}%`
                },
                textAlign: 'center' as const,
                verticalAlign: 'middle' as const,
              },
            },
          },
          {
            name: 'GPU 申请率',
            data: gpuRequestRateData,
            type: 'line' as const,
            color: '#52C41A',
            symbol: 'none',
            markPoint: {
              data: [
                { type: 'max' as const, name: 'Max' },
                { type: 'min' as const, name: 'Min' },
              ],
              label: {
                formatter: (params: any) => {
                  const value = params.value
                  const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
                  const formattedValue = value.toFixed(decimalPlaces)
                  return `${params.name}\n${formattedValue}%`
                },
                textAlign: 'center' as const,
                verticalAlign: 'middle' as const,
              },
            },
          },
        ],
      }
    },
    [],
  )

  // 创建资源使用趋势图表警告渲染函数
  const createResourceTrendAlertRender = useCallback(
    (data: any, timeRangeText: string) => {
      if (!data?.summary) return null
      const { avgActualUsageRate } = data.summary

      // 直接限制精度格式化（最大4位小数）
      const decimalPlaces = Math.min(getDecimalPlaces(avgActualUsageRate), 4)
      const formattedValue = `${avgActualUsageRate.toFixed(decimalPlaces)}%`

      return {
        timeRangeText,
        formattedValue,
      }
    },
    [],
  )

  return {
    // 配额数据
    quotaData,
    quotaLoading,
    quotaError,
    refetchQuotaData,

    // 双卡片模块的数据请求函数
    fetchGroupGpuUtilizationData,
    fetchGroupResourceTrendData,

    // 弹窗状态管理
    teamManagementModalOpen,
    openTeamManagementModal,
    closeTeamManagementModal,

    // 图表配置函数
    createGpuUtilizationFormatOption,
    createGpuUtilizationAlertRender,
    createResourceTrendFormatOption,
    createResourceTrendAlertRender,
  }
}

export default useAdminResourceDashboardService
