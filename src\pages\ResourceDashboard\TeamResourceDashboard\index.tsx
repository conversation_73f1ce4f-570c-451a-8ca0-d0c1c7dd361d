import { Card } from '@/components/ui/card'
import TeamResourceManagement from '@/pages/ResourceManagement/TeamResourceManagement'
import { SettingOutlined } from '@ant-design/icons'
import { Button, Modal, Skeleton } from 'antd'

import PrometheusChart from '../components/PrometheusChart'
import ResourceUsageBar from '../components/ResourceUsageBar'
import type { TeamPrometheusChartData } from './types'
import useTeamResourceDashboardService from './useTeamResourceDashboardService'

export default () => {
  const {
    currentTeam,
    currentRoleAtTeam,
    fetchPrometheusData,
    quotaData,
    quotaLoading,
    quotaError,
    createTeamChartFormatOption,
    createTeamAlertRender,
    gpuQuotaDialogOpen,
    openGpuQuotaDialog,
    closeGpuQuotaDialog,
  } = useTeamResourceDashboardService()

  return currentTeam ? (
    <>
      {/* 资源使用可视化 */}
      <Card className="w-full border-none p-4 text-[#333333] mb-4">
        <ResourceUsageBar
          mode="team"
          teamId={currentTeam}
          loadingDeps={[currentTeam, quotaData]}
          actionButton={
            currentRoleAtTeam === 'admin' && (
              <Button
                size="small"
                type="link"
                icon={<SettingOutlined />}
                onClick={openGpuQuotaDialog}
              >
                调整配额
              </Button>
            )
          }
        />
      </Card>

      {/* 资源使用趋势图表 */}
      <PrometheusChart<TeamPrometheusChartData>
        title="任务资源实际使用率"
        desc={
          <>
            <div>任务资源实际使用率 = GPU利用率 * GPU申请率</div>
            <br />
            <div>GPU申请率 = Request / 配额</div>
          </>
        }
        fetchPrometheusData={fetchPrometheusData}
        formatOption={createTeamChartFormatOption}
        alertRender={(data, timeRangeText) => {
          const alertData = createTeamAlertRender(data, timeRangeText)
          if (!alertData) return null

          return (
            <div>
              数据解读：{timeRangeText}，团队的{' '}
              <strong>任务资源实际使用率（均值）</strong> 为{' '}
              {alertData.formattedActualUsage}， 其中 GPU利用率（均值） 为{' '}
              {alertData.formattedGpuUtilization}，GPU申请率（均值） 为{' '}
              {alertData.formattedGpuRequest}。
            </div>
          )
        }}
        height={'calc(100vh - 310px)'}
        loadingDeps={[currentTeam, quotaData]}
        enabled={!quotaLoading && !!quotaData && !quotaError}
      />

      {/* GPU Quota Dialog */}
      <Modal
        title="调整GPU配额"
        open={gpuQuotaDialogOpen}
        onCancel={closeGpuQuotaDialog}
        footer={null}
        width={800}
      >
        <TeamResourceManagement
          teamId={currentTeam}
          showClusterSection={false}
        />
      </Modal>
    </>
  ) : (
    <Skeleton />
  )
}
