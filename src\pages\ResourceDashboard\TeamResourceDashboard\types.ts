/**
 * TeamResourceDashboard 业务相关的类型定义
 */

// TeamResourceDashboard 专用的 Prometheus 图表数据类型
// 直接包含 ECharts 需要的数据格式，减少组件层的数据处理
export interface TeamPrometheusChartData {
  // ECharts 需要的时间轴数据
  times: string[]

  // ECharts 需要的各指标数值数组
  actualUsageData: number[]
  gpuUtilizationData: number[]
  gpuRequestData: number[]

  // 计算好的平均值（用于标记线）
  avgActualUsage: number

  // alertRender 需要的统计汇总
  summary: {
    avgActualUsageRate: number
    avgGpuUtilization: number
    avgGpuRequestRate: number
  }
}

// 资源使用数据接口（从 ResourceUsageBar 导入）
export type { ResourceUsageData } from '../components/ResourceUsageBar'
