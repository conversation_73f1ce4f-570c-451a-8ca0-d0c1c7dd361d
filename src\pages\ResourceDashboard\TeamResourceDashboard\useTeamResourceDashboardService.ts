import {
  type PrometheusData,
  getPrometheusDetail,
  getTeamActualUsageRateQuery,
  getTeamGpuRequestRateQuery,
  getTeamGpuUtilizationQuery,
} from '@/api/prometheus'
import { getResourceOverviewDetail } from '@/api/resource'
import useUserStore from '@/stores/user'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import {
  calculateAverage,
  extractTimeSeries,
  getDecimalPlaces,
  mergeTimeSeries,
} from '../utils'
import type { TeamPrometheusChartData } from './types'

export default function useTeamResourceDashboardService() {
  const currentTeam = useUserStore((state) => state.currentTeam)
  const currentRoleAtTeam = useUserStore((state) => state.currentRoleAtTeam)

  // GPU 配额弹窗状态管理
  const [gpuQuotaDialogOpen, setGpuQuotaDialogOpen] = useState(false)

  // 动态获取团队配额数据
  const {
    data: quotaData,
    isLoading: quotaLoading,
    error: quotaError,
    refetch: refetchQuotaData,
  } = useQuery({
    queryKey: ['teamQuota', currentTeam],
    queryFn: () => getResourceOverviewDetail(currentTeam),
    enabled: !!currentTeam, // 只有当 currentTeam 存在时才执行查询
    staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
    gcTime: 10 * 60 * 1000, // 缓存10分钟
  })

  // 计算使用率
  const calculateUsageRate = useCallback(
    (used: number, total: number): number => {
      return total > 0 ? Math.round((used / total) * 100) : 0
    },
    [],
  )

  // 格式化存储大小
  const formatStorageSize = useCallback((sizeInGB: number): string => {
    if (sizeInGB >= 1024) {
      return `${(sizeInGB / 1024).toFixed(1)} TB`
    }
    return `${sizeInGB} GB`
  }, [])

  // 获取状态颜色
  const getStatusColor = useCallback((usage: number): string => {
    if (usage >= 90) return '#ff4d4f' // 红色 - 危险
    if (usage >= 80) return '#faad14' // 橙色 - 警告
    if (usage >= 70) return '#1890ff' // 蓝色 - 正常高使用率
    return '#52c41a' // 绿色 - 正常
  }, [])

  // 处理 Prometheus 响应数据
  const processPrometheusResponses = useCallback(
    (
      gpuUtilizationResponse: PrometheusData,
      gpuRequestRateResponse: PrometheusData,
      actualUsageRateResponse: PrometheusData,
    ): TeamPrometheusChartData => {
      // 使用通用工具函数提取时间序列数据
      const gpuUtilizationTimeSeries = extractTimeSeries(gpuUtilizationResponse)
      const gpuRequestRateTimeSeries = extractTimeSeries(gpuRequestRateResponse)
      const actualUsageRateTimeSeries = extractTimeSeries(
        actualUsageRateResponse,
      )

      // 使用通用工具函数合并时间序列数据
      const mergedData = mergeTimeSeries([
        gpuUtilizationTimeSeries,
        gpuRequestRateTimeSeries,
        actualUsageRateTimeSeries,
      ])

      // 提取 ECharts 需要的数据格式（保留原始精度，不进行格式化）
      const times = mergedData.map((point) => point.time)
      const gpuUtilizationData = mergedData.map((point) => point.values[0])
      const gpuRequestData = mergedData.map((point) => point.values[1])
      const actualUsageData = mergedData.map((point) => point.values[2])

      // 使用通用工具函数计算平均值
      const avgActualUsage = calculateAverage(actualUsageData)

      return {
        times,
        actualUsageData,
        gpuUtilizationData,
        gpuRequestData,
        avgActualUsage,
        summary: {
          avgActualUsageRate: avgActualUsage,
          avgGpuUtilization:
            gpuUtilizationData.length > 0
              ? gpuUtilizationData.reduce((a, b) => a + b, 0) /
                gpuUtilizationData.length
              : 0,
          avgGpuRequestRate:
            gpuRequestData.length > 0
              ? gpuRequestData.reduce((a, b) => a + b, 0) /
                gpuRequestData.length
              : 0,
        },
      }
    },
    [],
  )

  // Prometheus 数据请求函数
  const fetchPrometheusData = useCallback(
    async (
      start: string,
      end: string,
      step: string,
    ): Promise<TeamPrometheusChartData> => {
      try {
        // 参数验证：检查团队参数是否存在
        if (!currentTeam || currentTeam === null || currentTeam === undefined) {
          const errorMessage = '团队参数缺失，无法获取数据'
          console.error('[fetchPrometheusData] 参数验证失败:', errorMessage)
          throw new Error(errorMessage)
        }

        // 参数验证：检查配额数据是否可用
        if (!quotaData || quotaData.totalQuota === undefined) {
          const errorMessage = '配额数据未加载完成，无法获取 Prometheus 数据'
          console.error('[fetchPrometheusData] 配额数据验证失败:', errorMessage)
          throw new Error(errorMessage)
        }

        // 获取团队信息和真实配额
        const teamId = currentTeam.toString()
        const teamQuota = quotaData.totalQuota // 使用动态获取的真实配额

        // 构建查询语句
        const gpuUtilizationQuery = getTeamGpuUtilizationQuery(teamId)
        const gpuRequestRateQuery = getTeamGpuRequestRateQuery(
          teamId,
          teamQuota,
        )
        const actualUsageRateQuery = getTeamActualUsageRateQuery(
          teamId,
          teamQuota,
        )

        // 并行请求三个指标
        const [
          gpuUtilizationResponse,
          gpuRequestRateResponse,
          actualUsageRateResponse,
        ] = await Promise.all([
          getPrometheusDetail(gpuUtilizationQuery, start, end, step),
          getPrometheusDetail(gpuRequestRateQuery, start, end, step),
          getPrometheusDetail(actualUsageRateQuery, start, end, step),
        ])
        // 处理和合并数据
        // 注意：新的 API 函数返回的数据结构中，实际数据在 .data 属性中
        return processPrometheusResponses(
          gpuUtilizationResponse.data,
          gpuRequestRateResponse.data,
          actualUsageRateResponse.data,
        )
      } catch (error) {
        console.error('Prometheus 数据请求失败:', error)
        // 抛出错误，不再使用模拟数据作为降级方案
        throw error
      }
    },
    [currentTeam, quotaData, processPrometheusResponses],
  )

  // 创建团队图表格式化配置
  const createTeamChartFormatOption = useCallback(
    (chartData: TeamPrometheusChartData) => {
      // 直接使用 Service 层处理好的数据格式
      const {
        times = [],
        actualUsageData = [],
        gpuUtilizationData = [],
        gpuRequestData = [],
        avgActualUsage = 0,
      } = chartData || {}

      return {
        tooltip: {
          trigger: 'axis' as const,
          formatter: (params: any) => {
            if (Array.isArray(params)) {
              // 提取所有数值用于统一精度计算
              const values = params.map((param: any) =>
                Number.parseFloat(param.value),
              )
              const maxDecimalPlaces = Math.min(
                Math.max(...values.map(getDecimalPlaces)),
                4,
              )

              let result = `<div style="margin-bottom: 6px; font-weight: bold;">${params[0].axisValue}</div>`
              result +=
                '<table style="width: 100%; border-collapse: collapse;">'
              for (const param of params) {
                const formattedValue = Number.parseFloat(param.value).toFixed(
                  maxDecimalPlaces,
                )
                result += `
                <tr>
                  <td style="text-align: left; padding: 2px 0; white-space: nowrap;">
                    ${param.marker}${param.seriesName}
                  </td>
                  <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                    ${formattedValue}%
                  </td>
                </tr>
              `
              }
              result += '</table>'
              return result
            }

            const value = Number.parseFloat(params.value)
            const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
            const formattedValue = value.toFixed(decimalPlaces)

            return `
            <div style="margin-bottom: 6px; font-weight: bold;">${params.axisValue}</div>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="text-align: left; padding: 2px 0; white-space: nowrap;">
                  ${params.marker}${params.seriesName}
                </td>
                <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                  ${formattedValue}%
                </td>
              </tr>
            </table>
          `
          },
        },
        legend: {
          top: 'top',
          left: 'left',
          data: ['任务资源实际使用率', 'GPU 利用率', 'GPU 申请率'],
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '10%',
        },
        xAxis: {
          type: 'category' as const,
          data: times,
        },
        yAxis: {
          type: 'value' as const,
          //   min: (() => {
          //     const allData = [
          //       ...actualUsageData,
          //       ...gpuUtilizationData,
          //       ...gpuRequestData,
          //     ]
          //     if (allData.length === 0) return 0
          //     const dataMin = Math.min(...allData)
          //     return Math.floor((dataMin * 0.9) / 5) * 5
          //   })(),
          //   max: (() => {
          //     const allData = [
          //       ...actualUsageData,
          //       ...gpuUtilizationData,
          //       ...gpuRequestData,
          //     ]
          //     if (allData.length === 0) return 100
          //     const dataMax = Math.max(...allData)
          //     return Math.ceil((dataMax * 1.1) / 5) * 5
          //   })(),
          axisLabel: {
            formatter: (value: number) => value + ' %',
            //   formatter: (value: number) => Math.round(value) + ' %',
          },
        },
        series: [
          {
            name: '任务资源实际使用率',
            data: actualUsageData,
            type: 'line' as const,
            color: '#7367EF',
            markLine: {
              data: [
                {
                  name: '平均值',
                  type: 'average' as const,
                  lineStyle: { color: '#7367EF', type: 'dashed' as const },
                  label: {
                    show: true,
                    position: 'start' as const,
                    offset: [204, 0],
                    fontSize: 14,
                    height: 24,
                    backgroundColor: '#fafafacc',
                    color: '#666',
                    formatter: (() => {
                      const avgValue = avgActualUsage
                      // 直接限制精度（最大4位小数）
                      const decimalPlaces = Math.min(
                        getDecimalPlaces(avgValue),
                        4,
                      )
                      const formattedValue = avgValue.toFixed(decimalPlaces)
                      return `任务资源实际使用率均值：${formattedValue}%`
                    })(),
                  },
                },
              ],
            },
            markPoint: {
              data: [
                { type: 'max' as const, name: 'Max' },
                { type: 'min' as const, name: 'Min' },
              ],
              label: {
                formatter: (params: any) => {
                  const value = params.value
                  // 直接限制精度（最大4位小数）
                  const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
                  const formattedValue = value.toFixed(decimalPlaces)
                  return `${params.name}\n${formattedValue}%`
                },
                textAlign: 'center' as const,
                verticalAlign: 'middle' as const,
              },
            },
            symbol: 'none',
          },

          {
            name: 'GPU 利用率',
            data: gpuUtilizationData,
            type: 'line' as const,
            color: '#1FD0D5',
            symbol: 'none',
            markPoint: {
              data: [
                { type: 'max' as const, name: 'Max' },
                { type: 'min' as const, name: 'Min' },
              ],
              label: {
                formatter: (params: any) => {
                  const value = params.value
                  // 直接限制精度（最大4位小数）
                  const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
                  const formattedValue = value.toFixed(decimalPlaces)
                  return `${params.name}\n${formattedValue}%`
                },
                textAlign: 'center' as const,
                verticalAlign: 'middle' as const,
              },
            },
          },
          {
            name: 'GPU 申请率',
            data: gpuRequestData,
            type: 'line' as const,
            color: '#52C41A',
            symbol: 'none',
            markPoint: {
              data: [
                { type: 'max' as const, name: 'Max' },
                { type: 'min' as const, name: 'Min' },
              ],
              label: {
                formatter: (params: any) => {
                  const value = params.value
                  // 直接限制精度（最大4位小数）
                  const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
                  const formattedValue = value.toFixed(decimalPlaces)
                  return `${params.name}\n${formattedValue}%`
                },
                textAlign: 'center' as const,
                verticalAlign: 'middle' as const,
              },
            },
          },
        ],
      }
    },
    [],
  )

  // 创建团队图表警告渲染函数
  const createTeamAlertRender = useCallback(
    (data: any, timeRangeText: string) => {
      if (!data?.summary) return null
      const { avgActualUsageRate, avgGpuUtilization, avgGpuRequestRate } =
        data.summary

      // 直接限制精度格式化（最大4位小数）
      const actualUsageDecimalPlaces = Math.min(
        getDecimalPlaces(avgActualUsageRate),
        4,
      )
      const formattedActualUsage = `${avgActualUsageRate.toFixed(actualUsageDecimalPlaces)}%`

      const gpuRequestDecimalPlaces = Math.min(
        getDecimalPlaces(avgGpuRequestRate),
        4,
      )
      const formattedGpuRequest = `${avgGpuRequestRate.toFixed(gpuRequestDecimalPlaces)}%`

      const gpuUtilizationDecimalPlaces = Math.min(
        getDecimalPlaces(avgGpuUtilization),
        4,
      )
      const formattedGpuUtilization = `${avgGpuUtilization.toFixed(gpuUtilizationDecimalPlaces)}%`

      return {
        timeRangeText,
        formattedActualUsage,
        formattedGpuRequest,
        formattedGpuUtilization,
      }
    },
    [],
  )

  // 弹窗控制函数
  const openGpuQuotaDialog = useCallback(() => {
    setGpuQuotaDialogOpen(true)
  }, [])

  const closeGpuQuotaDialog = useCallback(() => {
    setGpuQuotaDialogOpen(false)
    // 配额弹窗关闭后重新获取配额数据，触发相关组件数据同步
    refetchQuotaData()
  }, [refetchQuotaData])

  return {
    // 工具方法
    calculateUsageRate,
    formatStorageSize,
    getStatusColor,

    // 用户信息
    currentTeam,
    currentRoleAtTeam,

    // 配额数据
    quotaData,
    quotaLoading,
    quotaError,
    refetchQuotaData,

    // 数据请求函数
    fetchPrometheusData,

    // 图表配置函数
    createTeamChartFormatOption,
    createTeamAlertRender,

    // 弹窗状态管理
    gpuQuotaDialogOpen,
    openGpuQuotaDialog,
    closeGpuQuotaDialog,
  }
}
