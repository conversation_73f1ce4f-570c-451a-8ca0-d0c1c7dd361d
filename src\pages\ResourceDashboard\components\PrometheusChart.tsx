import SectionTitle from '@/components/common/SectionTitle'
import { Card } from '@/components/ui/card'
import { CompressOutlined, ExpandOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { Alert, Button, DatePicker } from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { EChartsOption } from 'echarts'
import ReactECharts from 'echarts-for-react'
import type React from 'react'
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'

// 时间范围选项
export interface TimeRangeOption {
  label: string
  value: string
  minutes: number
}

// 组件属性接口
export interface PrometheusChartProps<T = any> {
  title?: string | React.ReactNode // 图表标题
  desc?: string | React.ReactNode // 图表描述（鼠标悬停显示）
  className?: string // 外部传入的样式类
  formatOption?: (chartData: T) => EChartsOption // 图表数据格式化函数
  fetchPrometheusData?: (start: string, end: string, step: string) => Promise<T> // 外部数据请求函数
  alertRender?: (data: T, timeRangeText: string) => React.ReactNode // 统计信息处理函数，新增时间范围文本参数
  height?: number | string // 组件总高度，默认400px，支持数字或CSS calc表达式
  loadingDeps?: any[] // 依赖项数组，当数组中的任何数据发生变化时，自动触发重新调用 fetchPrometheusData 函数
  enabled?: boolean // 是否启用查询，默认为 true。当设置为 false 时，将禁用 Prometheus 数据查询，即使其他条件满足也不会执行查询
  showTimeSelector?: boolean // 是否显示时间选择器，默认为 true
  dateRange?: [Dayjs, Dayjs] | null // 外部控制的时间范围，当提供时会覆盖内部状态
  onDateRangeChange?: (dateRange: [Dayjs, Dayjs] | null) => void // 时间范围变化回调
  onFullscreenChange?: (isFullscreen: boolean) => void // 全屏状态变化回调
}

// 预设时间范围选项
const TIME_PRESETS = [
  {
    label: '最近 30 分钟',
    value: () => [dayjs().subtract(30, 'minute'), dayjs()],
  },
  { label: '最近 6 小时', value: () => [dayjs().subtract(6, 'hour'), dayjs()] },
  {
    label: '最近 24 小时',
    value: () => [dayjs().subtract(24, 'hour'), dayjs()],
  },
  { label: '最近 3 天', value: () => [dayjs().subtract(3, 'day'), dayjs()] },
  { label: '最近 7 天', value: () => [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近 14 天', value: () => [dayjs().subtract(14, 'day'), dayjs()] },
]

const PrometheusChart = forwardRef(
  <T = any>(
    {
      title = '请传入图表标题',
      desc,
      formatOption,
      fetchPrometheusData,
      alertRender,
      className = '',
      height = 400,
      loadingDeps = [], // 默认为空数组，保持向后兼容性
      enabled, // 外部控制查询启用状态，默认为 undefined（等同于 true）
      showTimeSelector = true, // 默认显示时间选择器
      dateRange: externalDateRange, // 外部传入的时间范围
      onDateRangeChange, // 时间范围变化回调
      onFullscreenChange, // 全屏状态变化回调
    }: PrometheusChartProps<T>,
    ref: React.Ref<any>,
  ) => {
    // 状态管理
    const [internalDateRange, setInternalDateRange] = useState<
      [Dayjs, Dayjs] | null
    >(() => {
      // 默认选择最近30分钟
      const endTime = dayjs()
      const startTime = endTime.subtract(30, 'minute')
      return [startTime, endTime]
    })

    // 使用外部传入的时间范围，如果没有则使用内部状态
    const dateRange =
      externalDateRange !== undefined ? externalDateRange : internalDateRange

    const [isFullscreen, setIsFullscreen] = useState<boolean>(false)
    const [lastValidTimeRangeText, setLastValidTimeRangeText] =
      useState<string>('在最近 30 分钟里')

    // 创建内部ref来引用ReactECharts实例
    const chartRef = useRef<any>(null)

    // 暴露getEchartsInstance和setGroup方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        getEchartsInstance: () => {
          return chartRef.current?.getEchartsInstance?.()
        },
        setGroup: (groupId: string) => {
          const instance = chartRef.current?.getEchartsInstance?.()
          if (instance) {
            instance.group = groupId
          }
        },
      }),
      [],
    )

    // 计算查询步长（根据时间范围自动调整）
    const calculateStep = useCallback((durationMs: number) => {
      const durationMinutes = durationMs / (1000 * 60)
      if (durationMinutes <= 30) return '30s' // 30分钟以内：30秒步长
      if (durationMinutes <= 180) return '1m0s' // 3小时以内：1分钟步长
      if (durationMinutes <= 360) return '2m0s' // 6小时以内：2分钟步长
      if (durationMinutes <= 1440) return '5m0s' // 24小时以内：5分钟步长
      if (durationMinutes <= 10080) return '15m0s' // 7天以内：15分钟步长
      if (durationMinutes <= 20160) return '1h0m0s' // 14天以内：1小时步长
      return '4h0m0s' // 超过14天：4小时步长
    }, [])

    // 使用 React Query 进行数据获取
    const {
      data: chartData,
      isLoading: loading,
      error,
    } = useQuery({
      queryKey: [
        'prometheusChart',
        typeof title === 'string' ? title : 'react-component-title',
        dateRange?.[0]?.toISOString(),
        dateRange?.[1]?.toISOString(),
        dateRange
          ? calculateStep(dateRange[1].valueOf() - dateRange[0].valueOf())
          : null,
        ...loadingDeps, // 将 loadingDeps 展开到 queryKey 中，当依赖项变化时触发重新请求
      ],
      queryFn: async () => {
        if (!fetchPrometheusData || !dateRange?.[0] || !dateRange?.[1]) {
          throw new Error('缺少请求参数')
        }

        const start = dateRange[0]
        const end = dateRange[1]

        // 转换为 ISO 8601 字符串格式
        const startStr = start.toISOString()
        const endStr = end.toISOString()

        // 计算查询步长
        const duration = end.valueOf() - start.valueOf()
        const step = calculateStep(duration)

        const data = await fetchPrometheusData(startStr, endStr, step)
        return data
      },
      enabled:
        enabled !== false &&
        !!(fetchPrometheusData && dateRange?.[0] && dateRange?.[1]), // 外部控制 + 必要参数检查
      staleTime: 30 * 1000, // 30秒内数据被认为是新鲜的
      gcTime: 5 * 60 * 1000, // 缓存5分钟
      refetchOnWindowFocus: false, // 窗口聚焦时不自动重新请求
    })
    // 检查当前时间范围是否匹配某个预设选项
    const getMatchingPreset = useCallback((range: [Dayjs, Dayjs] | null) => {
      if (!range) return null

      const [start, end] = range

      // 检查是否匹配预设选项（允许2分钟的误差，因为时间可能有微小差异）
      for (const preset of TIME_PRESETS) {
        const [presetStart, presetEnd] = preset.value()
        if (
          Math.abs(start.diff(presetStart, 'minute')) <= 2 &&
          Math.abs(end.diff(presetEnd, 'minute')) <= 2
        ) {
          return preset.label
        }
      }
      return null
    }, [])

    // 构建 ECharts 配置
    const chartOption = useMemo(() => {
      // 如果提供了 formatOption 函数，使用它来格式化数据
      if (formatOption && chartData) {
        return formatOption(chartData)
      }

      // 没有提供 formatOption 或没有数据时返回空对象
      return {}
    }, [formatOption, chartData])

    // 全屏切换函数
    const toggleFullscreen = useCallback(() => {
      const newFullscreenState = !isFullscreen
      setIsFullscreen(newFullscreenState)

      // 通知父组件全屏状态变化
      if (onFullscreenChange) {
        onFullscreenChange(newFullscreenState)
      }
    }, [isFullscreen, onFullscreenChange])

    // ESC 键监听，退出全屏
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape' && isFullscreen) {
          setIsFullscreen(false)

          // 通知父组件全屏状态变化
          if (onFullscreenChange) {
            onFullscreenChange(false)
          }
        }
      }

      if (isFullscreen) {
        document.addEventListener('keydown', handleKeyDown)
      }

      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }, [isFullscreen, onFullscreenChange])

    // 生成时间范围文本
    const timeRangeText = useMemo(() => {
      // 首先检查当前时间范围是否匹配预设选项
      if (dateRange?.[0] && dateRange?.[1]) {
        const matchingPreset = getMatchingPreset(dateRange)
        if (matchingPreset) {
          // 匹配到预设选项，使用预设描述
          const presetMap: Record<string, string> = {
            '最近 30 分钟': '在最近 30 分钟里',
            '最近 6 小时': '在最近 6 小时里',
            '最近 24 小时': '在最近 24 小时里',
            '最近 3 天': '在最近 3 天里',
            '最近 7 天': '在最近 7 天里',
            '最近 14 天': '在最近 14 天里',
          }
          return presetMap[matchingPreset] || '在选定时间范围内'
        }

        // 自定义范围情况
        const startTime = dateRange[0].format('YYYY-MM-DD HH:mm:ss')
        const endTime = dateRange[1].format('YYYY-MM-DD HH:mm:ss')
        return `在 ${startTime} 到 ${endTime} 之间`
      }

      return '在选定时间范围内'
    }, [dateRange, getMatchingPreset])

    // 计算图表高度（总高度减去其他元素占用的空间）
    const chartHeight = useMemo(() => {
      // 全屏模式下的高度计算
      if (isFullscreen) {
        // 全屏模式：使用视口高度减去头部和其他元素的高度
        const FULLSCREEN_RESERVED_HEIGHT = 200 // 头部 + Alert + 数据来源 + 间距
        return `calc(100vh - ${FULLSCREEN_RESERVED_HEIGHT}px)`
      }

      // 普通模式的高度计算
      const RESERVED_HEIGHT = 152 // 预留给其他元素的高度（80px头部 + 40px Alert + 16px数据来源 + 16px间距）

      if (typeof height === 'number') {
        return Math.max(200, height - RESERVED_HEIGHT) // 最小高度200px
      }

      if (typeof height === 'string') {
        // 对于字符串类型（如 calc 表达式），使用 CSS calc 计算
        return `calc(${height} - ${RESERVED_HEIGHT}px)`
      }

      // 默认情况
      return Math.max(200, 400 - RESERVED_HEIGHT)
    }, [height, isFullscreen])

    // 生成数据来源提示文本
    const dataSourceText = useMemo(() => {
      // 检查 chartData 是否有 times 数组且不为空
      if (!chartData) {
        return '数据统计截止至 暂无数据 | 数据来源 Prometheus'
      }

      try {
        // 尝试从 chartData 中获取 times 数组
        const dataWithTimes = chartData as any
        if (
          dataWithTimes.times &&
          Array.isArray(dataWithTimes.times) &&
          dataWithTimes.times.length > 0
        ) {
          const times = dataWithTimes.times as string[]
          const lastTimeString = times[times.length - 1]

          if (lastTimeString) {
            // 尝试解析时间字符串
            const parsedTime = dayjs(lastTimeString)
            if (parsedTime.isValid()) {
              const formattedTime = parsedTime.format('YYYY年MM月DD日 HH:mm:ss')
              return `数据统计截止至 ${formattedTime} | 数据来源 Prometheus`
            }
          }
        }
      } catch (error) {
        console.warn('解析数据时间戳失败:', error)
      }

      // 默认情况：使用当前时间
      const currentTime = dayjs().format('YYYY年MM月DD日 HH:mm:ss')
      return `数据统计截止至 ${currentTime} | 数据来源 Prometheus`
    }, [chartData])

    // 渲染提示区域内容
    const alertContent = useMemo(() => {
      if (!alertRender || !chartData) return null
      // 当 dateRange 为空或不完整时，使用 lastValidTimeRangeText
      const textToUse =
        dateRange?.[0] && dateRange?.[1]
          ? timeRangeText
          : lastValidTimeRangeText
      return alertRender(chartData, textToUse)
    }, [
      alertRender,
      chartData,
      timeRangeText,
      lastValidTimeRangeText,
      dateRange,
    ])

    return (
      <Card
        className={`w-full border-none p-4 text-[#333333] ${className} ${
          isFullscreen ? 'fixed inset-0 z-50 bg-white overflow-auto' : ''
        }`}
        style={{
          ...(isFullscreen ? { height: '100vh', width: '100vw' } : {}),
          transition: 'all 0.3s ease-in-out',
        }}
      >
        {/* 头部区域 */}
        <div className="flex justify-between items-center mb-4">
          {/* 左侧：标题和描述 */}
          <SectionTitle title={title} desc={desc} className="!mb-0" />

          {/* 右侧：时间范围选择器和全屏按钮 */}
          <div className="flex items-center gap-2">
            {showTimeSelector && (
              <DatePicker.RangePicker
                value={dateRange}
                onChange={(dates) => {
                  // 2.1 完整性检查
                  if (dates?.[0] && dates?.[1]) {
                    // 选择完整，进行处理
                    let startTime = dates[0]
                    let endTime = dates[1]

                    // 确保开始时间在结束时间之前
                    if (startTime.isAfter(endTime)) {
                      ;[startTime, endTime] = [endTime, startTime]
                    }

                    // 2.2 14天范围校正（精确到分钟）
                    const diffMinutes = Math.abs(
                      endTime.diff(startTime, 'minute'),
                    )
                    const maxMinutes = 14 * 24 * 60 // 14天 = 20160分钟
                    if (diffMinutes > maxMinutes) {
                      // 超过14天，执行自动校正
                      console.log(
                        `时间范围超过14天（${diffMinutes}分钟 > ${maxMinutes}分钟），自动校正为14天范围`,
                      )
                      // 保持开始时间不变，调整结束时间为开始时间后的第14天
                      endTime = startTime.add(14, 'day')
                    }

                    // 2.3 正常选择处理
                    const correctedRange: [Dayjs, Dayjs] = [startTime, endTime]

                    // 如果有外部回调，调用它；否则更新内部状态
                    if (onDateRangeChange) {
                      onDateRangeChange(correctedRange)
                    } else {
                      setInternalDateRange(correctedRange)
                    }

                    // 更新最后一次有效的时间范围文本
                    const matchingPreset = getMatchingPreset(correctedRange)
                    if (matchingPreset) {
                      const presetMap: Record<string, string> = {
                        '最近 30 分钟': '在最近 30 分钟里',
                        '最近 6 小时': '在最近 6 小时里',
                        '最近 24 小时': '在最近 24 小时里',
                        '最近 3 天': '在最近 3 天里',
                        '最近 7 天': '在最近 7 天里',
                        '最近 14 天': '在最近 14 天里',
                      }
                      setLastValidTimeRangeText(
                        presetMap[matchingPreset] || '在选定时间范围内',
                      )
                    } else {
                      const startTimeStr = startTime.format(
                        'YYYY-MM-DD HH:mm:ss',
                      )
                      const endTimeStr = endTime.format('YYYY-MM-DD HH:mm:ss')
                      setLastValidTimeRangeText(
                        `在 ${startTimeStr} 到 ${endTimeStr} 之间`,
                      )
                    }

                    // 数据请求将由 useQuery 自动处理，这里只需要更新 dateRange
                  } else {
                    // 选择不完整（只选了一个日期或点击了 clear）
                    // 不触发数据请求，不自动补充默认时间范围
                    // 保持时间选择器为空状态，等待用户完成完整选择
                    if (onDateRangeChange) {
                      onDateRangeChange(null)
                    } else {
                      setInternalDateRange(null)
                    }
                  }
                }}
                format="YYYY-MM-DD HH:mm:ss"
                disabledDate={(current) => {
                  // 只禁用未来的日期，今天是可以选择的
                  if (current && current > dayjs().endOf('day')) {
                    return true
                  }
                  return false
                }}
                presets={TIME_PRESETS.map((preset) => ({
                  label: preset.label,
                  value: preset.value() as [Dayjs, Dayjs],
                }))}
                placeholder={['开始时间', '结束时间']}
                allowClear={false}
                style={{
                  minWidth: 280,
                }}
                dropdownClassName="prometheus-chart-date-picker"
              />
            )}

            {/* 全屏切换按钮 */}
            <Button
              type="text"
              size="small"
              icon={isFullscreen ? <CompressOutlined /> : <ExpandOutlined />}
              onClick={toggleFullscreen}
              title={isFullscreen ? '退出全屏' : '全屏显示'}
            />
          </div>
        </div>
        {alertContent && (
          <div className={showTimeSelector ? 'mb-4' : 'mb-1'}>
            <Alert
              className="items-start [&_.anticon]:mt-1"
              message={alertContent}
              type="info"
              showIcon
              style={{ backgroundColor: '#F8F8F9', border: 'none' }}
            />
          </div>
        )}
        <div style={{ height: chartHeight }}>
          {error ? (
            <div className="flex items-center justify-center h-full text-red-500">
              查询失败: {error?.message || '数据查询失败'}
            </div>
          ) : (
            <ReactECharts
              ref={chartRef}
              option={chartOption}
              style={{ height: '100%', width: '100%' }}
              notMerge={true}
              lazyUpdate={true}
              showLoading={loading}
              loadingOption={{
                text: '数据加载中...',
                color: '#7367EF',
                textColor: '#333333',
                maskColor: 'rgba(255, 255, 255, 0.8)',
              }}
            />
          )}
        </div>

        {/* 数据来源提示文本 */}
        <div
          style={{
            color: '#999999',
            fontSize: '12px',
            lineHeight: '16px',
            textAlign: 'left',
            paddingLeft: '0px', // 从 Card 的 padding 起点开始显示
          }}
        >
          {dataSourceText}
        </div>
      </Card>
    )
  },
)

export default PrometheusChart as <T = any>(
  props: PrometheusChartProps<T> & { ref?: React.Ref<any> },
) => JSX.Element
