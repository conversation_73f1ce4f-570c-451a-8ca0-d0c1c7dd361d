import SectionTitle from '@/components/common/SectionTitle'
import { Card } from '@/components/ui/card'
import { Space, Typography } from 'antd'
import dayjs from 'dayjs'
import type React from 'react'
import PrometheusChart from './PrometheusChart'

// PrometheusChartDemo 专用的数据类型定义
interface DemoChartData {
  series: Array<{
    time: string
    timestamp: number
    gpuUtilization: number // GPU 利用率
    gpuRequestRate: number // GPU 申请率
    actualUsageRate: number // 资源实际使用率
  }>
  summary: {
    avgGpuUtilization: number
    avgGpuRequestRate: number
    avgActualUsageRate: number
    maxGpuUtilization: number
    maxGpuRequestRate: number
    maxActualUsageRate: number
  }
}

const { Title, Text } = Typography

// 生成模拟数据函数
const generateMockData = (
  start: string,
  end: string,
  step: string,
): DemoChartData => {
  const startTime = new Date(start).getTime()
  const endTime = new Date(end).getTime()
  const duration = endTime - startTime
  const pointCount = Math.min(
    100,
    Math.max(10, Math.floor(duration / (5 * 60 * 1000))),
  ) // 每5分钟一个点

  const data = []
  for (let i = 0; i < pointCount; i++) {
    const time = startTime + (i * duration) / (pointCount - 1)
    data.push({
      time: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
      timestamp: time,
      gpuUtilization: Number.parseFloat((Math.random() * 30 + 50).toFixed(4)), // GPU 利用率: 50-80%，4位小数
      gpuRequestRate: Number.parseFloat((Math.random() * 25 + 60).toFixed(4)), // GPU 申请率: 60-85%，4位小数
      actualUsageRate: Number.parseFloat((Math.random() * 40 + 40).toFixed(4)), // 资源实际使用率: 40-80%，4位小数
    })
  }

  return {
    series: data,
    summary: {
      avgGpuUtilization: Number.parseFloat(
        (
          data.reduce((sum, item) => sum + item.gpuUtilization, 0) / data.length
        ).toFixed(2),
      ),
      avgGpuRequestRate: Number.parseFloat(
        (
          data.reduce((sum, item) => sum + item.gpuRequestRate, 0) / data.length
        ).toFixed(2),
      ),
      avgActualUsageRate: Number.parseFloat(
        (
          data.reduce((sum, item) => sum + item.actualUsageRate, 0) /
          data.length
        ).toFixed(2),
      ),
      maxGpuUtilization: Number.parseFloat(
        Math.max(...data.map((item) => item.gpuUtilization)).toFixed(2),
      ),
      maxGpuRequestRate: Number.parseFloat(
        Math.max(...data.map((item) => item.gpuRequestRate)).toFixed(2),
      ),
      maxActualUsageRate: Number.parseFloat(
        Math.max(...data.map((item) => item.actualUsageRate)).toFixed(2),
      ),
    },
  }
}

const PrometheusChartDemo: React.FC = () => {
  return (
    <div className="p-6">
      <Title level={2}>PrometheusChart 组件演示</Title>
      <Text type="secondary" className="block mb-6">
        通用的 Prometheus 图表组件，支持时间范围选择、数据查询和自定义提示信息
      </Text>

      <Space direction="vertical" size="large" className="w-full">
        {/* 基础用法说明 */}
        <Card>
          <SectionTitle title="重要说明" />
          <Text type="secondary" className="block mb-4">
            PrometheusChart 组件现在需要提供 formatOption 函数来处理图表数据。
            如果不提供 formatOption，图表将显示为空。
          </Text>
          <div className="bg-yellow-50 p-4 rounded border border-yellow-200">
            <Text className="text-yellow-800">
              ⚠️ 注意：组件已移除默认图表配置，必须通过 formatOption
              函数自定义图表显示。
            </Text>
          </div>
        </Card>

        {/* 使用 formatOption 自定义图表 */}
        <Card>
          <SectionTitle title="使用 formatOption 自定义图表" />
          <Text type="secondary" className="block mb-4">
            使用 formatOption 函数自定义图表配置和数据处理
          </Text>
          <PrometheusChart<DemoChartData>
            title="GPU 使用率"
            desc="显示 GPU 的使用率趋势，包括计算和内存使用情况"
            fetchPrometheusData={async (
              start: string,
              end: string,
              step: string,
            ) => {
              // 模拟数据请求延迟
              await new Promise((resolve) => setTimeout(resolve, 800))
              return generateMockData(start, end, step)
            }}
            formatOption={(chartData) => {
              const times =
                chartData?.series?.map((item: any) => item.time) || []
              const gpuData =
                chartData?.series?.map((item: any) => item.gpuUsage) || []
              const cpuData =
                chartData?.series?.map((item: any) => item.cpuUsage) || []

              return {
                legend: {
                  top: 'top',
                  left: 'center',
                  data: ['GPU 使用率', 'CPU 使用率'],
                },
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '3%',
                  top: '15%',
                  containLabel: true,
                },
                xAxis: {
                  type: 'category',
                  data: times,
                  axisLabel: {
                    rotate: 45,
                  },
                },
                yAxis: {
                  type: 'value',
                  min: 0,
                  max: 100,
                  axisLabel: {
                    formatter: '{value}%',
                  },
                },
                series: [
                  {
                    name: 'GPU 使用率',
                    data: gpuData,
                    type: 'line',
                    color: '#52C41A',
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                      width: 3,
                    },
                  },
                  {
                    name: 'CPU 使用率',
                    data: cpuData,
                    type: 'line',
                    color: '#1890FF',
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                      width: 3,
                    },
                  },
                ],
              }
            }}
            alertRender={(data, timeRangeText) => {
              if (!data?.summary) return null
              const { avgGpuUtilization, maxGpuUtilization } = data.summary
              return (
                <div className="space-y-2">
                  <div className="text-sm text-gray-700">
                    {timeRangeText}的 GPU 使用情况统计：
                  </div>
                  <div className="flex gap-4 text-sm">
                    <span className="text-blue-600">
                      <strong>平均使用率:</strong> {avgGpuUtilization}%
                    </span>
                    <span className="text-red-600">
                      <strong>峰值使用率:</strong> {maxGpuUtilization}%
                    </span>
                    <span className="text-gray-600">
                      建议保持 GPU 使用率在 80% 以下以获得最佳性能
                    </span>
                  </div>
                </div>
              )
            }}
          />
        </Card>

        {/* 自定义高度 */}
        <Card>
          <SectionTitle title="自定义图表高度" />
          <Text type="secondary" className="block mb-4">
            通过 height 属性调整图表高度，支持数字或 CSS calc 表达式
          </Text>
          <PrometheusChart<DemoChartData>
            title="网络流量"
            desc="显示网络入站和出站流量的变化趋势"
            fetchPrometheusData={async (
              start: string,
              end: string,
              step: string,
            ) => {
              await new Promise((resolve) => setTimeout(resolve, 600))
              return generateMockData(start, end, step)
            }}
            height={300}
            formatOption={(chartData) => {
              const times =
                chartData?.series?.map((item: any) => item.time) || []
              const networkData =
                chartData?.series?.map((item: any) => item.cpuUsage) || []

              return {
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '3%',
                  top: '10%',
                  containLabel: true,
                },
                xAxis: {
                  type: 'category',
                  data: times,
                },
                yAxis: {
                  type: 'value',
                  axisLabel: {
                    formatter: '{value} MB/s',
                  },
                },
                series: [
                  {
                    name: '网络流量',
                    data: networkData,
                    type: 'line',
                    color: '#722ED1',
                    smooth: true,
                  },
                ],
              }
            }}
            alertRender={(data, timeRangeText) => {
              if (!data?.summary) return null
              return (
                <div className="text-sm text-gray-600">
                  {timeRangeText}的网络流量统计信息
                </div>
              )
            }}
          />
        </Card>

        {/* CSS calc 高度示例 */}
        <Card>
          <SectionTitle title="CSS calc 高度示例" />
          <Text type="secondary" className="block mb-4">
            使用 CSS calc 表达式设置动态高度
          </Text>
          <PrometheusChart<DemoChartData>
            title="存储使用率"
            desc="显示存储空间的使用情况和变化趋势"
            fetchPrometheusData={async (
              start: string,
              end: string,
              step: string,
            ) => {
              await new Promise((resolve) => setTimeout(resolve, 700))
              return generateMockData(start, end, step)
            }}
            height="calc(50vh - 100px)"
            formatOption={(chartData) => {
              const times =
                chartData?.series?.map((item: any) => item.time) || []
              const storageData =
                chartData?.series?.map((item: any) => item.memoryUsage) || []

              return {
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '3%',
                  top: '10%',
                  containLabel: true,
                },
                xAxis: {
                  type: 'category',
                  data: times,
                },
                yAxis: {
                  type: 'value',
                  min: 0,
                  max: 100,
                  axisLabel: {
                    formatter: '{value}%',
                  },
                },
                series: [
                  {
                    name: '存储使用率',
                    data: storageData,
                    type: 'line',
                    color: '#13C2C2',
                    areaStyle: {
                      opacity: 0.3,
                    },
                  },
                ],
              }
            }}
            alertRender={(data, timeRangeText) => {
              if (!data?.summary) return null
              return (
                <div className="text-sm text-blue-600">
                  💾 存储监控：{timeRangeText}，动态高度适应视窗大小
                </div>
              )
            }}
          />
        </Card>

        {/* 复杂统计信息 */}
        <Card>
          <SectionTitle title="复杂统计信息展示" />
          <Text type="secondary" className="block mb-4">
            展示更复杂的统计信息和多种数据指标
          </Text>
          <PrometheusChart<DemoChartData>
            title="系统综合监控"
            desc="综合显示系统的 CPU、GPU、内存使用情况及其统计信息"
            fetchPrometheusData={async (
              start: string,
              end: string,
              step: string,
            ) => {
              await new Promise((resolve) => setTimeout(resolve, 900))
              return generateMockData(start, end, step)
            }}
            height={450}
            alertRender={(data, timeRangeText) => {
              if (!data?.summary) return null
              const {
                avgGpuUtilization,
                avgGpuRequestRate,
                avgActualUsageRate,
                maxGpuUtilization,
                maxGpuRequestRate,
                maxActualUsageRate,
              } = data.summary

              return (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-700">
                      资源使用统计
                    </span>
                    <span className="text-xs text-gray-500">
                      {timeRangeText}
                    </span>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="bg-blue-50 p-3 rounded">
                      <div className="text-blue-700 font-medium">
                        GPU 利用率
                      </div>
                      <div className="text-blue-600">
                        平均: {avgGpuUtilization}%
                      </div>
                      <div className="text-blue-800">
                        峰值: {maxGpuUtilization}%
                      </div>
                    </div>

                    <div className="bg-green-50 p-3 rounded">
                      <div className="text-green-700 font-medium">
                        GPU 申请率
                      </div>
                      <div className="text-green-600">
                        平均: {avgGpuRequestRate}%
                      </div>
                      <div className="text-green-800">
                        峰值: {maxGpuRequestRate}%
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-3 rounded">
                      <div className="text-yellow-700 font-medium">
                        实际使用率
                      </div>
                      <div className="text-yellow-600">
                        平均: {avgActualUsageRate}%
                      </div>
                      <div className="text-yellow-800">
                        峰值: {maxActualUsageRate}%
                      </div>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500 mt-2">
                    💡 提示: 当资源使用率持续超过 85% 时，建议考虑扩容或优化
                  </div>
                </div>
              )
            }}
          />
        </Card>

        {/* 时间范围文本功能演示 */}
        <Card>
          <SectionTitle title="时间范围文本功能演示" />
          <Text type="secondary" className="block mb-4">
            展示 alertRender 函数如何接收和使用时间范围文本参数
          </Text>
          <PrometheusChart<DemoChartData>
            title="时间范围文本示例"
            desc="演示不同时间选择方式下的文本显示效果"
            fetchPrometheusData={async (
              start: string,
              end: string,
              step: string,
            ) => {
              await new Promise((resolve) => setTimeout(resolve, 500))
              return generateMockData(start, end, step)
            }}
            height={350}
            alertRender={(data, timeRangeText) => {
              if (!data?.summary) return null
              const { avgActualUsageRate } = data.summary

              return (
                <div className="space-y-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                  <div className="text-lg font-semibold text-blue-800">
                    📊 智能时间范围识别
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-700">
                        检测到的时间范围：
                      </span>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">
                        {timeRangeText}
                      </span>
                    </div>

                    <div className="text-sm text-gray-600">
                      <strong>使用说明：</strong>
                      <ul className="mt-1 ml-4 space-y-1 list-disc">
                        <li>
                          选择快捷选项时显示：
                          <code className="bg-gray-100 px-1 rounded">
                            "在最近30分钟里"
                          </code>
                        </li>
                        <li>
                          手动选择时间时显示：
                          <code className="bg-gray-100 px-1 rounded">
                            "在2025-01-01 10:00到2025-01-01 11:00之间"
                          </code>
                        </li>
                      </ul>
                    </div>

                    <div className="text-sm text-indigo-700 bg-indigo-50 p-2 rounded">
                      💡 当前数据：{timeRangeText}，平均实际使用率为{' '}
                      {avgActualUsageRate}%
                    </div>
                  </div>
                </div>
              )
            }}
          />
        </Card>

        {/* 使用说明 */}
        <Card>
          <SectionTitle title="组件属性说明" />
          <div className="space-y-4 text-sm">
            <div>
              <strong className="text-gray-700">title</strong>
              <span className="text-gray-500 ml-2">图表标题（必填）</span>
            </div>
            <div>
              <strong className="text-gray-700">desc</strong>
              <span className="text-gray-500 ml-2">
                图表描述，鼠标悬停显示（可选）
              </span>
            </div>
            <div>
              <strong className="text-gray-700">query</strong>
              <span className="text-gray-500 ml-2">
                Prometheus PromQL 查询语句（可选）
              </span>
            </div>
            <div>
              <strong className="text-gray-700">formatOption</strong>
              <span className="text-gray-500 ml-2">
                图表数据格式化函数，接收原始数据返回 ECharts 配置（可选）
              </span>
            </div>
            <div>
              <strong className="text-gray-700">alertRender</strong>
              <span className="text-gray-500 ml-2">
                统计信息渲染函数，接收数据和时间范围文本两个参数（可选）
              </span>
            </div>
            <div>
              <strong className="text-gray-700">height</strong>
              <span className="text-gray-500 ml-2">
                图表高度，支持数字或 CSS calc 表达式，默认 400px（可选）
              </span>
            </div>
            <div>
              <strong className="text-gray-700">className</strong>
              <span className="text-gray-500 ml-2">自定义样式类名（可选）</span>
            </div>
          </div>
        </Card>
      </Space>
    </div>
  )
}

export default PrometheusChartDemo
