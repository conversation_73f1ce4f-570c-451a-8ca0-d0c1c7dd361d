import {
  type ResourceOverviewDetailResponse,
  type ResourceUsageDetailResponse,
  getResourceOverviewDetail,
  getResourceUsageDetail,
} from '@/api/resource'
import SectionTitle from '@/components/common/SectionTitle'
import { processLinkTo } from '@/utils/navigation'
import { useQuery } from '@tanstack/react-query'
import { Empty, Skeleton, Typography } from 'antd'
import type React from 'react'
import { useCallback, useEffect, useState } from 'react'
import { NavLink } from 'react-router-dom'

const { Text } = Typography

export interface ResourceUsageItem {
  label: string
  value: number
  color: string
  type: 'used' | 'queue' | 'available'
}

export interface ResourceUsageData {
  quota: number // 配额总数（卡）
  modelTraining: number // 模型训练运行中卡数
  modelExperiment: number // 模型实验运行中卡数
  queued: number // 排队任务卡数（统一处理）
  idle?: number // 空闲卡数（管理员模式专用）
  estimatedWaitHours?: number // 预计等待时间小时数（管理员模式专用）
}

export interface ResourceUsageBarProps {
  mode: 'team' | 'admin' // 模式配置
  teamId?: number // 可选参数，如果不传则获取集团数据
  className?: string
  actionButton?: React.ReactNode
  loadingDeps?: any[] // 依赖项数组，当数组中的任何数据发生变化时，自动触发重新调用 resourceData 的请求函数
}

const ResourceUsageBar: React.FC<ResourceUsageBarProps> = ({
  mode,
  teamId,
  className = '',
  actionButton,
  loadingDeps = [], // 默认为空数组
}) => {
  // 调试开关：只有当 localStorage 中的值为 'true' 时才输出调试日志
  const isDebugMode =
    localStorage.getItem('debuggerResourceUsageBar') === 'true'

  // 使用 React Query 进行数据获取
  const {
    data: resourceData,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ['resourceUsage', teamId, mode, ...loadingDeps],
    queryFn: async () => {
      try {
        // 参数验证
        if (mode === 'team' && (!teamId || teamId <= 0)) {
          throw new Error('团队模式下必须提供有效的 teamId 参数')
        }

        // 并行请求两个接口
        const [quotaResponse, usageResponse] = await Promise.all([
          getResourceOverviewDetail(teamId),
          getResourceUsageDetail(teamId),
        ])

        // 数据验证
        if (!quotaResponse || !usageResponse) {
          throw new Error('API 响应数据为空')
        }

        // 合并数据
        const mergedData = mergeResourceData(quotaResponse, usageResponse)

        // 调试日志
        if (isDebugMode) {
          console.log('[ResourceUsageBar] API 响应数据:', {
            quotaResponse,
            usageResponse,
            mergedData,
          })
        }

        return mergedData
      } catch (error) {
        console.error('[ResourceUsageBar] 数据获取失败:', error)
        throw error
      }
    },
    staleTime: 30 * 1000, // 30秒内数据被认为是新鲜的
    gcTime: 5 * 60 * 1000, // 缓存5分钟 (React Query v5 使用 gcTime 替代 cacheTime)
    refetchOnWindowFocus: false, // 窗口聚焦时不自动重新请求
  })

  // 数据有效性判断
  const isDataValid =
    resourceData &&
    typeof resourceData.quota === 'number' &&
    typeof resourceData.modelTraining === 'number' &&
    typeof resourceData.modelExperiment === 'number' &&
    typeof resourceData.queued === 'number'
  // 自动计算容器宽度
  const [containerWidth, setContainerWidth] = useState(
    Math.round(window.innerWidth - 264 - 148),
  )

  // 监听窗口大小变化，更新容器宽度
  useEffect(() => {
    const handleResize = () => {
      setContainerWidth(Math.round(window.innerWidth - 264 - 148))
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // 数据合并逻辑（修复死循环 bug 并适配新数据结构）
  const mergeResourceData = useCallback(
    (
      quotaResponse: ResourceOverviewDetailResponse,
      usageResponse: ResourceUsageDetailResponse,
    ): ResourceUsageData => {
      try {
        // 数据验证
        if (!quotaResponse || !usageResponse) {
          throw new Error('响应数据为空')
        }

        // 直接使用 API 返回的总配额值（配额计算逻辑已移至 API 层）
        const quota = quotaResponse.totalQuota || 0

        // 安全地计算各种状态的卡数（防止死循环）
        const safeReduce = (
          arr: any[],
          defaultValue = 0,
          fieldName = 'unknown',
        ) => {
          if (!Array.isArray(arr)) {
            if (isDebugMode) {
              console.warn(`[ResourceUsageBar] ${fieldName} 不是数组:`, arr)
            }
            return defaultValue
          }
          const res = arr.reduce((total, item) => {
            const nums = item?.nums || 0
            if (typeof nums !== 'number' || nums < 0) {
              if (isDebugMode) {
                console.warn(
                  `[ResourceUsageBar] ${fieldName} 中发现无效数值:`,
                  item,
                )
              }
              return total
            }
            return total + nums
          }, 0)

          return Number(res.toFixed(2))
        }

        // 计算各种状态的卡数
        const pending = safeReduce(usageResponse.pending, 0, 'pending')
        const idle = safeReduce(usageResponse.idle, 0, 'idle')
        const runningTrainTask = safeReduce(
          usageResponse.runningTrainTask,
          0,
          'runningTrainTask',
        )
        const runningOnlineDevelopment = safeReduce(
          usageResponse.runningOnlineDevelopment,
          0,
          'runningOnlineDevelopment',
        )

        // 数据合理性检查
        const totalUsed = runningTrainTask + runningOnlineDevelopment
        if (quota > 0 && totalUsed > quota) {
          console.warn('[ResourceUsageBar] 使用量超过配额:', {
            quota,
            totalUsed,
            runningTrainTask,
            runningOnlineDevelopment,
          })
        }

        // 转换为 ResourceUsageData 格式
        const result: ResourceUsageData = {
          quota: Math.max(0, quota), // 确保配额不为负数
          modelTraining: Math.max(0, runningTrainTask),
          modelExperiment: Math.max(0, runningOnlineDevelopment),
          queued: Math.max(0, pending), // 统一处理所有排队任务
        }

        // 管理员模式添加额外字段
        if (mode === 'admin') {
          result.idle = Math.max(0, idle)
          const estimatedSeconds =
            usageResponse.estimatedPendingTimeSeconds || 0
          result.estimatedWaitHours = Math.max(
            0,
            Math.round((estimatedSeconds / 3600) * 100) / 100,
          )
        }

        // 调试日志
        if (isDebugMode) {
          console.log('[ResourceUsageBar] 数据合并结果:', result)
        }

        return result
      } catch (error) {
        console.error('[ResourceUsageBar] 数据合并失败:', error)
        // 返回默认数据，避免组件崩溃
        const fallbackResult: ResourceUsageData = {
          quota: 0,
          modelTraining: 0,
          modelExperiment: 0,
          queued: 0,
        }

        if (mode === 'admin') {
          fallbackResult.idle = 0
          fallbackResult.estimatedWaitHours = 0
        }

        return fallbackResult
      }
    },
    [mode, isDebugMode],
  )

  // 只在数据有效时才解构，否则使用默认值
  const {
    quota = 0,
    modelTraining = 0,
    modelExperiment = 0,
    queued = 0,
    idle = 0,
    estimatedWaitHours = 0,
  } = resourceData || {}

  // 计算各种数值
  const actualUsage = modelTraining + modelExperiment // 运行中卡数
  const totalQueued = queued // 排队卡数（统一处理）
  const actualEquipped = actualUsage + idle // 管理员模式：实际配备（运行中+空闲中）
  const totalUsed = actualUsage + totalQueued // 用于最大值计算

  // 确定最大值（配额或总使用量中的较大者）
  const maxValue = Math.max(
    quota,
    mode === 'admin' ? actualEquipped + totalQueued : totalUsed,
  )

  // 调试日志：基础数据
  if (isDebugMode) {
    console.log('🔍 [ResourceUsageBar] 基础数据:', {
      quota,
      modelTraining,
      modelExperiment,
      queued,
      actualUsage,
      totalQueued,
      actualEquipped,
      totalUsed,
      maxValue,
      mode,
    })
  }

  // 计算可用空间（85%）
  const availableWidth = containerWidth * 0.85

  // 方块规格
  const blockWidth = 4
  const blockGap = 4
  const blockTotalWidth = blockWidth + blockGap

  // 计算可以放置的方块数量
  const maxBlocks = Math.floor(availableWidth / blockTotalWidth)

  // 计算每个方块代表的卡数（支持更高精度，避免精度丢失）
  // 修复：当 maxValue 为 0 时，设置最小值为 1，避免除零错误
  const safeMaxValue = Math.max(maxValue, 1)

  // 修复精度问题：使用更高精度计算，并设置最小值保护
  let cardsPerBlock = safeMaxValue / maxBlocks

  // 使用更高精度进行四舍五入（保留3位小数）
  cardsPerBlock = Math.round(cardsPerBlock * 1000) / 1000

  // 设置最小值保护，确保不会为0（最小精度0.001）
  cardsPerBlock = Math.max(cardsPerBlock, 0.001)

  // 调试日志：布局计算
  if (isDebugMode) {
    console.log('📐 [ResourceUsageBar] 布局计算:', {
      containerWidth,
      availableWidth,
      blockWidth,
      blockGap,
      blockTotalWidth,
      maxBlocks,
      safeMaxValue,
      cardsPerBlock,
    })
  }

  // 调试日志：精度计算详情
  if (isDebugMode) {
    console.log('🔢 [ResourceUsageBar] 精度计算详情:', {
      原始比值: safeMaxValue / maxBlocks,
      四舍五入前: (safeMaxValue / maxBlocks) * 1000,
      四舍五入后: Math.round((safeMaxValue / maxBlocks) * 1000),
      最终结果: cardsPerBlock,
      是否触发最小值保护: cardsPerBlock === 0.001,
    })
  }

  // 计算每种状态需要的方块数量
  // 修复：添加安全检查，避免除零错误和 NaN 结果，并处理小数值卡数的显示问题
  const getBlockCount = (value: number) => {
    if (cardsPerBlock <= 0 || !Number.isFinite(cardsPerBlock)) {
      if (isDebugMode) {
        console.warn('⚠️ [ResourceUsageBar] cardsPerBlock 无效:', cardsPerBlock)
      }
      return 0
    }

    const division = value / cardsPerBlock
    let result = Math.round(division)

    // 处理小数值卡数：当卡数大于0但计算结果为0时，强制返回1个方块
    if (value > 0 && result === 0) {
      result = 1
      if (isDebugMode) {
        console.log(`🔧 [ResourceUsageBar] 小数值修正: ${value}卡 → 1个方块`)
      }
    }

    // 调试日志：方块数量计算详情
    if (isDebugMode) {
      console.log(`🧮 [ResourceUsageBar] getBlockCount(${value}):`, {
        value,
        cardsPerBlock,
        division,
        原始结果: Math.round(division),
        最终结果: result,
        是否小数值修正: value > 0 && Math.round(division) === 0,
        isFinite: Number.isFinite(result),
      })
    }

    return Number.isFinite(result) ? result : 0
  }

  // 计算各队列的方块数量
  // 修复：优先保证子分类数据的完整显示，用子分类总和约束主队列显示
  const trainingBlocks = getBlockCount(modelTraining)
  const experimentBlocks = getBlockCount(modelExperiment)
  const mainQueueBlocks = trainingBlocks + experimentBlocks // 用子分类总和约束主队列显示
  const queuedBlocks = getBlockCount(totalQueued)
  const quotaBlocks = getBlockCount(quota)
  const idleBlocks = getBlockCount(idle)

  // 调试日志：方块数量计算
  if (isDebugMode) {
    console.log('🧮 [ResourceUsageBar] 方块数量计算:', {
      原始数据: {
        actualUsage,
        modelTraining,
        modelExperiment,
        totalQueued,
        quota,
        idle,
      },
      方块数量: {
        mainQueueBlocks,
        trainingBlocks,
        experimentBlocks,
        queuedBlocks,
        quotaBlocks,
        idleBlocks,
      },
      计算说明: '主队列方块数 = 训练方块数 + 实验方块数（保证子分类完整显示）',
    })
  }

  // 渲染方块的辅助函数
  const renderBlocks = (
    count: number,
    color: string,
    height: number,
    startX: number,
    startY: number,
  ) => {
    // 调试日志：renderBlocks 调用参数
    if (isDebugMode) {
      console.log('🎨 [ResourceUsageBar] renderBlocks 调用:', {
        count,
        color,
        height,
        startX,
        startY,
        blockWidth,
        blockTotalWidth,
      })
    }

    if (count <= 0) {
      if (isDebugMode) {
        console.log('⚠️ [ResourceUsageBar] 方块数量为0，跳过渲染')
      }
      return []
    }

    const blocks = []
    for (let i = 0; i < count; i++) {
      const blockStyle = {
        left: startX + i * blockTotalWidth,
        top: startY,
        width: blockWidth,
        height: height,
        backgroundColor: color,
      }

      // 调试日志：前3个方块的样式详情
      if (isDebugMode && i < 3) {
        console.log(`🔲 [ResourceUsageBar] 方块${i}样式:`, blockStyle)
      }

      blocks.push(
        <div
          key={`block-${startX}-${startY}-${i}`}
          className="absolute rounded-sm"
          style={blockStyle}
        />,
      )
    }

    if (isDebugMode) {
      console.log(
        `✅ [ResourceUsageBar] renderBlocks 返回${blocks.length}个方块`,
      )
    }
    return blocks
  }

  // 文本位置管理：按垂直位置分组进行碰撞检测
  interface TextPosition {
    startX: number
    endX: number
    text: string
    type: string
  }

  // 分组管理不同垂直层级的文本位置
  const upperTextPositions: TextPosition[] = [] // 上层文本组（top: -26px，主队列上方）
  const lowerTextPositions: TextPosition[] = [] // 下层文本组（top: 36px，子队列下方）

  // 文本宽度估算函数（基于字符数量的精确估算，支持小数显示和空格处理）
  const estimateTextWidth = (text: string): number => {
    // 中文字符约12px，英文字符约7px，数字约16px，小数点约4px，百分号约8px，空格约5px
    let width = 0
    for (const char of text) {
      if (/[\u4e00-\u9fa5]/.test(char)) {
        width += 12 // 中文字符
      } else if (/[0-9]/.test(char)) {
        width += 16 // 数字字符约16px宽度（字体较大）
      } else if (char === '.') {
        width += 4 // 小数点
      } else if (char === '%') {
        width += 8 // 百分号
      } else if (char === ' ') {
        width += 5 // 空格字符约5px宽度
      } else if (/\s/.test(char)) {
        width += 4 // 其他空白字符（制表符、换行符等）约4px宽度
      } else if (/[A-Za-z]/.test(char)) {
        width += 7 // 英文字母
      } else {
        width += 6 // 其他符号
      }
    }
    // 为小数数值添加额外的安全边距
    if (text.includes('.') && text.includes('%')) {
      width += 4 // 小数百分比额外边距
    }
    // 为包含多个空格的长文本添加额外安全边距
    const spaceCount = (text.match(/ /g) || []).length
    if (spaceCount >= 3) {
      width += 2 // 多空格文本额外边距
    }
    return width
  }

  // 碰撞检测和位置调整函数（支持分组管理）
  const calculateTextPosition = (
    defaultX: number,
    text: string,
    type: string,
    textGroup: TextPosition[], // 指定文本组进行碰撞检测
  ): number => {
    const textWidth = estimateTextWidth(text)
    let finalX = defaultX

    // 在指定的文本组内检测碰撞
    for (const existingText of textGroup) {
      // 检查是否重叠：当前文本的起始位置在已有文本的范围内
      if (
        finalX < existingText.endX &&
        finalX + textWidth > existingText.startX
      ) {
        // 发生碰撞，调整位置到已有文本的结束位置 + 8px间距
        finalX = existingText.endX + 8

        if (isDebugMode) {
          console.log('🔄 [ResourceUsageBar] 文本碰撞调整:', {
            type,
            text,
            原始位置: defaultX,
            碰撞文本: existingText.text,
            调整后位置: finalX,
            间距: '8px',
            文本组: textGroup === upperTextPositions ? '上层' : '下层',
          })
        }
      }
    }

    // 确保不超出容器边界（预留一些边距）
    const maxX = containerWidth - textWidth - 20
    if (finalX > maxX) {
      finalX = maxX
      if (isDebugMode) {
        console.log('⚠️ [ResourceUsageBar] 文本位置超出边界，调整到:', finalX)
      }
    }

    // 记录当前文本的位置信息到指定的文本组
    textGroup.push({
      startX: finalX,
      endX: finalX + textWidth,
      text,
      type,
    })

    if (isDebugMode) {
      console.log('📍 [ResourceUsageBar] 文本位置确定:', {
        type,
        text,
        startX: finalX,
        endX: finalX + textWidth,
        width: textWidth,
        文本组: textGroup === upperTextPositions ? '上层' : '下层',
      })
    }

    return finalX
  }

  // 获取图例配置
  const getLegendItems = () => {
    const baseItems = [
      {
        color: '#FFA043',
        label: mode === 'admin' ? '集团预算' : '团队配额',
      },
      {
        color: '#52C41A',
        label: '运行中',
      },
    ]

    if (mode === 'admin') {
      baseItems.push({
        color: '#999999',
        label: '空闲中',
      })
    }

    baseItems.push(
      {
        color: '#F1786D',
        label: '排队中',
      },
      {
        color: '#7367EF',
        label: '运行中的训练任务',
      },
      {
        color: '#1FD0D5',
        label: '运行中的开发机',
      },
    )

    return baseItems
  }

  // 渲染内容区域
  const renderContent = () => {
    // 加载中状态
    if (loading && !isDataValid) {
      return (
        <div style={{ height: 96 }}>
          <Skeleton active paragraph={{ rows: 2 }} />
        </div>
      )
    }

    // 错误状态
    if (error) {
      return (
        <div
          style={{ height: 96 }}
          className="flex items-center justify-center"
        >
          <Empty
            description={error?.message || '获取资源使用数据失败'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      )
    }

    // 空数据状态
    if (!loading && !isDataValid) {
      return (
        <div
          style={{ height: 96 }}
          className="flex items-center justify-center"
        >
          <Empty
            description="团队尚未产生资源监控数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      )
    }

    // 特殊处理：当配额为 0 时显示提示信息
    if (quota === 0) {
      return (
        <div
          style={{ height: 96 }}
          className="flex items-center justify-center"
        >
          {mode === 'admin' ? (
            <div className="h-8 w-full text-center text-[#999999] mt-4">
              集团暂未分配 GPU 配额
              <NavLink
                className="text-[#7569f0]"
                to={processLinkTo('/admin/resource-management') || ''}
              >
                &nbsp;管理后台-资源管理
              </NavLink>
            </div>
          ) : (
            <div className="h-8 w-full text-center text-[#999999] mt-4">
              团队暂未分配 GPU 配额
              <NavLink
                className="text-[#7569f0]"
                to={processLinkTo('/team/resource-management') || ''}
              >
                &nbsp;团队设置-资源管理
              </NavLink>
            </div>
          )}
        </div>
      )
    }

    // 重置文本位置数组，准备新的渲染周期
    upperTextPositions.length = 0 // 重置上层文本组
    lowerTextPositions.length = 0 // 重置下层文本组

    // 调试日志：渲染前的最终检查
    if (isDebugMode) {
      console.log('🎯 [ResourceUsageBar] 开始渲染，容器信息:', {
        containerWidth,
        height: 96,
        className: 'relative',
      })
    }

    // 正常状态：显示资源使用条形图
    return (
      <div
        className="relative"
        style={{
          width: containerWidth,
          height: 96,
        }}
      >
        {/* 第一行 - 实际占用/配备区域 */}

        {/* 主队列标题 */}
        <div
          className="absolute flex items-center justify-end"
          style={{
            left: 0,
            top: mode === 'admin' ? 8 : 2, // 管理员模式下调整垂直居中
            width: 160, // 增加宽度以容纳小数显示
            height: mode === 'admin' ? 24 : 24, // 保持一致的高度
            whiteSpace: 'nowrap', // 禁用文本换行
          }}
        >
          <Text
            className="text-sm text-gray-700"
            style={{ whiteSpace: 'nowrap' }}
          >
            {mode === 'admin' ? '实际配备' : '实际占用'}
          </Text>
          <Text
            className="text-2xl font-bold mx-1"
            style={{ whiteSpace: 'nowrap' }}
          >
            {mode === 'admin' ? actualEquipped : actualUsage}
          </Text>
          <Text
            className="text-sm text-gray-700"
            style={{ whiteSpace: 'nowrap' }}
          >
            卡
          </Text>
        </div>

        {/* 主队列（运行中） */}
        {(() => {
          if (isDebugMode) {
            console.log('🟢 [ResourceUsageBar] 渲染主队列方块')
          }
          return renderBlocks(mainQueueBlocks, '#52C41A', 24, 176, 0)
        })()}

        {/* 管理员模式：运行中标签（在方块上方） */}
        {mode === 'admin' &&
          actualUsage > 0 &&
          (() => {
            const text = `运行中 ${actualUsage} 卡`
            const adjustedX = calculateTextPosition(
              176,
              text,
              '运行中',
              upperTextPositions,
            )

            return (
              <div
                className="absolute flex items-baseline"
                style={{
                  left: adjustedX,
                  top: -26,
                  whiteSpace: 'nowrap',
                }}
              >
                <Text className="text-sm text-gray-700">运行中</Text>
                <Text className="text-xl font-bold mx-1">{actualUsage}</Text>
                <Text className="text-sm text-gray-700">卡</Text>
              </div>
            )
          })()}

        {/* 管理员模式：空闲队列 */}
        {mode === 'admin' && idle > 0 && (
          <>
            {(() => {
              if (isDebugMode) {
                console.log('⚪ [ResourceUsageBar] 渲染空闲方块')
              }
              return renderBlocks(
                idleBlocks,
                '#999999',
                40,
                176 + mainQueueBlocks * blockTotalWidth,
                0,
              )
            })()}
            {/* 空闲标签（在方块上方） */}
            {(() => {
              const text = `空闲中 ${idle} 卡`
              const defaultX = 176 + mainQueueBlocks * blockTotalWidth
              const adjustedX = calculateTextPosition(
                defaultX,
                text,
                '空闲中',
                upperTextPositions,
              )

              return (
                <div
                  className="absolute flex items-baseline"
                  style={{
                    left: adjustedX,
                    top: -26,
                    whiteSpace: 'nowrap',
                  }}
                >
                  <Text className="text-sm text-gray-700">空闲中</Text>
                  <Text className="text-xl font-bold mx-1">{idle}</Text>
                  <Text className="text-sm text-gray-700">卡</Text>
                </div>
              )
            })()}
          </>
        )}

        {/* 管理员模式：排队信息（文字形式） */}
        {mode === 'admin' && totalQueued > 0 && (
          <>
            {(() => {
              if (isDebugMode) {
                console.log('🔴 [ResourceUsageBar] 渲染排队方块')
              }
              return renderBlocks(
                queuedBlocks,
                '#F1786D',
                40,
                176 +
                  mainQueueBlocks * blockTotalWidth +
                  (idle > 0 ? idleBlocks * blockTotalWidth : 0),
                0,
              )
            })()}
            {/* 排队标签（在方块上方） */}
            {(() => {
              const text = `排队中 ${totalQueued} 卡，预计等待 ${estimatedWaitHours} 小时`
              const defaultX =
                176 +
                mainQueueBlocks * blockTotalWidth +
                (idle > 0 ? idleBlocks * blockTotalWidth : 0)
              const adjustedX = calculateTextPosition(
                defaultX,
                text,
                '排队中',
                upperTextPositions,
              )

              return (
                <div
                  className="absolute flex items-baseline"
                  style={{
                    left: adjustedX,
                    top: -26,
                    whiteSpace: 'nowrap',
                  }}
                >
                  <Text className="text-sm text-gray-700">排队中</Text>
                  <Text className="text-xl font-bold mx-1">{totalQueued}</Text>
                  <Text className="text-sm text-gray-700">卡，预计等待</Text>
                  <Text className="text-sm font-bold mx-1">
                    {estimatedWaitHours}
                  </Text>
                  <Text className="text-sm text-gray-700">小时</Text>
                </div>
              )
            })()}
          </>
        )}

        {/* 子队列1（模型训练） */}
        {(() => {
          if (isDebugMode) {
            console.log('🔵 [ResourceUsageBar] 渲染模型训练方块')
          }
          return renderBlocks(trainingBlocks, '#7367EF', 14, 176, 26)
        })()}
        {modelTraining > 0 &&
          (() => {
            const text = `模型训练 ${modelTraining} 卡`
            const adjustedX = calculateTextPosition(
              176,
              text,
              '模型训练',
              lowerTextPositions,
            )

            return (
              <div
                className="absolute flex items-center"
                style={{
                  left: adjustedX,
                  top: 36,
                  whiteSpace: 'nowrap',
                }}
              >
                <Text className="text-sm text-gray-700">模型训练</Text>
                <Text className="text-xl font-bold mx-1">{modelTraining}</Text>
                <Text className="text-sm text-gray-700">卡</Text>
              </div>
            )
          })()}

        {/* 子队列2（模型实验） */}
        {(() => {
          if (isDebugMode) {
            console.log('🔷 [ResourceUsageBar] 渲染模型实验方块')
          }
          return renderBlocks(
            experimentBlocks,
            '#1FD0D5',
            14,
            176 + trainingBlocks * blockTotalWidth,
            26,
          )
        })()}
        {modelExperiment > 0 &&
          (() => {
            const text = `模型实验 ${modelExperiment} 卡`
            const defaultX = 176 + trainingBlocks * blockTotalWidth
            const adjustedX = calculateTextPosition(
              defaultX,
              text,
              '模型实验',
              lowerTextPositions,
            )

            return (
              <div
                className="absolute flex items-center"
                style={{
                  left: adjustedX,
                  top: 36,
                  whiteSpace: 'nowrap',
                }}
              >
                <Text className="text-sm text-gray-700">模型实验</Text>
                <Text className="text-xl font-bold mx-1">
                  {modelExperiment}
                </Text>
                <Text className="text-sm text-gray-700">卡</Text>
              </div>
            )
          })()}

        {/* 团队模式：排队队列（按正确顺序渲染在模型训练和模型实验之后） */}
        {mode === 'team' && totalQueued > 0 && (
          <>
            {(() => {
              if (isDebugMode) {
                console.log('🔴 [ResourceUsageBar] 团队模式渲染排队方块')
              }
              return renderBlocks(
                queuedBlocks,
                '#F1786D',
                41,
                176 + mainQueueBlocks * blockTotalWidth,
                0,
              )
            })()}
            {(() => {
              const text = `排队中 ${totalQueued} 卡`
              const defaultX = 176 + mainQueueBlocks * blockTotalWidth
              const adjustedX = calculateTextPosition(
                defaultX,
                text,
                '团队排队中',
                lowerTextPositions,
              )

              return (
                <div
                  className="absolute flex items-center"
                  style={{
                    left: adjustedX,
                    top: 36,
                    whiteSpace: 'nowrap',
                  }}
                >
                  <Text className="text-sm text-gray-700">排队中</Text>
                  <Text className="text-xl font-bold mx-1">{totalQueued}</Text>
                  <Text className="text-sm text-gray-700">卡</Text>
                </div>
              )
            })()}
          </>
        )}

        {/* 第二行 - 配额队列 */}
        <div
          className="absolute flex items-center justify-end"
          style={{
            left: 0,
            top: 68,
            width: 160, // 增加宽度以容纳小数显示
            height: 24,
            whiteSpace: 'nowrap', // 禁用文本换行
          }}
        >
          <Text
            className="text-sm text-gray-700"
            style={{ whiteSpace: 'nowrap' }}
          >
            {mode === 'admin' ? '集团预算' : '团队配额'}
          </Text>
          <Text
            className="text-2xl font-bold mx-1"
            style={{ whiteSpace: 'nowrap' }}
          >
            {quota}
          </Text>
          <Text
            className="text-sm text-gray-700"
            style={{ whiteSpace: 'nowrap' }}
          >
            卡
          </Text>
        </div>

        {/* 配额方块 */}
        {(() => {
          if (isDebugMode) {
            console.log('🟡 [ResourceUsageBar] 渲染配额方块')
          }
          const quotaBlocksResult = renderBlocks(
            quotaBlocks,
            '#FFA043',
            24,
            176,
            68,
          )
          if (isDebugMode) {
            console.log('🎉 [ResourceUsageBar] 所有方块渲染完成')
          }
          return quotaBlocksResult
        })()}
      </div>
    )
  }

  return (
    <div className={`resource-usage-bar ${className}`}>
      {/* 标题、图例和按钮 */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-start gap-6">
          <SectionTitle title="资源监控" />
          {/* 图例 - 只在有数据时显示 */}
          {isDataValid && (
            <div className="flex items-center gap-4 text-sm">
              {getLegendItems().map((item, index) => (
                <div
                  key={`legend-${item.label}-${index}`}
                  className="flex items-center"
                >
                  <div
                    className="w-3 h-3 rounded-sm mr-1"
                    style={{ backgroundColor: item.color }}
                  />
                  <Text className="text-gray-600">{item.label}</Text>
                </div>
              ))}
            </div>
          )}
        </div>
        {actionButton && <div>{actionButton}</div>}
      </div>

      {/* 内容区域 */}
      {renderContent()}
    </div>
  )
}

export default ResourceUsageBar
