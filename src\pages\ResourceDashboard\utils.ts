/**
 * ResourceDashboard 通用工具函数
 * 用于处理数据精度格式化、时间序列数据处理和显示相关的通用逻辑
 */

import type { PrometheusData } from '@/api/prometheus'
import dayjs from 'dayjs'

/**
 * 时间序列数据点接口
 */
export interface TimeSeriesDataPoint {
  timestamp: number
  value: number
}

/**
 * 合并时间序列数据点接口
 */
export interface MergedTimeSeriesPoint {
  timestamp: number
  time: string
  values: number[] // 对应每个时间序列在该时间点的值
}

/**
 * 计算数值的小数位数
 * @param num 要计算小数位数的数值
 * @returns 小数位数
 */
export const getDecimalPlaces = (num: number): number => {
  if (Math.floor(num) === num) return 0
  const str = num.toString()
  if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
    return str.split('.')[1].length
  }
  if (str.indexOf('e-') !== -1) {
    const parts = str.split('e-')
    return Number.parseInt(parts[1], 10) + (parts[0].split('.')[1] || '').length
  }
  return 0
}

/**
 * 单个数值格式化函数
 * 根据参考数值计算统一精度，并转换为百分比格式
 * @param value 要格式化的数值
 * @param referenceValues 参考数值数组，用于计算统一精度
 * @param maxPrecision 最大精度限制，默认为4位小数
 * @returns 格式化后的百分比字符串
 */
export const formatSingleValue = (
  value: number,
  referenceValues: number[] = [],
  maxPrecision = 4,
): string => {
  const allValues = [value, ...referenceValues]
  const maxDecimalPlaces = Math.min(
    Math.max(...allValues.map(getDecimalPlaces)),
    maxPrecision,
  )
  return `${(value * 100).toFixed(maxDecimalPlaces)}%`
}

/**
 * 从 Prometheus 响应中提取时间序列数据
 * @param prometheusData Prometheus API 响应数据
 * @returns 时间序列数据点数组
 */
export const extractTimeSeries = (
  prometheusData: PrometheusData,
): TimeSeriesDataPoint[] => {
  if (!prometheusData?.result || prometheusData.result.length === 0) {
    return []
  }

  const result: TimeSeriesDataPoint[] = []

  // 遍历所有结果，合并时间序列数据
  for (const prometheusResult of prometheusData.result) {
    if (prometheusResult.values && Array.isArray(prometheusResult.values)) {
      for (const [timestamp, valueStr] of prometheusResult.values) {
        const value = Number.parseFloat(valueStr)
        if (!Number.isNaN(value)) {
          result.push({
            timestamp: timestamp * 1000, // 转换为毫秒时间戳
            value,
          })
        }
      }
    }
  }

  // 按时间戳排序
  return result.sort((a, b) => a.timestamp - b.timestamp)
}

/**
 * 格式化时间戳为显示字符串
 * @param timestamp 毫秒时间戳
 * @returns 格式化的时间字符串
 */
export const formatTimestamp = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 合并多个时间序列数据，按时间戳对齐
 * @param timeSeriesArray 多个时间序列数据数组
 * @returns 合并后的时间序列数据，包含所有时间点
 */
export const mergeTimeSeries = (
  timeSeriesArray: TimeSeriesDataPoint[][],
): MergedTimeSeriesPoint[] => {
  // 收集所有时间戳
  const allTimestamps = new Set<number>()
  for (const timeSeries of timeSeriesArray) {
    for (const point of timeSeries) {
      allTimestamps.add(point.timestamp)
    }
  }

  // 按时间戳排序
  const sortedTimestamps = Array.from(allTimestamps).sort()

  // 为每个时间戳创建合并的数据点
  const mergedData: MergedTimeSeriesPoint[] = []
  for (const timestamp of sortedTimestamps) {
    const values: number[] = []

    // 为每个时间序列查找该时间戳的值
    for (const timeSeries of timeSeriesArray) {
      const point = timeSeries.find((p) => p.timestamp === timestamp)
      values.push(point ? point.value : 0) // 如果没有找到值，使用 0
    }

    mergedData.push({
      timestamp,
      time: formatTimestamp(timestamp),
      values,
    })
  }

  return mergedData
}

/**
 * 计算数组的平均值
 * @param values 数值数组
 * @returns 平均值，如果数组为空则返回 0
 */
export const calculateAverage = (values: number[]): number => {
  if (values.length === 0) return 0
  const sum = values.reduce((acc, val) => acc + val, 0)
  return sum / values.length
}

/**
 * 计算数组的最大值
 * @param values 数值数组
 * @returns 最大值，保留2位小数
 */
export const calculateMax = (values: number[]): number => {
  if (values.length === 0) return 0
  return Number.parseFloat(Math.max(...values).toFixed(2))
}

/**
 * 计算数组的最小值
 * @param values 数值数组
 * @returns 最小值，保留2位小数
 */
export const calculateMin = (values: number[]): number => {
  if (values.length === 0) return 0
  return Number.parseFloat(Math.min(...values).toFixed(2))
}

/**
 * 生成时间范围内的模拟时间序列数据
 * @param start 开始时间字符串
 * @param end 结束时间字符串
 * @param minValue 最小值
 * @param maxValue 最大值
 * @param pointCount 数据点数量
 * @returns 模拟的时间序列数据
 */
export const generateMockTimeSeries = (
  start: string,
  end: string,
  minValue: number,
  maxValue: number,
  pointCount = 50,
): TimeSeriesDataPoint[] => {
  const startTime = new Date(start).getTime()
  const endTime = new Date(end).getTime()
  const duration = endTime - startTime

  const data: TimeSeriesDataPoint[] = []
  for (let i = 0; i < pointCount; i++) {
    const timestamp = startTime + (i * duration) / (pointCount - 1)
    const value = Number.parseFloat(
      (Math.random() * (maxValue - minValue) + minValue).toFixed(2),
    )
    data.push({ timestamp, value })
  }

  return data
}
