import { useGpuOverviewList, useTeamOverviewList } from '@/api/resource'
import CommonTable from '@/components/common/CommonTable'
import SectionTitle from '@/components/common/SectionTitle'
import TruncatedTags from '@/components/common/TruncatedTags'
import { Alert, Button, Checkbox, Modal, Table } from 'antd'
import type { TableProps } from 'antd'
import type { EChartsOption } from 'echarts'
import ReactECharts from 'echarts-for-react'
import type React from 'react'
import { useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import TeamResourceManagement from './TeamResourceManagement'

interface ResourceManagementProps {
  showTeamSection?: boolean
}

export const ResourceManagement: React.FC<ResourceManagementProps> = ({
  showTeamSection = true,
}) => {
  const [showOnlyConfiguredTeams, setShowOnlyConfiguredTeams] = useState(true)
  const [configModalOpen, setConfigModalOpen] = useState(false)
  const [selectedTeamId, setSelectedTeamId] = useState<number | null>(null)

  const {
    data: gpuOverviewData,
    isLoading: gpuOverviewLoading,
    refetch: refetchGpuOverview,
  } = useGpuOverviewList()

  const {
    data: teamOverviewData,
    isLoading: teamOverviewLoading,
    refetch: refetchTeamOverview,
  } = useTeamOverviewList()

  // Filter teams based on checkbox state
  const filteredTeamData = useMemo(() => {
    if (!teamOverviewData?.teamOverview) return []

    if (showOnlyConfiguredTeams) {
      return teamOverviewData.teamOverview.filter(
        (team) => team.clusterNamespaces && team.clusterNamespaces.length > 0,
      )
    }

    return teamOverviewData.teamOverview
  }, [teamOverviewData, showOnlyConfiguredTeams])

  // GPU Overview table columns
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const gpuOverviewColumns = useMemo<
    TableProps<{
      gpuAlias?: string
      gpuCore?: string
      gpuMemory?: string
      budget?: number
      allocation?: number
    }>['columns']
  >(() => {
    return [
      {
        title: '资源类型',
        dataIndex: 'gpuAlias',
        key: 'gpuAlias',
        width: 250,
        align: 'left',
        render: (gpuAlias: string) => gpuAlias || '-',
      },
      {
        title: 'CPU',
        dataIndex: 'gpuCore',
        key: 'gpuCore',
        width: 100,
        align: 'right',
        render: (gpuCore: string) => gpuCore + ' vCPU' || '-',
      },
      {
        title: '内存',
        dataIndex: 'gpuMemory',
        key: 'gpuMemory',
        width: 100,
        align: 'right',
        render: (gpuMemory: string) => gpuMemory + ' GiB' || '-',
      },
      {
        title: '团队预算',
        dataIndex: 'budget',
        key: 'budget',
        width: 100,
        align: 'right',
        render: (budget: number) => `${budget || 0} 卡`,
      },
      {
        title: '实际配备',
        dataIndex: 'allocation',
        key: 'allocation',
        width: 140,
        align: 'right',
        render: (allocation: number) => `${allocation || 0} 卡`,
      },
      {
        title: '超卖比（预算 / 实际）',
        key: 'ratio',
        width: 180,
        align: 'right',
        render: (_, record) => {
          const percentage =
            record.allocation && record.allocation > 0
              ? ((record.budget || 0) / record.allocation).toFixed(2)
              : 0
          return percentage
        },
      },
    ]
  }, [teamOverviewData])

  // Team Overview table columns
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const teamOverviewColumns = useMemo<
    TableProps<{
      id?: number
      name?: string
      teamId?: string
      clusterNamespaces?: {
        cluster?: string
        namespaces?: string[]
      }[]
      gpuQuotas?: {
        gpuAlias?: string
        gpuCore?: string
        gpuMemory?: string
        nums?: number
      }[]
    }>['columns']
  >(() => {
    return [
      {
        title: '团队名称',
        dataIndex: 'name',
        key: 'name',
        width: 120,
        render: (name: string) => name || '-',
        filters:
          teamOverviewData?.teamOverview
            ?.map((team) => ({
              text: team.name || '-',
              value: team.name || '',
            }))
            .filter(
              (item, index, self) =>
                index === self.findIndex((t) => t.value === item.value),
            ) || [],
        onFilter: (value, record) => record.name === value,
        filterSearch: true,
      },
      {
        title: '集群/实例名称',
        key: 'clusters',
        width: 250,
        render: (_, record) => {
          if (!record.clusterNamespaces?.length)
            return (
              <span className="inline-block text-xs text-white bg-[#F1786D] rounded-lg px-2 py-0 mr-2 mb-2">
                未配置
              </span>
            )

          const flattenedItems = record.clusterNamespaces.reduce<
            { cluster: string; namespace: string }[]
          >((acc, item) => {
            if (item.cluster && item.namespaces?.length) {
              for (const namespace of item.namespaces) {
                acc.push({
                  cluster: item.cluster,
                  namespace,
                })
              }
            }
            return acc
          }, [])

          const tagItems = flattenedItems.map((item) => ({
            key: `${record.id}-${item.cluster}-${item.namespace}`,
            label: `${item.cluster}/${item.namespace}`,
          }))

          return <TruncatedTags items={tagItems} autoResize />
        },
      },
      {
        title: '配额详情',
        children: [
          {
            title: '显卡型号与数量',
            key: 'gpuQuotas',
            width: 200,
            render: (_, record) => {
              if (!record.gpuQuotas?.length)
                return (
                  <span className="inline-block text-xs text-white bg-[#F1786D] rounded-lg px-2 py-0 mr-2 mb-2">
                    未配置
                  </span>
                )

              const tagItems = record.gpuQuotas.map((quota, index) => ({
                key: `${record.id}-${quota.gpuAlias}-${index}`,
                label: `${quota.gpuAlias} × ${quota.nums}`,
              }))

              return <TruncatedTags items={tagItems} autoResize />
            },
          },
          {
            title: '卡数总计',
            key: 'totalCards',
            width: 100,
            align: 'right',
            render: (_, record) => {
              const totalCards =
                record.gpuQuotas?.reduce(
                  (sum, quota) => sum + (quota.nums || 0),
                  0,
                ) || 0
              return `${totalCards} 卡`
            },
          },
        ],
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        render: (_, record) => (
          <div className="flex items-center gap-2">
            <Button
              size="small"
              type="link"
              onClick={() => handleConfigClick(record.id!)}
            >
              配置
            </Button>
            <Button
              size="small"
              type="link"
              onClick={() =>
                window.open('/resource-dashboard?team=' + record.id, '_blank')
              }
            >
              资源使用情况
            </Button>
          </div>
        ),
      },
    ]
  }, [])

  // Chart configuration
  const chartOption: EChartsOption = useMemo(() => {
    if (!gpuOverviewData?.gpuOverview) return {}

    const gpuData = gpuOverviewData.gpuOverview

    // Prepare chart data
    const categories = gpuData.map((item) => item.gpuAlias || '').reverse()
    const budgetData = gpuData.map((item) => item.budget || 0).reverse()
    const allocationData = gpuData.map((item) => item.allocation || 0).reverse()

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: any) => {
          let result = params[0].name + '<br/>'
          for (const param of params) {
            result +=
              param.marker + param.seriesName + ': ' + param.value + ' 卡<br/>'
          }
          return result
        },
      },
      legend: {
        data: ['实际配备 (卡)', '团队预算 (卡)'],
        top: 10,
        itemGap: 20,
      },
      grid: {
        left: '3%',
        right: '4%',
        top: '40px',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: '数量',
        axisLabel: {
          formatter: '{value}',
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          fontSize: 12,
          color: '#666',
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
      },
      series: [
        {
          name: '实际配备 (卡)',
          type: 'bar',
          data: allocationData,
          itemStyle: {
            color: '#52c41a82',
          },
          barWidth: 10,
          barGap: 0,
          label: {
            show: true,
            position: 'right',
            formatter: '{c} 卡',
            color: '#666',
            fontSize: 11,
          },
        },
        {
          name: '团队预算 (卡)',
          type: 'bar',
          data: budgetData,
          itemStyle: {
            color: '#ffa14278',
          },
          barWidth: 10,
          label: {
            show: true,
            position: 'right',
            formatter: '{c} 卡',
            color: '#666',
            fontSize: 11,
          },
        },
      ],
    }
  }, [gpuOverviewData])

  // Handle config button click
  const handleConfigClick = (teamId: number) => {
    setSelectedTeamId(teamId)
    setConfigModalOpen(true)
  }

  // Get selected team name
  const selectedTeamName = useMemo(() => {
    if (!selectedTeamId || !teamOverviewData?.teamOverview) return ''
    const team = teamOverviewData.teamOverview.find(
      (team) => team.id === selectedTeamId,
    )
    return team?.name || ''
  }, [selectedTeamId, teamOverviewData])

  // Handle config modal close
  const handleConfigModalClose = () => {
    setConfigModalOpen(false)
    setSelectedTeamId(null)
    // 关闭配置模态框时刷新数据
    refetchGpuOverview()
    refetchTeamOverview()
  }

  // Calculate totals for Alert message
  const { totalBudget, totalAllocation, oversellRatio } = useMemo(() => {
    if (!gpuOverviewData?.gpuOverview) {
      return { totalBudget: 0, totalAllocation: 0, oversellRatio: 0 }
    }

    const budget = gpuOverviewData.gpuOverview.reduce(
      (sum, record) => sum + (record.budget || 0),
      0,
    )
    const allocation = gpuOverviewData.gpuOverview.reduce(
      (sum, record) => sum + (record.allocation || 0),
      0,
    )
    const ratio = allocation > 0 ? (budget / allocation).toFixed(2) : '0.00'

    return {
      totalBudget: budget,
      totalAllocation: allocation,
      oversellRatio: ratio,
    }
  }, [gpuOverviewData])

  return (
    <div>
      {/* GPU Overview Section */}
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <SectionTitle title="集团资源池详情" />
        </div>
        <Alert
          message={
            <div>
              整体情况：团队预算总计 {totalBudget} 卡，实际配比{' '}
              {totalAllocation} 卡；集团资源池
              <strong>超卖比 {oversellRatio}</strong> 。
            </div>
          }
          type="info"
          showIcon
          className="mb-4"
        />
        <div className="flex">
          <CommonTable
            size="small"
            className="flex-grow w-1"
            rowKey={(record, index) =>
              record.gpuAlias || `gpu-overview-${index || 0}`
            }
            loading={gpuOverviewLoading}
            columns={gpuOverviewColumns}
            dataSource={gpuOverviewData?.gpuOverview || []}
            pagination={false}
            scroll={{ x: '100%' }}
            sticky={{ offsetHeader: 0, offsetScroll: 0 }}
            summary={(pageData) => {
              const totalBudget = pageData.reduce(
                (sum, record) => sum + (record.budget || 0),
                0,
              )
              const totalAllocation = pageData.reduce(
                (sum, record) => sum + (record.allocation || 0),
                0,
              )
              const avgRatio =
                totalAllocation > 0
                  ? (totalBudget / totalAllocation).toFixed(2)
                  : '0.00'

              return (
                <Table.Summary fixed="bottom">
                  <Table.Summary.Row style={{ backgroundColor: '#D2CEFA' }}>
                    <Table.Summary.Cell index={0} colSpan={2} />
                    <Table.Summary.Cell index={3} colSpan={2} align="right">
                      团队预算：{totalBudget} 卡
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4} align="right">
                      实际配备：{totalAllocation} 卡
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align="right">
                      整体超卖比：{avgRatio}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              )
            }}
          />
          <ReactECharts
            option={chartOption}
            style={{
              width: '35vw',
              height: gpuOverviewData?.gpuOverview?.length
                ? gpuOverviewData?.gpuOverview?.length * 35 + 90
                : 200,
            }}
            notMerge={true}
            lazyUpdate={true}
            showLoading={gpuOverviewLoading}
            loadingOption={{
              text: '数据加载中...',
              color: '#7367EF',
              textColor: '#333333',
              maskColor: 'rgba(255, 255, 255, 0.8)',
            }}
          />
        </div>
      </div>

      {/* Team Overview Section */}
      {showTeamSection && (
        <div>
          <div className="flex items-center gap-4">
            <SectionTitle title="团队信息" />
            <div className="flex items-center mb-4 ">
              <Checkbox
                checked={showOnlyConfiguredTeams}
                onChange={(e) => setShowOnlyConfiguredTeams(e.target.checked)}
              >
                只看已配置集群空间的团队
              </Checkbox>
            </div>
          </div>
          <CommonTable
            size="small"
            rowKey={(record, index) => record.teamId || `team-${index || 0}`}
            loading={teamOverviewLoading}
            columns={teamOverviewColumns}
            dataSource={filteredTeamData}
            pagination={false}
            scroll={{ y: 'calc(40vh - 150px)' }}
            summary={(pageData) => {
              const totalCards = pageData.reduce((sum, record) => {
                const recordTotal =
                  record.gpuQuotas?.reduce(
                    (cardSum, quota) => cardSum + (quota.nums || 0),
                    0,
                  ) || 0
                return sum + recordTotal
              }, 0)

              return (
                <Table.Summary fixed="bottom">
                  <Table.Summary.Row style={{ backgroundColor: '#D2CEFA' }}>
                    <Table.Summary.Cell index={0} colSpan={2} />
                    <Table.Summary.Cell index={2} align="right">
                      总计
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">
                      {totalCards} 卡
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} />
                  </Table.Summary.Row>
                </Table.Summary>
              )
            }}
          />
        </div>
      )}

      {/* Team Configuration Modal */}
      {showTeamSection && (
        <Modal
          title={
            selectedTeamName
              ? `${selectedTeamName} - 团队资源配置`
              : '团队资源配置'
          }
          open={configModalOpen}
          onCancel={handleConfigModalClose}
          footer={null}
          width={1200}
          centered
          destroyOnClose
        >
          {selectedTeamId && <TeamResourceManagement teamId={selectedTeamId} />}
        </Modal>
      )}
    </div>
  )
}

export default ResourceManagement
