import {
  type MlopsInternalModelDtoGpuQuota,
  useClusterNamespaceList,
  useCreateGpuQuota,
  useDeleteClusterNamespace,
  useDeleteGpuQuota,
  useGpuQuotaList,
  useUpdateGpuQuota,
} from '@/api/team'
import AddClusterModal from '@/components/ResourceManagement/AddClusterModal'
import AddGpuQuotaModal from '@/components/ResourceManagement/AddGpuQuotaModal'
import CommonTable from '@/components/common/CommonTable'
import SectionTitle from '@/components/common/SectionTitle'
import useUserStore from '@/stores/user'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons'
import { Button, Divider, Modal, Table, Tag, message } from 'antd'
import type { TableProps } from 'antd'
import type React from 'react'
import { useMemo, useState } from 'react'
import { EnumUserRole } from '../MemberManagement/setting'

interface TeamResourceManagementProps {
  teamId?: number
  showClusterSection?: boolean
}

export const TeamResourceManagement: React.FC<TeamResourceManagementProps> = ({
  teamId,
  showClusterSection = true,
}) => {
  const [modal, contextHolder] = Modal.useModal()
  const currentTeam = useUserStore((state) => state.currentTeam)
  const effectiveTeamId = teamId || currentTeam
  console.log(effectiveTeamId)
  const [addClusterModalOpen, setAddClusterModalOpen] = useState(false)
  const [addGpuQuotaModalOpen, setAddGpuQuotaModalOpen] = useState(false)
  const [editingGpuQuota, setEditingGpuQuota] =
    useState<MlopsInternalModelDtoGpuQuota | null>(null)
  const teamAndRoleMap = useUserStore((state) => state.teamAndRoleMap)
  const isTeamAdmin = teamAndRoleMap[effectiveTeamId] === EnumUserRole.Admin

  const {
    data: clusterData,
    isLoading: clusterLoading,
    error: clusterError,
    refetch: refetchCluster,
  } = useClusterNamespaceList({
    teamId: effectiveTeamId,
  })

  const {
    data: gpuData,
    isLoading: gpuLoading,
    error: gpuError,
    refetch: refetchGpu,
  } = useGpuQuotaList({
    teamId: effectiveTeamId,
  })

  // Mutation hooks
  const deleteClusterMutation = useDeleteClusterNamespace()
  const deleteGpuQuotaMutation = useDeleteGpuQuota()
  const updateGpuQuotaMutation = useUpdateGpuQuota()
  const createGpuQuotaMutation = useCreateGpuQuota()

  // Transform cluster data to expand namespaces into separate rows
  const expandedClusterData = useMemo(() => {
    if (!clusterData?.clusterNamespaces) return []

    return clusterData.clusterNamespaces.reduce<
      Array<{
        cluster: string
        namespace: string
        originalIndex: number
        namespaceIndex: number
      }>
    >((acc, item, originalIndex) => {
      const namespaces = item.namespaces?.length ? item.namespaces : ['-']

      return acc.concat(
        namespaces.map((namespace, namespaceIndex) => ({
          cluster: item.cluster || '',
          namespace,
          originalIndex,
          namespaceIndex,
        })),
      )
    }, [])
  }, [clusterData])

  // Cluster table columns
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const clusterColumns = useMemo<
    TableProps<{
      cluster: string
      namespace: string
      originalIndex: number
      namespaceIndex: number
    }>['columns']
  >(() => {
    return [
      {
        title: '集群名称',
        dataIndex: 'cluster',
        key: 'cluster',
        render: (cluster: string) => cluster || '-',
      },
      {
        title: '命名空间',
        dataIndex: 'namespace',
        key: 'namespace',
        render: (namespace: string) => {
          if (namespace === '-') return '-'
          return namespace
        },
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        render: (_, record) => (
          <div className="flex items-center">
            <Button
              size="small"
              color="danger"
              variant="link"
              disabled={!isTeamAdmin}
              onClick={() => confirmDeleteCluster(record)}
            >
              删除
            </Button>
          </div>
        ),
      },
    ]
  }, [effectiveTeamId, isTeamAdmin])

  // Calculate total GPU count
  const totalGpuCount = useMemo(() => {
    return (gpuData?.gpuQuota || []).reduce(
      (sum, item) => sum + (item.nums || 0),
      0,
    )
  }, [gpuData?.gpuQuota])

  // GPU table columns
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const gpuColumns = useMemo<
    TableProps<MlopsInternalModelDtoGpuQuota>['columns']
  >(() => {
    return [
      {
        title: '显卡型号',
        dataIndex: 'gpuAlias',
        key: 'gpuAlias',
        width: 150,
        render: (gpuAlias: string) => gpuAlias || '-',
      },
      {
        title: '核心',
        dataIndex: 'gpuCore',
        key: 'gpuCore',
        width: 120,
        align: 'right',
        render: (gpuCore: string) => gpuCore + ' vCPU' || '-',
      },
      {
        title: '显存',
        dataIndex: 'gpuMemory',
        key: 'gpuMemory',
        width: 120,
        align: 'right',
        render: (gpuMemory: string) => gpuMemory + ' GiB' || '-',
      },
      {
        title: '数量',
        dataIndex: 'nums',
        key: 'nums',
        width: 100,
        align: 'right',
        render: (nums: number) => (nums ?? 0) + ' 卡',
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        align: 'center',
        render: (_, record) => (
          <div className="flex items-center justify-center">
            <Button
              size="small"
              color="primary"
              variant="link"
              disabled={!isTeamAdmin}
              onClick={() => handleEditGpuQuota(record)}
            >
              配置
            </Button>
            <Button
              size="small"
              color="danger"
              variant="link"
              disabled={!isTeamAdmin}
              onClick={() => confirmDeleteGpuQuota(record)}
            >
              删除
            </Button>
          </div>
        ),
      },
    ]
  }, [effectiveTeamId, isTeamAdmin])

  // Handle add cluster
  const handleAddCluster = () => {
    setAddClusterModalOpen(true)
  }

  // Handle cluster modal confirm
  const handleClusterModalConfirm = (data: {
    cluster: string
    namespaces: string[]
  }) => {
    // TODO: Implement API call to add cluster
    setAddClusterModalOpen(false)
    // Refetch cluster data after adding
    refetchCluster()
  }

  // Handle cluster modal cancel
  const handleClusterModalCancel = () => {
    setAddClusterModalOpen(false)
  }

  // Handle add GPU quota
  const handleAddGpuQuota = () => {
    setAddGpuQuotaModalOpen(true)
  }

  // Handle GPU quota modal confirm
  const handleGpuQuotaModalConfirm = async (data: {
    gpuAlias: string
    gpuType: string
    gpuCore: string
    gpuMemory: string
    nums: number
  }) => {
    if (!effectiveTeamId) {
      message.error('请先选择团队')
      return
    }

    try {
      if (editingGpuQuota) {
        // Update existing GPU quota
        await updateGpuQuotaMutation.mutateAsync({
          teamId: effectiveTeamId,
          gpuQuota: data,
        })
        message.success('更新GPU配额成功')
      } else {
        // Create new GPU quota
        await createGpuQuotaMutation.mutateAsync({
          teamId: effectiveTeamId,
          gpuQuota: data,
        })
        message.success('添加GPU配额成功')
      }
      setAddGpuQuotaModalOpen(false)
      setEditingGpuQuota(null)
      refetchGpu()
    } catch (error) {
      console.error('GPU quota operation error:', error)
    }
  }

  // Handle GPU quota modal cancel
  const handleGpuQuotaModalCancel = () => {
    setAddGpuQuotaModalOpen(false)
    setEditingGpuQuota(null)
  }

  // Confirm delete cluster namespace
  const confirmDeleteCluster = (record: {
    cluster: string
    namespace: string
  }) => {
    modal.confirm({
      title: '确认删除',
      content: `确定要删除集群 "${record.cluster}" 的命名空间 "${record.namespace}" 吗？`,
      centered: true,
      okText: '确定',
      cancelText: '取消',

      onOk: () => handleDeleteCluster(record),
    })
  }

  // Handle delete cluster namespace
  const handleDeleteCluster = async (record: {
    cluster: string
    namespace: string
  }) => {
    if (!effectiveTeamId) {
      message.error('请先选择团队')
      return
    }

    try {
      await deleteClusterMutation.mutateAsync({
        teamId: effectiveTeamId,
        cluster: record.cluster,
        namespace: record.namespace,
      })
      message.success('删除集群成功')
      refetchCluster()
    } catch (error) {
      console.error('Delete cluster error:', error)
    }
  }

  // Handle edit GPU quota
  const handleEditGpuQuota = (record: MlopsInternalModelDtoGpuQuota) => {
    setEditingGpuQuota(record)
    setAddGpuQuotaModalOpen(true)
  }

  // Confirm delete GPU quota
  const confirmDeleteGpuQuota = (record: MlopsInternalModelDtoGpuQuota) => {
    modal.confirm({
      title: '确认删除',
      content: `确定要删除 "${record.gpuAlias}" GPU配额吗？`,
      okText: '确定',
      cancelText: '取消',
      centered: true,
      onOk: () => handleDeleteGpuQuota(record),
    })
  }

  // Handle delete GPU quota
  const handleDeleteGpuQuota = async (
    record: MlopsInternalModelDtoGpuQuota,
  ) => {
    if (!effectiveTeamId) {
      message.error('请先选择团队')
      return
    }

    try {
      await deleteGpuQuotaMutation.mutateAsync({
        teamId: effectiveTeamId,
        gpuType: record.gpuType,
      })
      message.success('删除GPU配额成功')
      refetchGpu()
    } catch (error) {
      console.error('Delete GPU quota error:', error)
    }
  }

  return (
    <div>
      {/* Cluster Information Section */}
      {showClusterSection && (
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <SectionTitle title="集群信息" className="mb-0" />
            <div className="flex items-center justify-center gap-2">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                disabled={!isTeamAdmin}
                onClick={handleAddCluster}
              >
                添加集群
              </Button>
            </div>
          </div>
          <CommonTable
            size="small"
            rowKey={(
              record: {
                cluster: string
                namespace: string
                originalIndex: number
                namespaceIndex: number
              },
              index?: number,
            ) =>
              `${record.cluster}-${record.namespace}-${record.originalIndex}-${record.namespaceIndex}` ||
              `cluster-${index || 0}`
            }
            loading={clusterLoading}
            columns={clusterColumns}
            dataSource={expandedClusterData}
            pagination={false}
            scroll={{ y: 'calc(40vh - 100px)' }}
          />
        </div>
      )}

      {/* GPU Quota Section */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <SectionTitle title="GPU配额" className="mb-0" />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            disabled={!isTeamAdmin}
            onClick={handleAddGpuQuota}
          >
            添加配额
          </Button>
        </div>
        <CommonTable
          size="small"
          rowKey={(record: MlopsInternalModelDtoGpuQuota, index?: number) =>
            record.gpuAlias || `gpu-${index || 0}`
          }
          loading={gpuLoading}
          columns={gpuColumns}
          dataSource={gpuData?.gpuQuota || []}
          pagination={false}
          scroll={{ y: 'calc(40vh - 100px)' }}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row style={{ backgroundColor: '#D2CEFA' }}>
                <Table.Summary.Cell index={0} colSpan={3} />
                <Table.Summary.Cell index={3} align="right">
                  总计 {totalGpuCount} 卡
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4} />
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </div>

      {/* Add Cluster Modal */}
      {showClusterSection && (
        <AddClusterModal
          open={addClusterModalOpen}
          onCancel={handleClusterModalCancel}
          onConfirm={handleClusterModalConfirm}
          teamId={effectiveTeamId}
          defaultCluster={clusterData?.clusterNamespaces?.[0]?.cluster}
        />
      )}

      {/* Add GPU Quota Modal */}
      <AddGpuQuotaModal
        open={addGpuQuotaModalOpen}
        onCancel={handleGpuQuotaModalCancel}
        onConfirm={handleGpuQuotaModalConfirm}
        teamId={effectiveTeamId}
        initialData={editingGpuQuota}
      />
      {contextHolder}
    </div>
  )
}

export default TeamResourceManagement
