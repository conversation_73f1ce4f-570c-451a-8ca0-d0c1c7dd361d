import type { SettingItem } from '@/api/setting'
import CommonTable from '@/components/common/CommonTable'
import { Button, Popconfirm, Space } from 'antd'
import type { TableProps } from 'antd'
import type React from 'react'

interface ConfigTableProps {
  tableData: SettingItem[]
  onChangePage: (page: number, pageSize: number) => void
  onEditConfig: (data: SettingItem) => void
  onDeleteItem: (id: number) => void
  total?: number
  loading: boolean
}

const ConfigTable: React.FC<ConfigTableProps> = ({
  tableData,
  onChangePage,
  onDeleteItem,
  onEditConfig,
  total,
  loading,
}) => {
  const columns: TableProps<SettingItem>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'KEY',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: 'CATEGORY',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: 'DESC',
      dataIndex: 'desc',
      key: 'desc',
    },
    {
      title: 'UPDATEDAT',
      dataIndex: 'updated_at',
      key: 'updated_at',
    },
    {
      title: 'OPTION',
      key: 'option',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            style={{ margin: '0', padding: 0 }}
            onClick={() => onEditConfig(record)}
          >
            配置
          </Button>
          <Popconfirm
            title="删除"
            description="确认是否删除?"
            onConfirm={() => onDeleteItem(record.id)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <CommonTable<SettingItem>
      columns={columns}
      rowKey="id"
      dataSource={tableData}
      loading={loading}
      pagination={{
        onChange: onChangePage,
        total: total,
        showTotal: (total) => `共 ${total} 条`,
      }}
    />
  )
}

export default ConfigTable
