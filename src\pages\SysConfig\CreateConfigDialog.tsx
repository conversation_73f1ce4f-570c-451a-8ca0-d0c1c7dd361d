import {
  type SettingCreate,
  type SettingItem,
  createSetting,
  updateSetting,
} from '@/api/setting'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, Input, Modal, Select } from 'antd'
import form from 'antd/es/form'
import TextArea from 'antd/es/input/TextArea'
import {
  type Ref,
  forwardRef,
  memo,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'

type Props = {
  onSuccess?: () => void
}
export type SettingModalHandle = {
  showModal: (type: 'create' | 'edit', data?: SettingItem) => void
}
export default memo(
  forwardRef<SettingModalHandle, Props>(({ onSuccess }, ref) => {
    const [form] = Form.useForm<SettingCreate>()
    const categoryList = ['system', 'doc', 'test']
    const [isModalVisible, setIsModalVisible] = useState(false)
    const id = useRef<number>()
    const type = useRef<'create' | 'edit'>('create')
    const data = {
      key: '',
      value: '',
      category: '',
      desc: '',
    }

    // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
    useImperativeHandle(
      ref,
      () => ({
        showModal: (t: 'create' | 'edit', data?: SettingItem) => {
          setIsModalVisible(true)
          type.current = t
          if (data) {
            id.current = data.id
            form.setFieldsValue(data)
          } else {
            form.resetFields()
          }
        },
      }),
      [],
    )

    const onFinish = (values: any) => {
      // handleConfig(id ? { id, ...values } : values, () => setModalVisible(false))
      console.log(values)
    }

    const onFinishFailed = (errorInfo: any) => {
      console.log('Failed:', errorInfo)
    }
    const handleOk = async () => {
      await form.validateFields()
      const data = form.getFieldsValue()
      if (type.current === 'create') await createSetting(data)
      else await updateSetting(id.current!, data)
      if (onSuccess) onSuccess()
      setIsModalVisible(false)
    }

    const handleCancel = () => {
      setIsModalVisible(false)
    }
    return (
      <Modal
        title={`${type.current === 'create' ? '创建' : '修改'}配置`}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          name="configForm"
          layout="vertical"
          initialValues={data}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            label="Key"
            name="key"
            rules={[{ required: true, message: 'Please input the key!' }]}
          >
            <Input disabled={type.current === 'edit'} />
          </Form.Item>

          <Form.Item
            label="Value"
            name="value"
            rules={[{ required: true, message: 'Please input the value!' }]}
          >
            <TextArea rows={5} />
          </Form.Item>

          <Form.Item
            label="Category"
            name="category"
            rules={[{ required: true, message: 'Please select the category!' }]}
          >
            <Select>
              {categoryList.map((category: any) => (
                <Select.Option key={category} value={category}>
                  {category}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="Description" name="desc">
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    )
  }),
)
