import {
  type SettingCreate,
  type SettingItem,
  delSetting,
  useSettingList,
} from '@/api/setting.ts'
import { AudioOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import { Alert, Input, Space } from 'antd'
import type { GetProps } from 'antd'
import type React from 'react'
import { useRef, useState } from 'react'
import ConfigTable from './ConfigTable'
import ConfigDialog, { type SettingModalHandle } from './CreateConfigDialog.tsx'

type SearchProps = GetProps<typeof Input.Search>

const ConfigManger: React.FC = () => {
  const [search, setSearch] = useState<string>('')
  const [page, setPage] = useState(1)
  const [size, setSize] = useState(10)
  const modalRef = useRef<SettingModalHandle>(null)

  const { data, refetch, isLoading } = useSettingList({
    page,
    size,
    s_key: search,
  })
  // 搜索
  const onSearch: SearchProps['onSearch'] = (value) => {
    setSearch(value)
  }
  // 变更分页
  const onChangePage = (page: number, size: number) => {
    setPage(page)
    setSize(size)
  }
  // 删除条目
  const onDeleteItem = async (id: number) => {
    // 删除条目之后 获取列表一次
    await delSetting(id)
    refetch()
  }
  const onEditConfig = (data: SettingItem) => {
    modalRef.current?.showModal('edit', data)
  }

  const showModal = () => {
    modalRef.current?.showModal('create')
  }

  return (
    <div title="系统配置">
      <Button
        type="primary"
        variant="solid"
        icon={<PlusOutlined />}
        onClick={showModal}
      >
        创建配置
      </Button>
      <ConfigDialog ref={modalRef} onSuccess={refetch} />
      <div className="flex justify-between py-4">
        <div className="flex items-center">
          <div className="w-[100px] font-semibold">KEY</div>
          <Input
            placeholder="Enter keyword to search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') refetch()
            }}
          />
        </div>
      </div>
      <ConfigTable
        tableData={data?.list || []}
        onChangePage={onChangePage}
        onDeleteItem={onDeleteItem}
        onEditConfig={onEditConfig}
        total={data?.total}
        loading={isLoading}
      />
    </div>
  )
}

export default ConfigManger
