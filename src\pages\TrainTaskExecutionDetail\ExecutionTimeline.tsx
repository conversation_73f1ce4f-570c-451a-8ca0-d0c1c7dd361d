import { useTrainTaskExecutionConditions } from '@/api/traintask'
import leaf from '@/assets/leaf.svg'
import load from '@/assets/load.svg'
import {
  CheckCircleFilled,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleFilled,
  LoadingOutlined,
  MinusCircleFilled,
} from '@ant-design/icons'
import { Timeline } from 'antd'
import { useEffect, useMemo, useState } from 'react'

interface ExecutionTimelineProps {
  executionId: number
}

export default function ExecutionTimeline({
  executionId,
}: ExecutionTimelineProps) {
  const { data: conditionsData, isLoading } =
    useTrainTaskExecutionConditions(executionId)
  const [currentTime, setCurrentTime] = useState(new Date())

  // 每秒更新当前时间，用于实时计算耗时
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // 状态映射
  const getStatusConfig = (status: string) => {
    const statusMap = {
      SUBMITTED: {
        label: '任务提交',
        color: 'blue',
        icon: <CheckCircleOutlined />,
      },
      PENDING: {
        label: '任务等待',
        color: 'orange',
        icon: <img className="w-[14px]" src={leaf} alt="leaf" />,
      },
      RUNNING: {
        label: '任务执行',
        color: 'green',
        icon: <img className="w-[14px]" src={load} alt="load" />,
      },
      SUCCEEDED: {
        label: '任务成功',
        color: 'green',
        icon: <CheckCircleFilled className="text-[#52C41A]" />,
      },
      FAILED: {
        label: '任务失败',
        color: 'red',
        icon: <CloseCircleFilled className="text-[#F1786D]" />,
      },
      CANCELLED: {
        label: '任务取消',
        color: 'gray',
        icon: <MinusCircleFilled className="text-[#777777]" />,
      },
    }
    return (
      statusMap[status as keyof typeof statusMap] || {
        label: status,
        color: 'gray',
        icon: <ClockCircleOutlined />,
      }
    )
  }

  // 计算持续时间
  const calculateDuration = useMemo(() => {
    return (
      currentTimestamp: string,
      nextTimestamp?: string,
      status?: string,
      isLatest?: boolean,
    ) => {
      const current = new Date(currentTimestamp)
      let end: Date

      // 只有 PENDING 和 RUNNING 状态需要显示耗时
      if (status !== 'PENDING' && status !== 'RUNNING') {
        return null
      }

      // 如果是最新状态（没有后续状态）且为 PENDING 或 RUNNING，使用当前时间实时计算
      // 否则使用下一个状态的时间戳
      if (isLatest && (status === 'PENDING' || status === 'RUNNING')) {
        end = currentTime
      } else if (nextTimestamp) {
        end = new Date(nextTimestamp)
      } else {
        return null
      }

      const diffMs = end.getTime() - current.getTime()

      if (diffMs < 0) return '0 秒'

      const seconds = Math.floor(diffMs / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)

      if (hours > 0) {
        return `${hours} 时 ${minutes % 60} 分钟 ${seconds % 60} 秒`
      }
      if (minutes > 0) {
        return `${minutes} 分钟 ${seconds % 60} 秒`
      }
      return `${seconds} 秒`
    }
  }, [currentTime])

  // 生成时间线数据
  const timelineItems = useMemo(() => {
    if (!conditionsData?.conditions || conditionsData.conditions.length === 0) {
      return []
    }

    const conditions = conditionsData.conditions

    // 由于数据是由新到旧排序，需要反转以符合时间线从上到下的显示逻辑
    const reversedConditions = [...conditions].reverse()

    return reversedConditions.map((condition, index) => {
      const statusConfig = getStatusConfig(condition.status)
      // 在反转后的数组中，下一个元素是时间上的后续状态
      const nextCondition = reversedConditions[index + 1]
      // 检查是否是原始数据中的第一个（最新的）状态
      const isLatest = conditions[0] === condition

      const duration = calculateDuration(
        condition.timestamp,
        nextCondition?.timestamp,
        condition.status,
        isLatest,
      )

      return {
        dot: statusConfig.icon,
        color: statusConfig.color,
        children: (
          <div>
            <div className="font-medium text-base mb-1">
              {statusConfig.label}
            </div>
            <div className="text-gray-500 text-sm mb-1">
              {new Date(condition.timestamp).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
              })}
            </div>
            <div className="text-gray-500 text-sm mb-1">
              操作人：{condition.operatorName} {condition.operatorAccount}
            </div>
            {duration && (
              <div className="text-gray-500 text-sm">
                耗时：{duration}{' '}
                {/* {isLatest &&
                (condition.status === 'PENDING' ||
                  condition.status === 'RUNNING')
                  ? '(实时更新)'
                  : ''} */}
              </div>
            )}
          </div>
        ),
      }
    })
    // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  }, [conditionsData?.conditions, calculateDuration, getStatusConfig])

  if (isLoading) {
    return (
      <div className="w-96 p-3 flex items-center justify-center">
        <LoadingOutlined className="text-lg" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  return <Timeline className="w-96 p-3" items={timelineItems} reverse={true} />
}
