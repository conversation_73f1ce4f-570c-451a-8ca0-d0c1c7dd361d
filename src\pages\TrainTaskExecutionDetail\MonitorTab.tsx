import {
  getCpuUsageQuery,
  getCpuUtilizationQuery,
  getGpuCoreUtilizationQuery,
  getGpuMemoryCoreUsageQuery,
  getGpuMemoryUsageQuery,
  getGpuMemoryUtilizationQuery,
  getMemoryUsageQuery,
  getMemoryUtilizationQuery,
  getPrometheusDetail,
} from '@/api/prometheus'
import {
  useTrainTaskExecutionDetail,
  useTrainTaskExecutionWorkers,
} from '@/api/traintask'
import InfoTooltip from '@/components/common/InfoTooltip'
import { ScrollArea } from '@/components/ui/scroll-area'
import { EnumTrainTaskExecutionState } from '@/pages/TrainTaskExecutionManagement/setting'
import {
  Collapse,
  type CollapseProps,
  DatePicker,
  Form,
  Radio,
  Select,
} from 'antd'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { EChartsOption } from 'echarts'
import * as echarts from 'echarts'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'
import PrometheusChart from '../ResourceDashboard/components/PrometheusChart'

interface MonitorFormValues {
  worker?: string[]
  timeRange?: [Dayjs, Dayjs]
}

interface GpuChartData {
  times: string[]
  values: number[]
  workers?: Array<{
    name: string
    values: number[]
  }>
}

// 创建动态时间预设函数
const createTimePresets = (executionDetail: any) => {
  const taskData = executionDetail?.data
  const isRunning =
    taskData &&
    (taskData.status === EnumTrainTaskExecutionState.PENDING ||
      taskData.status === EnumTrainTaskExecutionState.RUNNING)

  // 如果任务正在运行，使用当前时间；如果已结束，使用结束时间
  const referenceTime = isRunning
    ? dayjs()
    : taskData?.endTime
      ? dayjs(taskData.endTime)
      : dayjs()

  return [
    {
      label: '最近 30 分钟',
      value: () => [referenceTime.subtract(30, 'minute'), referenceTime],
    },
    {
      label: '最近 6 小时',
      value: () => [referenceTime.subtract(6, 'hour'), referenceTime],
    },
    {
      label: '最近 24 小时',
      value: () => [referenceTime.subtract(24, 'hour'), referenceTime],
    },
    {
      label: '最近 3 天',
      value: () => [referenceTime.subtract(3, 'day'), referenceTime],
    },
    {
      label: '最近 7 天',
      value: () => [referenceTime.subtract(7, 'day'), referenceTime],
    },
    {
      label: '最近 14 天',
      value: () => [referenceTime.subtract(14, 'day'), referenceTime],
    },
  ]
}

export default function MonitorTab({
  clusterName,
  namespace,
}: {
  clusterName?: string
  namespace?: string
}) {
  const { id } = useParams<{ id: string }>()
  const [form] = Form.useForm<MonitorFormValues>()
  const [activeTab, setActiveTab] = useState('1')
  const [collapseActiveKey, setCollapseActiveKey] = useState(['1', '2'])
  const [isInitialTimeRange, setIsInitialTimeRange] = useState(true)

  // 创建refs来引用Collapse面板
  const collapseRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  // 创建refs来引用所有的图表实例
  const chartRefs = useRef<{ [key: string]: any }>({})

  // 跟踪全屏状态
  const [isAnyChartFullscreen, setIsAnyChartFullscreen] = useState(false)

  // 设置图表ref的辅助函数
  const setChartRef = useCallback((key: string, ref: any) => {
    chartRefs.current[key] = ref
  }, [])

  // 滚动到指定的Collapse面板
  const scrollToCollapse = useCallback((key: string) => {
    const element = collapseRefs.current[key]
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
      })
    }
  }, [])

  const { data: workersData } = useTrainTaskExecutionWorkers(Number(id))
  const { data: executionDetail } = useTrainTaskExecutionDetail(Number(id))

  // 监听表单值变化
  const formValues = Form.useWatch([], form)
  const selectedWorkers = formValues?.worker || []
  const dateRange = formValues?.timeRange || null

  // 获取任务执行详情中的团队ID和执行名称
  const teamId = executionDetail?.data?.teamId?.toString() || ''
  const execName = executionDetail?.data?.executionName || ''

  // 根据任务状态自动设置时间范围
  useEffect(() => {
    // 默认查询逻辑
    // 任务已结束：
    // 任务未超过3天：范围查 创建~结束，显示文本：数据解读：本任务从开始到结束，xxxx
    // 任务超过3天：范围查 最近30分钟，显示文本：数据解读：在最近 30 分钟里，xxxx
    // 任务未结束
    // 任务未超过3天：范围查 创建~现在，显示文本：数据解读：本任务从开始到当前，xxxx
    // 任务超过3天：范围查 最近30分钟，显示文本：数据解读：在最近 30 分钟里，xxxx
    if (!executionDetail?.data) return

    const taskData = executionDetail.data
    const isRunning =
      taskData.status === EnumTrainTaskExecutionState.PENDING ||
      taskData.status === EnumTrainTaskExecutionState.RUNNING

    let newDateRange: [Dayjs, Dayjs]

    if (isRunning) {
      // 任务未结束
      const startTime = taskData.startTime
        ? dayjs(taskData.startTime)
        : dayjs().subtract(30, 'minute')
      const currentTime = dayjs()
      const taskDuration = currentTime.diff(startTime, 'day')

      if (taskDuration <= 3) {
        // 任务未超过3天：范围查 创建~现在
        newDateRange = [startTime, currentTime]
      } else {
        // 任务超过3天：范围查 最近30分钟
        const endTime = currentTime
        const rangeStartTime = endTime.subtract(30, 'minute')
        newDateRange = [rangeStartTime, endTime]
      }
    } else {
      // 任务已结束
      const startTime = taskData.startTime
        ? dayjs(taskData.startTime)
        : dayjs().subtract(30, 'minute')
      const endTime = taskData.endTime ? dayjs(taskData.endTime) : dayjs()
      const taskDuration = endTime.diff(startTime, 'day')

      if (taskDuration <= 3) {
        // 任务未超过3天：范围查 创建~结束
        newDateRange = [startTime, endTime]
      } else {
        // 任务超过3天：范围查 最近30分钟（以结束时间为基准）
        const rangeStartTime = endTime.subtract(30, 'minute')
        newDateRange = [rangeStartTime, endTime]
      }
    }

    form.setFieldValue('timeRange', newDateRange)
    setIsInitialTimeRange(true)
  }, [executionDetail?.data, form])

  // 当workersData加载完成时，默认选择所有workers包括"全部"
  useEffect(() => {
    if (workersData?.workers && workersData.workers.length > 0) {
      const currentWorkers = form.getFieldValue('worker') || []
      // 只有当前没有选择任何worker时才设置默认值
      if (currentWorkers.length === 0) {
        form.setFieldValue('worker', ['', ...workersData.workers])
      }
    }
  }, [workersData?.workers, form])

  // 获取小数位数的辅助函数
  const getDecimalPlaces = useCallback((value: number): number => {
    if (Math.floor(value) === value) return 0
    const str = value.toString()
    if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
      return str.split('.')[1].length
    }
    if (str.indexOf('e-') !== -1) {
      const parts = str.split('e-')
      return Number.parseInt(parts[1], 10)
    }
    return 0
  }, [])

  // 创建数据获取函数
  const createGpuDataFetcher = useCallback(
    (
      queryBuilder: (
        teamId: string,
        execName: string,
        podName: string,
        cluster: string,
        namespace: string,
      ) => string,
      debugLabel: string,
    ) =>
      async (
        start: string,
        end: string,
        step: string,
      ): Promise<GpuChartData> => {
        if (!teamId || !execName) {
          throw new Error('缺少必要的参数：teamId 或 execName')
        }

        if (!clusterName || !namespace) {
          throw new Error('缺少必要的参数：clusterName 或 namespace')
        }

        try {
          // 确定要查询的workers列表，永远使用选中的workers
          const workers =
            selectedWorkers && selectedWorkers.length > 0
              ? selectedWorkers
              : workersData?.workers || []

          if (workers.length === 0) {
            throw new Error('没有可用的 worker')
          }

          // 统一的数据获取逻辑，永远按多选处理
          const allPromises = workers.map(async (worker: string) => {
            let pod = ''
            if (worker) pod = `"${worker}"`
            else pod = `~"${(workersData?.workers || []).join('|')}"`

            const query = queryBuilder(
              teamId,
              execName,
              pod,
              clusterName,
              namespace,
            )
            const response = await getPrometheusDetail(
              query,
              start,
              end,
              step,
              `${debugLabel}_multipleWorkers`,
            )
            return {
              worker,
              values: response.data?.result?.[0]?.values || [],
            }
          })

          const allResults = await Promise.all(allPromises)

          // 收集所有时间戳
          const allTimestamps = new Set<number>()
          for (const result of allResults) {
            for (const [timestamp] of result.values) {
              allTimestamps.add(timestamp as number)
            }
          }

          const sortedTimes = Array.from(allTimestamps).sort()

          // 为每个 worker 创建数据映射
          const workerDataMap = new Map<string, Map<number, number>>()
          for (const result of allResults) {
            const dataMap = new Map<number, number>()
            for (const [timestamp, value] of result.values) {
              dataMap.set(
                timestamp as number,
                Number.parseFloat(value as string) || 0,
              )
            }
            workerDataMap.set(result.worker, dataMap)
          }

          // 构建每个 worker 的完整数据序列
          const workersChartData = workers.map((worker: string) => {
            const dataMap = workerDataMap.get(worker) || new Map()
            return {
              name: worker,
              values: sortedTimes.map(
                (timestamp) => dataMap.get(timestamp) || 0,
              ),
            }
          })

          // 计算平均值作为默认显示
          const averageValues = sortedTimes.map((timestamp) => {
            const values = workers
              .map((worker: string) => {
                const dataMap = workerDataMap.get(worker) || new Map()
                return dataMap.get(timestamp) || 0
              })
              .filter((val: number) => val > 0) // 过滤掉0值

            return values.length > 0
              ? values.reduce((sum: number, val: number) => sum + val, 0) /
                  values.length
              : 0
          })

          return {
            times: sortedTimes.map((timestamp) =>
              new Date(timestamp * 1000).toISOString(),
            ),
            values: averageValues,
            workers: workersChartData,
          }
        } catch (error) {
          console.error('GPU数据获取失败:', error)
          throw error
        }
      },
    [
      teamId,
      execName,
      selectedWorkers,
      workersData?.workers,
      clusterName,
      namespace,
    ],
  )

  // 创建各种GPU数据获取函数
  const fetchGpuCoreUtilization = useMemo(
    () =>
      createGpuDataFetcher(
        getGpuCoreUtilizationQuery,
        'fetchGpuCoreUtilization',
      ),
    [createGpuDataFetcher],
  )

  const fetchGpuMemoryUtilization = useMemo(
    () =>
      createGpuDataFetcher(
        getGpuMemoryUtilizationQuery,
        'fetchGpuMemoryUtilization',
      ),
    [createGpuDataFetcher],
  )

  const fetchGpuCoreUsage = useMemo(
    () => createGpuDataFetcher(getGpuMemoryCoreUsageQuery, 'fetchGpuCoreUsage'),
    [createGpuDataFetcher],
  )

  const fetchGpuMemoryUsage = useMemo(
    () => createGpuDataFetcher(getGpuMemoryUsageQuery, 'fetchGpuMemoryUsage'),
    [createGpuDataFetcher],
  )

  // 创建CPU和内存数据获取函数
  const fetchCpuUtilization = useMemo(
    () => createGpuDataFetcher(getCpuUtilizationQuery, 'fetchCpuUtilization'),
    [createGpuDataFetcher],
  )

  const fetchCpuUsage = useMemo(
    () => createGpuDataFetcher(getCpuUsageQuery, 'fetchCpuUsage'),
    [createGpuDataFetcher],
  )

  const fetchMemoryUtilization = useMemo(
    () =>
      createGpuDataFetcher(getMemoryUtilizationQuery, 'fetchMemoryUtilization'),
    [createGpuDataFetcher],
  )

  const fetchMemoryUsage = useMemo(
    () => createGpuDataFetcher(getMemoryUsageQuery, 'fetchMemoryUsage'),
    [createGpuDataFetcher],
  )

  // 定义图表组ID
  const CHART_GROUP_ID = 'monitor-charts-group'

  // 图表联动效果：使用group方式连接所有图表实例
  useEffect(() => {
    // 如果有图表处于全屏状态，不进行连接
    if (isAnyChartFullscreen) {
      return
    }

    // 延迟执行，确保所有图表都已渲染完成
    const timer = setTimeout(() => {
      let connectedCount = 0

      // 为所有图表实例设置group
      for (const key in chartRefs.current) {
        const chartRef = chartRefs.current[key]
        if (chartRef?.setGroup && chartRef?.getEchartsInstance) {
          const instance = chartRef.getEchartsInstance()
          if (instance) {
            // 使用暴露的setGroup方法设置group
            chartRef.setGroup(CHART_GROUP_ID)
            connectedCount++
          }
        }
      }

      // 如果有多个图表实例，使用group连接它们
      if (connectedCount > 1) {
        try {
          echarts.connect(CHART_GROUP_ID)
          console.log(
            `已连接 ${connectedCount} 个图表实例到组 ${CHART_GROUP_ID}，实现联动效果`,
          )
        } catch (error) {
          console.warn('图表连接失败:', error)
        }
      }
    }, 500) // 延迟500ms执行，确保图表都已渲染

    return () => clearTimeout(timer)
  }, [isAnyChartFullscreen])

  // 处理全屏状态变化
  const handleFullscreenChange = useCallback((isFullscreen: boolean) => {
    setIsAnyChartFullscreen(isFullscreen)

    if (isFullscreen) {
      // 进入全屏时断开组连接
      try {
        echarts.disconnect(CHART_GROUP_ID)
        console.log(`已断开图表组 ${CHART_GROUP_ID} 的连接`)
      } catch (error) {
        console.warn('断开图表连接失败:', error)
      }
    }
    // 退出全屏时，连接会在 useEffect 中自动重新建立
  }, [])

  // 创建图表格式化函数
  const createGpuChartOption = useCallback(
    (unit = '%') =>
      (data: GpuChartData): EChartsOption => {
        // 颜色配置
        const colors = [
          '#7367EF',
          '#FF6B6B',
          '#4ECDC4',
          '#45B7D1',
          '#96CEB4',
          '#FFEAA7',
          '#DDA0DD',
          '#98D8C8',
          '#F7DC6F',
          '#BB8FCE',
        ]

        // 统一的tooltip格式化函数
        const tooltipFormatter = (params: any) => {
          if (Array.isArray(params)) {
            // 提取所有数值用于统一精度计算
            const values = params.map((param: any) =>
              Number.parseFloat(param.value),
            )
            const maxDecimalPlaces = Math.min(
              Math.max(...values.map(getDecimalPlaces)),
              4,
            )

            const timeIndex = data.times
              .map((time) => dayjs(time).format('MM-DD HH:mm'))
              .indexOf(params[0].axisValue)
            const originalTime =
              timeIndex >= 0 ? data.times[timeIndex] : params[0].axisValue
            const formattedTime = dayjs(originalTime).format(
              'YYYY-MM-DD HH:mm:ss',
            )
            let result = `<div style="margin-bottom: 6px; font-weight: bold;">${formattedTime}</div>`
            result += '<table style="width: 100%; border-collapse: collapse;">'
            for (const param of params) {
              const formattedValue = Number.parseFloat(param.value).toFixed(
                maxDecimalPlaces,
              )
              result += `
              <tr>
                <td style="text-align: left; padding: 2px 0; max-width: 150px; word-wrap: break-word; white-space: normal;">
                  ${param.marker}${param.seriesName}
                </td>
                <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                  ${formattedValue}${unit}
                </td>
              </tr>
            `
            }
            result += '</table>'
            return result
          }

          const value = Number.parseFloat(params.value)
          const decimalPlaces = Math.min(getDecimalPlaces(value), 4)
          const formattedValue = value.toFixed(decimalPlaces)

          const timeIndex = data.times
            .map((time) => dayjs(time).format('MM-DD HH:mm'))
            .indexOf(params.axisValue)
          const originalTime =
            timeIndex >= 0 ? data.times[timeIndex] : params.axisValue
          const formattedTime = dayjs(originalTime).format(
            'YYYY-MM-DD HH:mm:ss',
          )
          return `
          <div style="margin-bottom: 6px; font-weight: bold;">${formattedTime}</div>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="text-align: left; padding: 2px 0; max-width: 150px; word-wrap: break-word; white-space: normal;">
                ${params.marker}${params.seriesName}
              </td>
              <td style="text-align: right; padding: 2px 0; padding-left: 20px; font-weight: bold;">
                ${formattedValue}${unit}
              </td>
            </tr>
          </table>
        `
        }

        // 永远按多选模式处理，显示所有选中的worker
        const activeWorkers =
          data.workers && selectedWorkers && selectedWorkers.length > 0
            ? data.workers.filter((worker) =>
                selectedWorkers.includes(worker.name),
              )
            : data.workers || []

        const legendData = activeWorkers.map(
          (worker) => `${worker.name}` || '全部',
        )

        const series = activeWorkers.map((worker, index) => ({
          name: `${worker.name}` || '全部',
          type: 'line' as const,
          data: worker.values,
          color: colors[index % colors.length],
          symbol: 'none',
        }))

        return {
          tooltip: {
            appendToBody: false,
            // confine: true,
            trigger: 'axis' as const,
            formatter: tooltipFormatter,
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#7367EF',
              },
            },
          },
          legend: {
            type: 'scroll',
            orient: 'horizontal',
            top: 'top',
            left: 'left',
            data: legendData,
          },
          grid: {
            left: '10%',
            right: '5%',
            top: '15%',
            bottom: '10%',
          },
          xAxis: {
            type: 'category' as const,
            data: data.times.map((time) => dayjs(time).format('MM-DD HH:mm')),
          },
          yAxis: {
            type: 'value' as const,
            axisLabel: {
              formatter: (value: number) => value + ` ${unit}`,
            },
          },
          series,
        }
      },
    [selectedWorkers, getDecimalPlaces],
  )

  // 生成数据解读文本
  const generateDataInterpretationText = useCallback(() => {
    if (!isInitialTimeRange || !executionDetail?.data) {
      // 用户自己选择过时间，显示具体时间范围
      if (dateRange) {
        const startTime = dateRange[0].format('YYYY-MM-DD HH:mm')
        const endTime = dateRange[1].format('YYYY-MM-DD HH:mm')
        return `从 ${startTime} 到 ${endTime}`
      }
      return '在选定时间范围内'
    }

    const taskData = executionDetail.data
    const isRunning =
      taskData.status === EnumTrainTaskExecutionState.PENDING ||
      taskData.status === EnumTrainTaskExecutionState.RUNNING

    if (isRunning) {
      // 任务未结束
      const startTime = taskData.startTime
        ? dayjs(taskData.startTime)
        : dayjs().subtract(30, 'minute')
      const currentTime = dayjs()
      const taskDuration = currentTime.diff(startTime, 'day')

      if (taskDuration <= 3) {
        return '本任务从开始到当前'
      }

      return '在最近 30 分钟里'
    }

    // 任务已结束
    const startTime = taskData.startTime
      ? dayjs(taskData.startTime)
      : dayjs().subtract(30, 'minute')
    const endTime = taskData.endTime ? dayjs(taskData.endTime) : dayjs()
    const taskDuration = endTime.diff(startTime, 'day')

    if (taskDuration <= 3) {
      return '本任务从开始到结束'
    }
    return '在最近 30 分钟里'
  }, [isInitialTimeRange, executionDetail?.data, dateRange])

  // 创建提示信息渲染函数
  const createAlertRender = useCallback(
    (metricName: string, unit = '%') =>
      (data: GpuChartData, timeRangeText: string) => {
        if (!data.values || data.values.length === 0) {
          const interpretationText = generateDataInterpretationText()
          return (
            <div>
              数据解读：{interpretationText}，暂无{metricName}数据
            </div>
          )
        }

        // 兜底显示
        const avgValue =
          data.values.reduce((sum, val) => sum + val, 0) / data.values.length
        const formattedValue =
          unit === 'GiB' ? avgValue.toFixed(2) : avgValue.toFixed(1)

        const interpretationText = generateDataInterpretationText()

        return (
          <div>
            数据解读：{interpretationText}，
            <strong>{metricName}（均值）</strong> 为 {formattedValue}
            {unit}
          </div>
        )
      },
    [generateDataInterpretationText],
  )

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: (
        <div
          ref={(el) => {
            collapseRefs.current['1'] = el
          }}
          className="collapse-header"
        >
          CPU 和内存
        </div>
      ),
      children: (
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('cpu-utilization', ref)}
            showTimeSelector={false}
            title={
              <div>
                CPU 利用率<span className="text-[#C8C8C8]"> %</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchCpuUtilization}
            formatOption={createGpuChartOption('%')}
            alertRender={createAlertRender('CPU利用率', '%')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[
              selectedWorkers,
              workersData,
              dateRange,
              'cpu-utilization',
            ]}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />
          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('memory-utilization', ref)}
            showTimeSelector={false}
            title={
              <div>
                内存利用率<span className="text-[#C8C8C8]"> %</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchMemoryUtilization}
            formatOption={createGpuChartOption('%')}
            alertRender={createAlertRender('内存利用率', '%')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[
              selectedWorkers,
              workersData,
              dateRange,
              'memory-utilization',
            ]}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />
          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('cpu-usage', ref)}
            showTimeSelector={false}
            title={
              <div>
                CPU 核数<span className="text-[#C8C8C8]"> Core</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchCpuUsage}
            formatOption={createGpuChartOption('Core')}
            alertRender={createAlertRender('CPU核数', 'Core')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[selectedWorkers, workersData, dateRange, 'cpu-usage']}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />

          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('memory-usage', ref)}
            showTimeSelector={false}
            title={
              <div>
                内存使用量<span className="text-[#C8C8C8]"> GiB</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchMemoryUsage}
            formatOption={createGpuChartOption('GiB')}
            alertRender={createAlertRender('内存使用量', 'GiB')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[
              selectedWorkers,
              workersData,
              dateRange,
              'memory-usage',
            ]}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div
          ref={(el) => {
            collapseRefs.current['2'] = el
          }}
          className="collapse-header"
        >
          GPU
        </div>
      ),
      children: (
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('gpu-core-utilization', ref)}
            showTimeSelector={false}
            title={
              <div>
                GPU 核心利用率<span className="text-[#C8C8C8]"> %</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchGpuCoreUtilization}
            formatOption={createGpuChartOption('%')}
            alertRender={createAlertRender('GPU核心利用率', '%')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[
              selectedWorkers,
              workersData,
              dateRange,
              'gpu-core-utilization',
            ]}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />
          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('gpu-memory-utilization', ref)}
            showTimeSelector={false}
            title={
              <div>
                GPU 显存利用率<span className="text-[#C8C8C8]"> %</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchGpuMemoryUtilization}
            formatOption={createGpuChartOption('%')}
            alertRender={createAlertRender('GPU显存利用率', '%')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[
              selectedWorkers,
              workersData,
              dateRange,
              'gpu-memory-utilization',
            ]}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />

          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('gpu-core-usage', ref)}
            showTimeSelector={false}
            title={
              <div>
                GPU 核心利用量<span className="text-[#C8C8C8]"> Core</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchGpuCoreUsage}
            formatOption={createGpuChartOption('Core')}
            alertRender={createAlertRender('GPU核心利用量', 'Core')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[
              selectedWorkers,
              workersData,
              dateRange,
              'gpu-core-usage',
            ]}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />

          <PrometheusChart<GpuChartData>
            ref={(ref) => setChartRef('gpu-memory-usage', ref)}
            showTimeSelector={false}
            title={
              <div>
                GPU 显存使用量<span className="text-[#C8C8C8]"> GiB</span>
              </div>
            }
            className="flex-1 bg-white rounded-[9px] shadow-[0px_3px_6px_0px_rgba(0,0,0,0.15)]"
            height={'40vh'}
            fetchPrometheusData={fetchGpuMemoryUsage}
            formatOption={createGpuChartOption('GiB')}
            alertRender={createAlertRender('GPU显存使用量', 'GiB')}
            enabled={
              !!(teamId && execName && dateRange && clusterName && namespace)
            }
            loadingDeps={[
              selectedWorkers,
              workersData,
              dateRange,
              'gpu-memory-usage',
            ]}
            dateRange={dateRange}
            onFullscreenChange={handleFullscreenChange}
          />
        </div>
      ),
    },
  ]

  const radioOptions = items.map((item) => ({
    value: item.key,
    label: item.label,
  }))

  const workerOptions = workersData?.workers
    ? [
        { label: '全部', value: '' },
        ...workersData.workers.map((worker) => ({
          label: worker,
          value: worker,
        })),
      ]
    : []

  return (
    <div>
      <Form
        form={form}
        layout="inline"
        className="mb-4"
        initialValues={{
          worker: [], // 空数组表示"全部"
          timeRange: null, // 将由useEffect根据任务状态自动设置
        }}
      >
        <Form.Item>
          <Radio.Group
            className="w-56"
            block
            options={radioOptions}
            value={activeTab}
            onChange={(e) => {
              const newValue = e.target.value
              setActiveTab(newValue)

              // 确保对应的Collapse面板是展开的
              if (!collapseActiveKey.includes(newValue)) {
                setCollapseActiveKey([...collapseActiveKey, newValue])
              }

              // 延迟滚动，确保Collapse面板已经展开
              setTimeout(() => {
                scrollToCollapse(newValue)
              }, 500)
            }}
            optionType="button"
            buttonStyle="solid"
          />
        </Form.Item>

        <Form.Item name="worker" label="workers">
          <Select
            mode="multiple"
            maxTagCount="responsive"
            placeholder="选择 Worker"
            style={{ width: '30vw' }}
            options={workerOptions}
            onChange={(value) => form.setFieldValue('worker', value || [])}
          />
        </Form.Item>

        <Form.Item
          name="timeRange"
          label={
            <InfoTooltip
              maxWidth={600}
              title={
                <div>
                  运行中的任务默认以当前时刻为参考展示近 x 小时的监控数据
                  <br />
                  当任务停止运行后将以停止时刻至开始时刻为参考展示相应的监控数据
                </div>
              }
            >
              时间范围
            </InfoTooltip>
          }
        >
          <DatePicker.RangePicker
            onChange={(dates) => {
              setIsInitialTimeRange(false) // 用户选择预设时间，标记为非初始状态
              // 完整性检查
              if (dates?.[0] && dates?.[1]) {
                // 选择完整，进行处理
                let startTime = dates[0]
                let endTime = dates[1]

                // 确保开始时间在结束时间之前
                if (startTime.isAfter(endTime)) {
                  ;[startTime, endTime] = [endTime, startTime]
                }

                // 14天范围校正（精确到分钟）
                const diffMinutes = Math.abs(endTime.diff(startTime, 'minute'))
                const maxMinutes = 14 * 24 * 60 // 14天 = 20160分钟
                if (diffMinutes > maxMinutes) {
                  // 超过14天，执行自动校正
                  console.log(
                    `时间范围超过14天（${diffMinutes}分钟 > ${maxMinutes}分钟），自动校正为14天范围`,
                  )
                  // 保持开始时间不变，调整结束时间为开始时间后的第14天
                  endTime = startTime.add(14, 'day')
                }

                // 正常选择处理
                const correctedRange: [Dayjs, Dayjs] = [startTime, endTime]
                form.setFieldValue('timeRange', correctedRange)
                setIsInitialTimeRange(false) // 用户手动选择时间，标记为非初始状态
              } else {
                // 选择不完整（只选了一个日期或点击了 clear）
                form.setFieldValue('timeRange', null)
                setIsInitialTimeRange(false) // 用户手动选择时间，标记为非初始状态
              }
            }}
            format="YYYY-MM-DD HH:mm:ss"
            disabledDate={(current) => {
              // 只禁用未来的日期，今天是可以选择的
              if (current && current > dayjs().endOf('day')) {
                return true
              }
              return false
            }}
            presets={createTimePresets(executionDetail).map((preset) => ({
              label: preset.label,
              value: preset.value() as [Dayjs, Dayjs],
            }))}
            placeholder={['开始时间', '结束时间']}
            allowClear={false}
            style={{
              minWidth: 280,
            }}
          />
        </Form.Item>
      </Form>

      <ScrollArea style={{ height: 'calc(100vh - 200px)' }}>
        <Collapse
          activeKey={collapseActiveKey}
          onChange={(keys) =>
            setCollapseActiveKey(Array.isArray(keys) ? keys : [keys])
          }
          ghost
          items={items}
          className="[&_.ant-collapse-header]:!p-0 [&_.ant-collapse-item]:!mb-[16px]"
        />
      </ScrollArea>
    </div>
  )
}
