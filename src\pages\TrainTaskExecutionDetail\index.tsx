import type { TrainTaskDetail } from '@/api/traintask'
import {
  getTrainTaskDetail,
  useTrainTaskExecutionDetail,
} from '@/api/traintask'
import TaskForm, { type TaskFormRef } from '@/components/forms/TaskForm'
import { ScrollArea } from '@/components/ui/scroll-area'
import { convertTrainTaskDetailToFormData } from '@/utils/dataConvert'
import { Tabs } from 'antd'
import { useEffect, useRef, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import ExecutionTimeline from './ExecutionTimeline'
import MonitorTab from './MonitorTab'
import TaskHeader from './task-header'

export default function TrainTaskExecutionDetail() {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const [searchParams, setSearchParams] = useSearchParams()
  const taskFormRef = useRef<TaskFormRef>(null)
  const [isTaskDetailLoading, setIsTaskDetailLoading] = useState(false)

  // Get active tab from URL query params, default to '1'
  const activeTab = searchParams.get('tab') || '1'

  // Fetch train task execution details
  const {
    data: executionDetail,
    isLoading,
    error,
    refetch: refetchExecutionDetail,
  } = useTrainTaskExecutionDetail(Number(id) || 0)
  const clusterName = executionDetail?.data.clusterName
  const namespace = executionDetail?.data.namespace

  const taskId = executionDetail?.data?.taskId

  // Fetch and set task detail data when taskId is available
  useEffect(() => {
    const fetchTaskDetail = async () => {
      if (taskId && taskFormRef.current && activeTab === '1') {
        setIsTaskDetailLoading(true)
        try {
          const { data } = await getTrainTaskDetail(taskId)
          const formData = convertTrainTaskDetailToFormData(data)
          console.log('Task detail form data:', formData)

          // Set form values after a small delay to ensure form is ready
          setTimeout(() => {
            taskFormRef.current?.setValues(formData)
          }, 0)
        } catch (error) {
          console.error('Failed to fetch task detail:', error)
        } finally {
          setIsTaskDetailLoading(false)
        }
      }
    }

    setTimeout(() => {
      // 防止没有taskFormRef.current
      fetchTaskDetail()
    }, 10)
  }, [taskId, activeTab])

  const handleFormFinish = (values: TrainTaskDetail) => {
    console.log('表单提交数据:', values)
    // 这里可以添加提交逻辑
  }

  const handleFormFinishFailed = (errorInfo: any) => {
    console.error('表单验证失败:', errorInfo)
  }

  const handleTabChange = (key: string) => {
    const newParams = new URLSearchParams(searchParams)
    newParams.set('tab', key)
    setSearchParams(newParams)
  }

  const handleRefresh = () => {
    refetchExecutionDetail()
  }

  return (
    <div className="h-full bg-white">
      <TaskHeader
        executionDetail={executionDetail?.data}
        isLoading={isLoading}
        error={error}
        onRefresh={handleRefresh}
      />
      <div className="px-3 py-3">
        <Tabs
          type="card"
          activeKey={activeTab}
          onChange={handleTabChange}
          items={[
            {
              label: '模板概览',
              key: '1',
              children: (
                <ScrollArea style={{ height: 'calc(100vh - 152px)' }}>
                  <div className="flex">
                    <TaskForm
                      className="flex-1"
                      ref={taskFormRef}
                      type="read"
                      disabled={true}
                      onFinish={handleFormFinish}
                      onFinishFailed={handleFormFinishFailed}
                    />
                    <div className="w-[1px] bg-[#E2E4E4] ml-8 mr-4" />
                    <ExecutionTimeline executionId={Number(id) || 0} />
                  </div>
                </ScrollArea>
              ),
            },
            {
              label: '监控',
              key: '2',
              children: (
                <MonitorTab clusterName={clusterName} namespace={namespace} />
              ),
            },
            {
              label: '日志',
              key: '3',
              children: 'Tab 3',
              disabled: true,
            },
          ]}
        />
      </div>
    </div>
  )
}
