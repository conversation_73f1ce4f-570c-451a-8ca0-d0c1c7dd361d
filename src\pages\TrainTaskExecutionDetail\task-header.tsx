import { interruptTrainTaskExecution } from '@/api/traintask'
import { TASK_PRIORITY_OPTIONS } from '@/constants/taskPriority'
import {
  type TrainTaskExecution,
  TrainTaskExecutionStateMap,
} from '@/pages/TrainTaskExecutionManagement/setting'
import { ClockCircleOutlined } from '@ant-design/icons'
import { Button, Modal, message } from 'antd'
import { ArrowLeft, Clock, User } from 'lucide-react'
import { useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'

interface TaskHeaderProps {
  executionDetail?: TrainTaskExecution
  isLoading?: boolean
  error?: any
  onRefresh?: () => void // 添加刷新回调
}

export default function TaskHeader({
  executionDetail,
  isLoading,
  error,
  onRefresh,
}: TaskHeaderProps) {
  const navigate = useNavigate()
  const [modal, contextHolder] = Modal.useModal()
  const [interruptLoading, setInterruptLoading] = useState(false)

  // Calculate running duration
  const runningDuration = useMemo(() => {
    if (!executionDetail?.startTime) return ''

    const startTime = new Date(executionDetail.startTime)
    const endTime = executionDetail.endTime
      ? new Date(executionDetail.endTime)
      : new Date()
    const diffMs = endTime.getTime() - startTime.getTime()

    const hours = Math.floor(diffMs / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000)

    return `${hours} 时 ${minutes} 分 ${seconds} 秒`
  }, [executionDetail?.startTime, executionDetail?.endTime])

  // Get priority info using consistent priority system
  const getPriorityInfo = (priority?: string) => {
    const priorityOption =
      TASK_PRIORITY_OPTIONS.find((option) => option.value === priority) ||
      TASK_PRIORITY_OPTIONS[2] // Default to P2 (常规)

    return priorityOption
  }

  const priorityInfo = getPriorityInfo(executionDetail?.priority)

  const handleGoBack = () => {
    navigate('/train/task-management')
  }

  // 处理中断任务
  const handleInterrupt = async () => {
    if (!executionDetail) return

    await modal.confirm({
      title: '提示',
      centered: true,
      content: `您确定要中断任务 "${executionDetail.executionName || executionDetail.taskName}" 吗？`,
      onOk: async () => {
        setInterruptLoading(true)
        try {
          await interruptTrainTaskExecution(executionDetail.id)
          message.success('任务中断成功！')
          onRefresh?.() // 刷新页面数据
        } catch (error) {
          console.error('中断任务失败:', error)
          message.error('中断任务失败，请重试')
        } finally {
          setInterruptLoading(false)
        }
      },
    })
  }

  // Format date helper
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return '未知时间'
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }

  if (isLoading) {
    return (
      <div className="bg-white px-3 py-2">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/3" />
          <div className="h-4 bg-gray-200 rounded w-2/3" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white px-3 py-3">
        <div className="text-red-600">加载任务详情失败</div>
      </div>
    )
  }
  const statusObj = executionDetail?.status
    ? TrainTaskExecutionStateMap[executionDetail.status]
    : null
  return (
    <div className="bg-white px-3 pt-3">
      {/* Top navigation row */}
      <div className="flex items-center gap-4 mb-4">
        <button
          type="button"
          onClick={handleGoBack}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span className="text-sm">返回</span>
        </button>
        <div className="w-px h-3 bg-gray-300" />

        <div className="flex items-center gap-2 text-sm text-gray-600">
          <span>任务运行详情</span>
          <span>{'>'}</span>
          <span className="font-bold text-[#333]">
            {executionDetail?.executionName || '未知任务'}
          </span>
        </div>

        {statusObj && (
          <div
            className={
              'flex items-center gap-2 text-sm text-gray-500 rounded-sm px-1'
            }
            style={{
              backgroundColor: statusObj.bg,
            }}
          >
            {statusObj.icon}
            {statusObj.label}
          </div>
        )}

        {runningDuration && (
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>已运行 {runningDuration}</span>
          </div>
        )}

        {/* 中断按钮 */}
        <Button
          danger
          size="small"
          loading={interruptLoading}
          disabled={
            !executionDetail?.status ||
            !TrainTaskExecutionStateMap[executionDetail.status]?.canInterrupt
          }
          onClick={handleInterrupt}
        >
          中断
        </Button>
      </div>

      {/* Task details row */}
      <div className="flex items-center gap-3 text-[12px] text-[#333]">
        <div className="flex items-center gap-2">
          <span className="text-[#999]">ID</span>
          <span className="font-medium">{executionDetail?.id || '-'}</span>
        </div>

        <div className="w-px h-3 bg-gray-300" />

        <div className="flex items-center gap-2">
          <span className="text-[#999]">任务模板</span>
          <span className="font-medium">
            {executionDetail?.taskName || '未知模板'}
          </span>
        </div>

        <div className="w-px h-3 bg-gray-300" />

        <div className="flex items-center gap-2">
          <span className="text-[#999]">优先级</span>
          <div className="flex items-center flex-1 justify-center gap-1">
            <div
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: priorityInfo.color }}
            />
            <span className="font-medium">{priorityInfo.label}</span>
          </div>
        </div>

        <div className="w-px h-3 bg-gray-300" />

        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <span>{executionDetail?.triggeredByUserName || '未知用户'}</span>
        </div>

        <div className="w-px h-3 bg-gray-300" />

        <div className="flex items-center gap-2">
          <ClockCircleOutlined className="w-4 h-4 text-gray-400" />
          <span>{formatDate(executionDetail?.triggerTime)} 创建</span>
        </div>

        {executionDetail?.endTime && (
          <>
            <div className="w-px h-3 bg-gray-300" />
            <div className="flex items-center gap-2">
              <ClockCircleOutlined className="w-4 h-4 text-gray-400" />
              <span>{formatDate(executionDetail.endTime)} 结束</span>
            </div>
          </>
        )}
      </div>
      {contextHolder}
    </div>
  )
}
