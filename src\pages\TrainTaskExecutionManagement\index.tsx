import GrafanaIcon from '@/assets/grafana.svg?react'
import qianxing from '@/assets/qianxing.png'
import RayIcon from '@/assets/ray.svg?react'

import InfoTooltip from '@/components/common/InfoTooltip'
import {
  ReloadOutlined,
  SearchOutlined,
  SettingOutlined,
  StopOutlined,
} from '@ant-design/icons'
import { ProTable } from '@ant-design/pro-components'
import type { ProColumns } from '@ant-design/pro-components'
import {
  Button,
  Checkbox,
  Divider,
  Input,
  Popover,
  Radio,
  Skeleton,
  Space,
  Tag,
  Tooltip,
  message,
} from 'antd'

import TaskAdjustDialog from '@/components/TaskAdjustDialog'

import { getMonitorUrl, updateTaskExecutionPriority } from '@/api/traintask'
import CachedRadioGroup from '@/components/CachedRadioGroup'
import PriorityTooltip from '@/components/PriorityTooltip'
import PriorityDisplay from '@/components/common/PriorityDisplay'
import { TASK_PRIORITY_OPTIONS } from '@/constants/taskPriority'
import { TRAINING_FRAMEWORK_MAP } from '@/constants/trainingFramework'
import { renderCustomFilterDropdown } from '@/utils/useProTableService'
import dayjs from 'dayjs'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  CreateByEnumToLabel,
  EnumCreateBy,
  EnumTrainTaskExecutionState,
  type TrainTaskExecution,
  TrainTaskExecutionStateList,
  TrainTaskExecutionStateMap,
  formatDuration,
  formatDurationSimple,
} from './setting'
import useTrainTaskExecutionManagementService from './useTrainTaskExecutionManagementService'

export default () => {
  const navigate = useNavigate()
  const {
    getTableRequest,
    taskExecutionState,
    setTaskExecutionState,
    actionRef,
    taskName,
    setTaskName,
    onRerun,
    onInterrupt,
    onGrafanaMonitor,
    onRayMonitor,
    contextHolder,
    createBy,
    setCreateBy,
    userInfo,
    idFromUrl,
    monitorParams,
    isDashboardEnabled,
    refreshKey,
    isHardLoading,
    currentTeam,
  } = useTrainTaskExecutionManagementService()

  // 任务调整对话框状态
  const [adjustDialogOpen, setAdjustDialogOpen] = useState(false)
  const [currentAdjustTask, setCurrentAdjustTask] =
    useState<TrainTaskExecution | null>(null)
  const [adjustLoading, setAdjustLoading] = useState(false)

  // 处理任务调整
  const handleTaskAdjust = (record: TrainTaskExecution) => {
    setCurrentAdjustTask(record)
    setAdjustDialogOpen(true)
  }

  // 确认任务调整
  const handleAdjustConfirm = async (priority: string) => {
    if (!currentAdjustTask) return

    setAdjustLoading(true)
    try {
      updateTaskExecutionPriority(currentAdjustTask.id, priority)
      // 刷新表格
      actionRef.current?.reload()

      // 关闭对话框
      setAdjustDialogOpen(false)
      setCurrentAdjustTask(null)
      message.success('调整成功')
    } catch (error) {
      console.error('更新任务优先级失败:', error)
    }
  }

  // 取消任务调整
  const handleAdjustCancel = () => {
    setAdjustDialogOpen(false)
    setCurrentAdjustTask(null)
  }

  const columns: ProColumns<TrainTaskExecution>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      copyable: true,
      ellipsis: true,
      search: false,
      width: 80,
      fixed: 'left',
      filters: true,
      defaultFilteredValue: idFromUrl ? [idFromUrl] : undefined,
      filterDropdown: (args) =>
        renderCustomFilterDropdown({ ...args, type: 'Input' }),
      render: (_, record) => (
        <Button
          type="link"
          className="p-0 h-auto"
          onClick={() =>
            window.open(
              `/train/task-execution/${record.id}?team=${currentTeam}`,
              '_blank',
            )
          }
        >
          {record.id}
        </Button>
      ),
    },
    {
      title: '名称',
      dataIndex: 'executionName',
      copyable: true,
      ellipsis: true,
      search: false,
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <Button
          type="link"
          className="p-0 h-auto"
          onClick={() =>
            window.open(
              `/train/task-execution/${record.id}?team=${currentTeam}`,
              '_blank',
            )
          }
        >
          {record.executionName}
        </Button>
      ),
      //   filters: true,
      //   filterDropdown: (args) =>
      //     renderCustomFilterDropdown({ ...args, type: 'Input' }),
    },
    {
      title: '关联任务名称',
      dataIndex: 'taskName',
      copyable: true,
      ellipsis: true,
      search: false,
      width: 200,
      fixed: 'left',
      //   render: (_, record) => (
      //     <>
      //       {record.taskName}
      //       <span className="text-[#999999]">（ID：{record.taskId}）</span>
      //     </>
      //   ),
      //   filters: true,
      //   filterDropdown: (args) =>
      //     renderCustomFilterDropdown({ ...args, type: 'Input' }),
    },
    {
      title: (
        <div>
          <InfoTooltip
            title="影响资源调度逻辑：优先级越高越先安排资源，在资源不足时，高级别任务会抢占低级别任务的资源。"
            iconPosition="right"
            iconClassName="ml-2"
          >
            优先级
          </InfoTooltip>
        </div>
      ),
      width: 100,
      dataIndex: 'priority',
      key: 'priority',
      align: 'center',
      render: (_, record) => {
        return <PriorityDisplay priority={record.priority} placement="right" />
      },
    },
    {
      title: (
        <div>
          <InfoTooltip
            title="任务创建后不能修改"
            iconPosition="right"
            iconClassName="ml-2"
          >
            任务类型
          </InfoTooltip>
        </div>
      ),
      width: 120,
      dataIndex: 'trainingFramework',
      key: 'trainingFramework',
      align: 'center',
      render: (_, record) => {
        return (
          TRAINING_FRAMEWORK_MAP[
            record.trainingFramework as keyof typeof TRAINING_FRAMEWORK_MAP
          ] || record.trainingFramework
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      ellipsis: true,
      search: false,
      width: 120,
      align: 'center',
      render: (_, record) => {
        const obj = TrainTaskExecutionStateMap[record.status]
        return (
          <Space className="flex  justify-center">
            {obj?.icon}
            {obj?.label}
          </Space>
        )
      },
    },
    {
      title: '运行时长',
      dataIndex: 'duration',
      search: false,
      width: 180,
      align: 'center',
      key: `duration-${refreshKey}`, // 使用动态 key 强制刷新
      render: (_, record) => (
        <>
          {record.status === 'PENDING' ? (
            <>
              <span className="text-[#999999]">
                （预计排队
                {formatDurationSimple(record.estimatedWaitTimeSeconds * 1000)}）
              </span>

              <SettingOutlined
                className="cursor-pointer text-gray-500 hover:text-blue-500 transition-colors"
                onClick={() => handleTaskAdjust(record)}
              />
            </>
          ) : record.startTime ? (
            formatDuration(record.startTime, record.endTime ?? undefined)
          ) : (
            <div className="text-[#999999]">（未曾运行）</div>
          )}
        </>
      ),
    },
    {
      title: (
        <InfoTooltip
          title="任务创建后不能修改"
          iconPosition="right"
          iconClassName="ml-2"
        >
          创建方式
        </InfoTooltip>
      ),
      width: 120,
      dataIndex: 'taskType',
      key: 'taskType',
      align: 'center',
      render: (_, record) => {
        return record.taskType === 'FORM' ? (
          <Tag color="#dbd6fa" className="!text-[#666666] w-16 text-center">
            表单
          </Tag>
        ) : (
          <Tag color="#d7f1d4" className="!text-[#666666] w-16 text-center">
            YAML
          </Tag>
        )
      },
    },
    {
      title: '触发人',
      dataIndex: 'triggeredByUserName',
      ellipsis: true,
      search: false,
      align: 'center',
      width: 90,
    },
    {
      title: '触发方式',
      dataIndex: 'triggerSource',
      ellipsis: true,
      search: false,
      width: 110,
      align: 'center',
      filters: true,
      valueEnum: {
        MANUAL: {
          text: '手动触发',
        },
        SCHEDULED: {
          text: '定时触发',
        },
        API_CALL: {
          text: 'API 触发',
        },
      },
    },
    {
      title: '触发时间',
      dataIndex: 'triggerTime',
      //   renderText(text, record, index, action) {
      //     return record.startTime
      //   },
      filterDropdown: (args) =>
        renderCustomFilterDropdown({ ...args, type: 'RangePicker' }),
      width: 180,
      align: 'center',
      search: false,
    },
    {
      title: '开始运行时间',
      dataIndex: 'startTime',
      filterDropdown: (args) =>
        renderCustomFilterDropdown({ ...args, type: 'RangePicker' }),
      width: 180,

      search: false,
      align: 'center',
      render: (_, record) =>
        record?.startTime ? (
          record?.startTime
        ) : (
          <span className="text-[#999999]">（尚未开始）</span>
        ),
    },
    {
      title: '结束运行时间',
      dataIndex: 'endTime',
      filterDropdown: (args) =>
        renderCustomFilterDropdown({ ...args, type: 'RangePicker' }),
      width: 180,
      search: false,
      align: 'center',
      render: (_, record) =>
        record?.endTime ? (
          record?.endTime
        ) : (
          <span className="text-[#999999]">
            （尚未{`${record.startTime ? '结束' : '开始'}`}）
          </span>
        ),
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      align: 'center',
      fixed: 'right',
      width: 300,
      render: (text, record, _, action) => (
        <div className="flex items-center justify-around">
          {TrainTaskExecutionStateMap[record.status]?.canRerun && (
            <Button
              key="rerun"
              type="link"
              className="p-0"
              onClick={() => onRerun(record)}
            >
              <ReloadOutlined />
              重跑
            </Button>
          )}
          {TrainTaskExecutionStateMap[record.status]?.canInterrupt && (
            <Button
              key="interrupt"
              type="link"
              danger
              className="p-0"
              onClick={() => onInterrupt(record)}
            >
              <StopOutlined />
              中断
            </Button>
          )}

          <Divider type="vertical" className="h-4" />

          <Button
            type="link"
            className="p-0"
            onClick={() =>
              window.open(
                `/train/task-execution/${record.id}?team=${currentTeam}&tab=1`,
                '_blank',
              )
            }
          >
            详情
          </Button>
          <Button
            type="link"
            className="p-0"
            onClick={() =>
              window.open(
                `/train/task-execution/${record.id}?team=${currentTeam}&tab=2`,
                '_blank',
              )
            }
          >
            监控
          </Button>
          <Button
            type="link"
            className="p-0"
            onClick={() => {
              getMonitorUrl(record.id).then((res) => {
                window.open(res.url, '_blank')
              })
            }}
            disabled={isDashboardEnabled(record)}
          >
            日志
          </Button>
          <Button
            type="link"
            className="p-0"
            icon={
              <img
                alt="牵星icon"
                src={qianxing}
                className="w-[14px] h-[14px]"
                style={
                  // !record.isHeadReady || isDashboardEnabled(record)
                  isDashboardEnabled(record)
                    ? { filter: 'grayscale(1)', opacity: 0.6 }
                    : {}
                }
              />
            }
            onClick={() => {
              getMonitorUrl(record.id).then((res) => {
                window.open(res.url, '_blank')
              })
            }}
            disabled={isDashboardEnabled(record)}
          >
            实时 pod
          </Button>
        </div>
      ),
    },
  ]
  return (
    <>
      {userInfo?.employeeNo ? (
        <ProTable<TrainTaskExecution>
          className="mt-[-16px]"
          columns={columns}
          actionRef={actionRef}
          search={false}
          scroll={{ x: 1300, y: 'calc(100vh - 282px)' }}
          ghost
          params={monitorParams}
          debounceTime={800}
          request={getTableRequest}
          {...(isHardLoading ? { loading: false } : {})}
          editable={{
            type: 'multiple',
          }}
          rowKey="id"
          options={false}
          pagination={{
            defaultPageSize: 20,
            pageSizeOptions: ['10', '20', '50', '100'],
            showSizeChanger: true,
          }}
          toolbar={{
            title: (
              <CachedRadioGroup
                cacheKey="trainTaskExecutionManagement_onlyMy" // 缓存标记
                value={createBy} // 双向绑定数据
                onChange={setCreateBy} // 更新表单值
                options={[
                  {
                    label: CreateByEnumToLabel[EnumCreateBy.ALL],
                    value: EnumCreateBy.ALL,
                  },
                  {
                    label: CreateByEnumToLabel[EnumCreateBy.BY_ME],
                    value: EnumCreateBy.BY_ME,
                  },
                ]}
              />
            ),
            filter: (
              <Space>
                <Checkbox.Group
                  name="checkbox-group"
                  value={taskExecutionState}
                  onChange={setTaskExecutionState}
                >
                  {TrainTaskExecutionStateList.map((status) => (
                    <Checkbox value={status.value} key={status.value}>
                      <Space className="flex items-center flex-nowrap">
                        {status.label}
                        {status.icon}
                      </Space>
                    </Checkbox>
                  ))}
                </Checkbox.Group>
                <Input
                  placeholder="请输入关联任务名称"
                  style={{ width: 200 }}
                  allowClear
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
                <Button
                  type="primary"
                  shape="circle"
                  icon={<SearchOutlined />}
                  onClick={() => actionRef.current?.reload(true)}
                />
              </Space>
            ),
          }}
        />
      ) : (
        <Skeleton active className="w-full h-[calc(100vh-290px)]" />
      )}

      {contextHolder}

      {/* 任务调整对话框 */}
      <TaskAdjustDialog
        open={adjustDialogOpen}
        onCancel={handleAdjustCancel}
        onConfirm={handleAdjustConfirm}
        currentPriority={currentAdjustTask?.priority}
        loading={adjustLoading}
      />
    </>
  )
}
