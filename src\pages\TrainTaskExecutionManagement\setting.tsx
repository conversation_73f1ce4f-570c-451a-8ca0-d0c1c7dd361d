import type { TrainingFramework } from '@/api/traintask'
import leaf from '@/assets/leaf.svg'
import load from '@/assets/load.svg'
import {
  CheckCircleFilled,
  CloseCircleFilled,
  MinusCircleFilled,
} from '@ant-design/icons'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)

export type EnumToUnion<T extends string> = `${T}`

export type TrainTaskExecution = {
  taskType: string
  clusterId: number
  clusterName: string
  namespace: string

  dashboardUrl: string
  duration: number
  startTime: string | null
  endTime: string | null
  triggerTime: string | null
  executionName: string
  id: number

  imageUrl: string
  priority: EnumToUnion<EnumPriority>

  status: EnumTrainTaskExecutionState

  taskId: number
  taskName: string
  teamId: number
  teamName: string
  triggerSource: EnumToUnion<EnumTriggerSource>
  triggeredByEmployeeNo: string
  triggeredByUserName: string

  isHeadReady: boolean
  trainingFramework: TrainingFramework

  estimatedWaitTimeSeconds: number
}

export enum EnumPriority {
  P0 = 'P0',
  P1 = 'P1',
  P2 = 'P2',
  P3 = 'P3',
}

export enum EnumCreateBy {
  BY_ME = 'BY_ME',
  ALL = 'ALL',
}

export const CreateByEnumToLabel = {
  [EnumCreateBy.BY_ME]: '我触发的',
  [EnumCreateBy.ALL]: '全部',
}

export type UnionCreateBy = EnumToUnion<EnumCreateBy>

export enum EnumTriggerSource {
  MANUAL = 'MANUAL',
  SCHEDULED = 'SCHEDULED',
  API_CALL = 'API_CALL',
}

export const TriggerSourceEnumToLabel = {
  [EnumTriggerSource.MANUAL]: '手动触发',
  [EnumTriggerSource.SCHEDULED]: '定时触发',
  [EnumTriggerSource.API_CALL]: 'API 触发',
}

export enum EnumTrainTaskExecutionState {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export const TrainTaskExecutionStateMap = {
  [EnumTrainTaskExecutionState.PENDING]: {
    label: '排队中',
    icon: <img className="w-[14px]" src={leaf} alt="leaf" />,
    value: EnumTrainTaskExecutionState.PENDING,
    canRerun: false,
    canInterrupt: true,
    bg: '#EBF8E9',
  },
  [EnumTrainTaskExecutionState.RUNNING]: {
    label: '运行中',
    icon: <img className="w-[14px]" src={load} alt="load" />,
    value: EnumTrainTaskExecutionState.RUNNING,
    canRerun: false,
    canInterrupt: true,
    bg: '#D6D4F8',
  },
  [EnumTrainTaskExecutionState.SUCCEEDED]: {
    label: '运行成功',
    icon: <CheckCircleFilled className="text-[#52C41A]" />,
    value: EnumTrainTaskExecutionState.SUCCEEDED,
    canRerun: true,
    canInterrupt: false,
    bg: '#EBF8E9',
  },
  [EnumTrainTaskExecutionState.FAILED]: {
    label: '运行失败',
    icon: <CloseCircleFilled className="text-[#F1786D]" />,
    value: EnumTrainTaskExecutionState.FAILED,
    canRerun: true,
    canInterrupt: false,
    bg: '#FCE4E2',
  },
  [EnumTrainTaskExecutionState.CANCELLED]: {
    label: '手动中断',
    icon: <MinusCircleFilled className="text-[#777777]" />,
    value: EnumTrainTaskExecutionState.CANCELLED,
    canRerun: true,
    canInterrupt: false,
    bg: '#E5E5E3',
  },
}

export const TrainTaskExecutionStateList = Object.values(
  TrainTaskExecutionStateMap,
)

export const TrainTaskExecutionStateKeyList = Object.keys(
  TrainTaskExecutionStateMap,
)

export const formatDuration = (startTime: string, endTime?: string) => {
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const duration = dayjs
    .duration(end.diff(start))
    .format('HH [小时] mm [分钟] ss [秒]')

  return duration
}

export function formatDurationSimple(dur: number) {
  const durationObj = dayjs.duration(dur)

  if (durationObj.asHours() < 1) {
    // 小于1小时：只显示分钟（取整）
    return `${Math.floor(durationObj.asMinutes())}分钟`
  }
  // 大于等于1小时：只显示小时（取整）
  return `${Math.floor(durationObj.asHours())}小时`
}
