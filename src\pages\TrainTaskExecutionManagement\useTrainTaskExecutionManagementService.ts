import {
  getTrainTaskExecutionList,
  interruptTrainTaskExecution,
  triggerTrainTask,
} from '@/api/traintask'
import useUserStore from '@/stores/user'
import { formatDateRange } from '@/utils/useProTableService'
import type { ActionType, ProTableProps } from '@ant-design/pro-components'
import { Modal, message } from 'antd'
import { useEffect, useRef, useState } from 'react'
import { useLocation } from 'react-router-dom'
import {
  EnumCreateBy,
  EnumTrainTaskExecutionState,
  type TrainTaskExecution,
  TrainTaskExecutionStateKeyList,
  type UnionCreateBy,
} from './setting'

export default function useTrainTaskExecutionManagementService() {
  const userInfo = useUserStore((state) => state.userInfo)
  const currentTeam = useUserStore((state) => state.currentTeam)
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)

  const [modal, contextHolder] = Modal.useModal()

  const [createBy, setCreateBy] = useState<UnionCreateBy>(EnumCreateBy.ALL)
  const [taskName, setTaskName] = useState<string>()
  const [taskExecutionState, setTaskExecutionState] = useState<string[]>(
    TrainTaskExecutionStateKeyList,
  )
  const [refreshKey, setRefreshKey] = useState(0)
  const [isHardLoading, setIsHardLoading] = useState(false)

  const actionRef = useRef<ActionType>()

  // 清除 URL 中的 id 参数，防止带到其它页面中去
  useEffect(() => {
    if (searchParams.has('id')) {
      setCreateBy(EnumCreateBy.ALL)
      searchParams.delete('id')
      // 强制刷新 URL
      window.history.replaceState(null, '', `?${searchParams.toString()}`)
    }
  }, [searchParams])

  // 定时器：每秒刷新一次，主要用于更新表格的时间戳等动态数据
  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshKey((prev) => prev + 1) // 每秒刷新一次
    }, 1000)

    return () => clearInterval(interval) // 清除定时器
  }, [])

  // 定时器：定期无感知地刷新表格状态，获取最新状态
  useEffect(() => {
    const interval = setInterval(async () => {
      actionRef.current?.reload() // 刷新表格数据
      setIsHardLoading(true) // 强制刷新标记
    }, 5000) // 每 5 秒刷新一次

    return () => clearInterval(interval) // 清除定时器
  }, [])

  // 监听以下参数的变化，自动重新加载数据
  const monitorParams = {
    taskName,
    teamId: currentTeam,
    statuses: taskExecutionState,
    ...(createBy === EnumCreateBy.BY_ME
      ? { triggeredByEmployeeNo: userInfo?.employeeNo }
      : null),
  }

  const getTableRequest: ProTableProps<TrainTaskExecution, any>['request'] =
    async (params, sort, filter) => {
      // 表单搜索项会从 params 传入，传递给后端接口。
      console.log(11111, params, sort, filter)
      if (isHardLoading) {
        setIsHardLoading(false)
      }
      const msg = await getTrainTaskExecutionList({
        params: {
          ...params,
          page: params.current,
          triggerSources: filter?.triggerSource,
          ...(filter.id?.[0] ? { executionId: filter.id[0] } : null),
          ...formatDateRange('triggerTime', filter),
          ...formatDateRange('startTime', filter),
          ...formatDateRange('endTime', filter),
        },
      })
      return {
        data: msg.list,
        // success 请返回 true，
        // 不然 table 会停止解析数据，即使有数据
        success: true,
        total: msg.total,
      }
    }

  const onRerun = async (record: TrainTaskExecution) => {
    console.log('Re-running task execution:', record)
    await modal.confirm({
      title: '提示',
      centered: true,
      content: `您确定要重新运行任务 “${record.taskName}” 吗？`,
      onOk: async () => {
        await triggerTrainTask(record.taskId)
        message.success('任务重新运行成功！')
        actionRef.current?.reload?.()
      },
    })
  }

  const onInterrupt = async (record: TrainTaskExecution) => {
    console.log('Interrupt task execution:', record)
    await modal.confirm({
      title: '提示',
      centered: true,
      content: `您确定要中断任务 “${record.taskName}” 吗？`,
      onOk: async () => {
        await interruptTrainTaskExecution(record.id)
        message.success('任务中断成功！')
        actionRef.current?.reload?.()
      },
    })
  }

  const onGrafanaMonitor = (record: TrainTaskExecution) => {
    window.open(
      `https://grafana.example.com/d/1234567890/task-monitor?taskId=${record.id}`,
      '_blank',
    )
  }
  const onRayMonitor = (record: TrainTaskExecution) => {
    window.open(record.dashboardUrl, '_blank')
  }

  const isDashboardEnabled = (record: TrainTaskExecution) =>
    record.status === EnumTrainTaskExecutionState.CANCELLED ||
    record.status === EnumTrainTaskExecutionState.PENDING

  return {
    getTableRequest,
    taskExecutionState,
    setTaskExecutionState,
    actionRef,
    taskName,
    setTaskName,
    onRerun,
    onInterrupt,
    onGrafanaMonitor,
    onRayMonitor,
    contextHolder,
    createBy,
    setCreateBy,
    userInfo,
    idFromUrl: location.search.includes('id') ? searchParams.get('id') : null,
    monitorParams,
    isDashboardEnabled,
    refreshKey,
    isHardLoading,
    currentTeam,
  }
}
