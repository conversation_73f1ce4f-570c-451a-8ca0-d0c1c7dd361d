import {
  type TrainTaskDetail,
  type TrainTaskItem,
  createTrainTask,
  getTrainTaskDetail,
  updateTrainTask,
} from '@/api/traintask'
import TaskForm, {
  type TaskFormData,
  type TaskFormRef,
} from '@/components/forms/TaskForm'
import type { ModalType } from '@/types/common'
import { convertTrainTaskDetailToFormData } from '@/utils/dataConvert'
import { InfoCircleOutlined } from '@ant-design/icons'
import { But<PERSON>, Divider, Drawer, Popover, Tooltip, message } from 'antd'
import { forwardRef, useImperativeHandle, useRef, useState } from 'react'

export interface CreateTaskDrawerRef {
  showDrawer: (type: ModalType, item?: TrainTaskItem) => void
}

interface CreateTaskDrawerProps {
  onSubmit?: (values: TrainTaskDetail) => void
  onDelete?: (item: TrainTaskItem) => void
}

const titleMap = {
  create: '创建任务',
  edit: '编辑任务',
  read: '查看任务',
  copy: '复制任务',
}

const CreateTaskDrawer = forwardRef<CreateTaskDrawerRef, CreateTaskDrawerProps>(
  ({ onSubmit, onDelete }, ref) => {
    const [open, setOpen] = useState(false)
    const [type, setType] = useState<ModalType>('create')
    const [taskId, setTaskId] = useState<number>()
    const taskFormRef = useRef<TaskFormRef>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [record, setRecord] = useState<TrainTaskItem>()
    const showDrawer = async (type: ModalType, item?: TrainTaskItem) => {
      setType(type)
      if ((type === 'edit' || type === 'read' || type === 'copy') && item) {
        const { data } = await getTrainTaskDetail(item.id)
        const formData = convertTrainTaskDetailToFormData(data)
        console.log('formData', formData)
        setTaskId(item.id)
        setRecord(item)
        setTimeout(() => {
          taskFormRef.current?.setValues(formData)
        }, 0)
      } else {
        taskFormRef.current?.reset()
      }
      console.log('taskFormRef.current', taskFormRef.current)
      setOpen(true)
    }

    const onClose = () => {
      setOpen(false)
    }

    const handleSubmit = () => {
      taskFormRef.current?.submit()
    }

    const onFinish = async (values: TrainTaskDetail) => {
      console.log('提交任务数据:', values)
      setIsLoading(true)
      try {
        if (type === 'create' || type === 'copy') {
          await createTrainTask(values)
        } else {
          await updateTrainTask(String(taskId!), values)
        }
        message.success('提交成功')
        onSubmit?.(values)
        onClose()
      } finally {
        setIsLoading(false)
      }
    }

    const onFinishFailed = (errorInfo: any) => {
      console.error('Form validation failed:', errorInfo)
    }

    useImperativeHandle(ref, () => ({
      showDrawer,
    }))

    return (
      <Drawer
        title={titleMap[type]}
        width={'65vw'}
        closable={{ 'aria-label': 'Close Button' }}
        onClose={onClose}
        maskClosable={type === 'read'}
        open={open}
        footer={
          type === 'read' ? null : (
            <div className="flex justify-start gap-3 items-center">
              <Popover
                arrow={{ pointAtCenter: true }}
                content={
                  <span>
                    注意：任务{type === 'edit' ? '保存' : '创建'}后，需要
                    <strong>&nbsp;手动运行&nbsp;</strong>
                    才能触发任务。
                  </span>
                }
              >
                <Button
                  type="primary"
                  onClick={handleSubmit}
                  loading={isLoading}
                  icon={<InfoCircleOutlined />}
                >
                  {type === 'edit' ? '保存' : '创建'}
                </Button>
              </Popover>
              <Button onClick={onClose}>取消</Button>
              {type === 'edit' && (
                <>
                  <Divider type="vertical" className="h-4" />
                  <Button
                    onClick={() => record && onDelete?.(record)}
                    color="danger"
                    variant="outlined"
                  >
                    删除
                  </Button>
                </>
              )}
            </div>
          )
        }
      >
        <TaskForm
          ref={taskFormRef}
          type={type}
          disabled={type === 'read'}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
        />
      </Drawer>
    )
  },
)

export default CreateTaskDrawer
