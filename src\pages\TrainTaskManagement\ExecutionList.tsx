import {
  getMonitorUrl,
  getTrainTaskExecutionList,
  interruptTrainTaskExecution,
  triggerTrainTask,
} from '@/api/traintask'
import qianxing from '@/assets/qianxing.png'
import RayIcon from '@/assets/ray.svg?react'
import useUserStore from '@/stores/user'
import { processLinkTo } from '@/utils/navigation'
import { ReloadOutlined, StopOutlined } from '@ant-design/icons'
import { Button, Divider, Modal, Popover, Space, message } from 'antd'
import { useState } from 'react'
import { NavLink } from 'react-router-dom'
import {
  EnumTrainTaskExecutionState,
  type TrainTaskExecution,
  TrainTaskExecutionStateMap,
} from '../TrainTaskExecutionManagement/setting'

const ExecutionList = ({
  id,
  onExecutionListChange,
}: {
  id: number
  onExecutionListChange?: (list: TrainTaskExecution[]) => void
}) => {
  const currentTeam = useUserStore((state) => state.currentTeam)
  const [executionList, setExecutionList] = useState<TrainTaskExecution[]>([])
  const [loading, setLoading] = useState(false)
  const [modal, contextHolder] = Modal.useModal()

  const fetchData = (open: boolean) => {
    if (open) {
      setLoading(true)
      getTrainTaskExecutionList({
        params: {
          page: 1,
          pageSize: 10,
          TaskId: id,
        },
      })
        .then((res) => {
          setExecutionList(res.list)
          onExecutionListChange?.(res.list)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  const isDashboardEnabled = (record: TrainTaskExecution) =>
    record.status === EnumTrainTaskExecutionState.CANCELLED ||
    record.status === EnumTrainTaskExecutionState.PENDING

  const onInterrupt = async (record: TrainTaskExecution) => {
    await modal.confirm({
      title: '提示',
      centered: true,
      content: `您确定要中断任务 "${record.executionName}" 吗？`,
      onOk: async () => {
        await interruptTrainTaskExecution(record.id)
        message.success('任务中断成功！')
        fetchData(true) // 刷新数据
      },
    })
  }

  const onRerun = async (record: TrainTaskExecution) => {
    console.log('Re-running task execution:', record)
    await modal.confirm({
      title: '提示',
      centered: true,
      content: `您确定要重新运行任务 “${record.taskName}” 吗？`,
      onOk: async () => {
        await triggerTrainTask(record.taskId)
        message.success('任务重新运行成功！')
        fetchData(true)
      },
    })
  }

  return (
    <>
      <Popover
        placement="topRight"
        color="white"
        arrow={{
          pointAtCenter: true,
        }}
        onOpenChange={fetchData}
        content={
          <div className="text-[#666666]">
            {executionList?.length ? (
              executionList.map((item) => {
                const obj = TrainTaskExecutionStateMap[item.status]
                return (
                  <div
                    key={item.id}
                    className="flex items-center gap-3 mb-2 last:mb-0"
                  >
                    <Button
                      type="link"
                      className="p-0 h-auto text-[#7569f0] flex-1 min-w-0 truncate text-left"
                      onClick={() =>
                        window.open(
                          `/train/task-execution/${item.id}?team=${currentTeam}`,
                          '_blank',
                        )
                      }
                    >
                      {item.executionName}
                    </Button>
                    <Space className="flex w-20 flex-shrink-0">
                      {obj?.icon}
                      {obj?.label}
                    </Space>

                    <div className="flex items-center justify-around w-[250px]">
                      {TrainTaskExecutionStateMap[item.status]?.canRerun && (
                        <Button
                          key="rerun"
                          type="link"
                          className="p-0"
                          onClick={() => onRerun(item)}
                        >
                          <ReloadOutlined />
                          重跑
                        </Button>
                      )}
                      {TrainTaskExecutionStateMap[item.status]
                        ?.canInterrupt && (
                        <Button
                          key="interrupt"
                          type="link"
                          danger
                          className="p-0"
                          onClick={() => onInterrupt(item)}
                        >
                          <StopOutlined />
                          中断
                        </Button>
                      )}

                      <Divider type="vertical" className="h-4" />

                      <Button
                        type="link"
                        className="p-0"
                        onClick={() =>
                          window.open(
                            `/train/task-execution/${item.id}?team=${currentTeam}&tab=1`,
                            '_blank',
                          )
                        }
                      >
                        详情
                      </Button>
                      <Button
                        type="link"
                        className="p-0"
                        onClick={() =>
                          window.open(
                            `/train/task-execution/${item.id}?team=${currentTeam}&tab=2`,
                            '_blank',
                          )
                        }
                      >
                        监控
                      </Button>
                      <Button
                        type="link"
                        className="p-0"
                        onClick={() => {
                          getMonitorUrl(item.id).then((res) => {
                            window.open(res.url, '_blank')
                          })
                        }}
                        disabled={isDashboardEnabled(item)}
                      >
                        日志
                      </Button>
                      <Button
                        type="link"
                        className="p-0"
                        icon={
                          <img
                            alt="牵星icon"
                            src={qianxing}
                            className="w-[14px] h-[14px]"
                            style={
                              // !item.isHeadReady || isDashboardEnabled(item)
                              isDashboardEnabled(item)
                                ? { filter: 'grayscale(1)', opacity: 0.6 }
                                : {}
                            }
                          />
                        }
                        onClick={() => {
                          getMonitorUrl(item.id).then((res) => {
                            window.open(res.url, '_blank')
                          })
                        }}
                        disabled={isDashboardEnabled(item)}
                      >
                        实时 pod
                      </Button>
                    </div>
                  </div>
                )
              })
            ) : (
              <div>{loading ? '数据获取中...' : '暂无数据'}</div>
            )}
          </div>
        }
      >
        <Button type="link" className="!mx-0 !px-0">
          详情
        </Button>
      </Popover>
      {contextHolder}
    </>
  )
}

export default ExecutionList
