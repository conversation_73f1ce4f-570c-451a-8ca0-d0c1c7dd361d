import { getMonitorUrl, getTrainTaskExecutionList } from '@/api/traintask'
import qianxing from '@/assets/qianxing.png'
import InfoTooltip from '@/components/common/InfoTooltip'
import PriorityDisplay from '@/components/common/PriorityDisplay'
import { TRAINING_FRAMEWORK_MAP } from '@/constants/trainingFramework'
import useUserStore from '@/stores/user'
import { ReloadOutlined, StopOutlined } from '@ant-design/icons'
import { Button, Divider, Space, Table, Tag, message } from 'antd'
import type { TableProps } from 'antd'
import type React from 'react'
import { useEffect, useState } from 'react'
import {
  type TrainTaskExecution,
  TrainTaskExecutionStateMap,
  formatDuration,
  formatDurationSimple,
} from '../TrainTaskExecutionManagement/setting'

interface TaskExecutionTableProps {
  taskId: number
}

const TaskExecutionTable: React.FC<TaskExecutionTableProps> = ({ taskId }) => {
  const currentTeam = useUserStore((state) => state.currentTeam)
  const [loading, setLoading] = useState(false)
  const [executionList, setExecutionList] = useState<TrainTaskExecution[]>([])

  // 获取运行记录数据
  const fetchExecutionList = async () => {
    setLoading(true)
    try {
      const res = await getTrainTaskExecutionList({
        params: {
          page: 1,
          pageSize: 50, // 展开表格显示更多记录
          TaskId: taskId,
        },
      })
      setExecutionList(res.list)
    } catch (error) {
      console.error('获取运行记录失败:', error)
      message.error('获取运行记录失败')
    } finally {
      setLoading(false)
    }
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: 间接依赖就是 taskId
  useEffect(() => {
    if (taskId) {
      fetchExecutionList()
    }
  }, [taskId])

  // 判断是否禁用监控按钮
  const isDashboardEnabled = (record: TrainTaskExecution) => {
    return !record.isHeadReady || record.status !== 'RUNNING'
  }

  const columns: TableProps<TrainTaskExecution>['columns'] = [
    {
      title: '运行记录ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (_, record) => (
        <Button
          type="link"
          className="p-0 h-auto"
          onClick={() =>
            window.open(
              `/train/task-execution/${record.id}?team=${currentTeam}`,
              '_blank',
            )
          }
        >
          {record.id}
        </Button>
      ),
    },
    {
      title: '运行记录名称',
      dataIndex: 'executionName',
      key: 'executionName',
      width: 200,
      ellipsis: true,
      render: (_, record) => (
        <Button
          type="link"
          className="p-0 h-auto"
          onClick={() =>
            window.open(
              `/train/task-execution/${record.id}?team=${currentTeam}`,
              '_blank',
            )
          }
        >
          {record.executionName}
        </Button>
      ),
    },
    {
      title: '优先级',
      width: 100,
      dataIndex: 'priority',
      key: 'priority',
      align: 'center',
      render: (_, record) => {
        return <PriorityDisplay priority={record.priority} placement="right" />
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (_, record) => {
        const obj = TrainTaskExecutionStateMap[record.status]
        return (
          <Space className="flex justify-center">
            {obj?.icon}
            {obj?.label}
          </Space>
        )
      },
    },
    {
      title: '运行时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 180,
      align: 'center',
      render: (_, record) => (
        <>
          {record.status === 'PENDING' ? (
            <span className="text-[#999999]">
              （预计排队
              {formatDurationSimple(record.estimatedWaitTimeSeconds * 1000)}）
            </span>
          ) : record.startTime ? (
            formatDuration(record.startTime, record.endTime ?? undefined)
          ) : (
            <div className="text-[#999999]">（未曾运行）</div>
          )}
        </>
      ),
    },
    {
      title: '触发人',
      dataIndex: 'triggeredByUserName',
      key: 'triggeredByUserName',
      width: 100,
      align: 'center',
    },
    {
      title: '触发时间',
      dataIndex: 'triggerTime',
      key: 'triggerTime',
      width: 180,
      align: 'center',
    },
    {
      title: '开始运行时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 180,
      align: 'center',
      render: (_, record) =>
        record?.startTime ? (
          record?.startTime
        ) : (
          <span className="text-[#999999]">（尚未开始）</span>
        ),
    },
    {
      title: '结束运行时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 180,
      align: 'center',
      render: (_, record) =>
        record?.endTime ? (
          record?.endTime
        ) : (
          <span className="text-[#999999]">
            （尚未{`${record.startTime ? '结束' : '开始'}`}）
          </span>
        ),
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      align: 'center',
      render: (_, record) => (
        <div className="flex items-center justify-around">
          <Button
            type="link"
            className="p-0"
            onClick={() =>
              window.open(
                `/train/task-execution/${record.id}?team=${currentTeam}&tab=1`,
                '_blank',
              )
            }
          >
            详情
          </Button>
          <Button
            type="link"
            className="p-0"
            onClick={() =>
              window.open(
                `/train/task-execution/${record.id}?team=${currentTeam}&tab=2`,
                '_blank',
              )
            }
          >
            监控
          </Button>
          <Button
            type="link"
            className="p-0"
            onClick={() => {
              getMonitorUrl(record.id).then((res) => {
                window.open(res.url, '_blank')
              })
            }}
            disabled={isDashboardEnabled(record)}
          >
            日志
          </Button>
          <Button
            type="link"
            className="p-0"
            icon={
              <img
                alt="牵星icon"
                src={qianxing}
                className="w-[14px] h-[14px]"
                style={
                  isDashboardEnabled(record)
                    ? { filter: 'grayscale(1)', opacity: 0.6 }
                    : {}
                }
              />
            }
            onClick={() => {
              getMonitorUrl(record.id).then((res) => {
                window.open(res.url, '_blank')
              })
            }}
            disabled={isDashboardEnabled(record)}
          >
            实时 pod
          </Button>
        </div>
      ),
    },
  ]

  return (
    <div>
      <Table<TrainTaskExecution>
        loading={loading}
        columns={columns}
        dataSource={executionList}
        rowKey="id"
        pagination={false}
        scroll={{ x: 1200 }}
      />
    </div>
  )
}

export default TaskExecutionTable
