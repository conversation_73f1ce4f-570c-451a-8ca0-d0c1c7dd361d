import {
  type TrainTaskItem,
  createTrainTask,
  deleteTrainTask,
  getTrainTaskExecutionList,
  interruptTrainTaskExecution,
  triggerTrainTask,
  useTrainTaskList,
} from '@/api/traintask'
import CachedRadioGroup from '@/components/CachedRadioGroup'
import PriorityTooltip from '@/components/PriorityTooltip'
import CommonTable from '@/components/common/CommonTable'
import InfoTooltip from '@/components/common/InfoTooltip'
import PriorityDisplay from '@/components/common/PriorityDisplay'
import { TRAINING_FRAMEWORK_MAP } from '@/constants/trainingFramework'
import useUserStore from '@/stores/user'
import { CodeOutlined, CopyOutlined, SearchOutlined } from '@ant-design/icons'
import {
  Button,
  Checkbox,
  Divider,
  Form,
  Input,
  Modal,
  Tag,
  Tooltip,
  message,
} from 'antd'
import type { TableProps } from 'antd'
import dayjs from 'dayjs'
import type React from 'react'
import { useMemo, useRef, useState } from 'react'
import { useDebounce } from 'use-debounce'
import { TrainTaskExecutionStateMap } from '../TrainTaskExecutionManagement/setting'
import CreateTaskDrawer, { type CreateTaskDrawerRef } from './CreateTaskDrawer'
import ExecutionList from './ExecutionList'
import TaskExecutionTable from './TaskExecutionTable'

export const TaskManagementList: React.FC = () => {
  const [modal, contextHolder] = Modal.useModal()
  const currentTeam = useUserStore((state) => state.currentTeam)
  const createTaskDrawerRef = useRef<CreateTaskDrawerRef>(null)
  const [form] = Form.useForm<{
    onlyMy: boolean
    onlyUnfinished: boolean
    taskName: string
  }>()
  const formVal = Form.useWatch((values) => {
    setPage(1)
    return values
  }, form)
  const [page, setPage] = useState(1)
  const [size, setSize] = useState(10)
  const [params] = useDebounce(
    {
      page,
      pageSize: size,
      teamId: currentTeam,
      onlyMy: formVal?.onlyMy === undefined ? true : formVal.onlyMy,
      onlyUnfinished: formVal?.onlyUnfinished || false,
      taskName: formVal?.taskName || '',
    },
    500,
  )
  const { data, refetch, isLoading } = useTrainTaskList(params)
  const handleDelete = (record: TrainTaskItem) => {
    const isRunning =
      record.lastStatus === 'PENDING' || record.lastStatus === 'RUNNING'
    const content = isRunning
      ? `任务 “${record.taskName}” 还尚未结束运行，您确定要强制中断并删除任务吗？`
      : `您确定要删除任务 “${record.taskName}” 吗？`
    modal.confirm({
      title: '提示',
      content,
      centered: true,
      onOk: async () => {
        if (isRunning) {
          // 先取消任务
          const res = await getTrainTaskExecutionList({
            params: {
              page: 1,
              pageSize: 10,
              TaskId: record.id,
            },
          })
          await interruptTrainTaskExecution(res.list[0].id)
        }
        await deleteTrainTask(record.id)
        message.success('删除成功')
        refetch()
      },
      onCancel: () => {},
      okText: '确定',
      cancelText: '取消',
    })
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const columns = useMemo<TableProps<TrainTaskItem>['columns']>(() => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        fixed: 'left',
        render: (_, record) => {
          return (
            <div className="flex items-center">
              {record.id}
              <Button color="primary" variant="link">
                <CopyOutlined
                  color="primary"
                  onClick={() => {
                    navigator.clipboard.writeText(record.id.toString())
                    message.success('复制成功')
                  }}
                />
              </Button>
            </div>
          )
        },
      },
      {
        title: '名称',
        dataIndex: 'taskName',
        key: 'taskName',
        width: 200,
        ellipsis: true,
        fixed: 'left',
        render: (_, record) => {
          return <div className="max-w-80 truncate">{record.taskName}</div>
        },
      },
      {
        title: (
          <InfoTooltip
            title="影响资源调度逻辑：优先级越高越先安排资源，在资源不足时，高级别任务会抢占低级别任务的资源。"
            iconPosition="right"
            iconClassName="ml-2"
          >
            优先级
          </InfoTooltip>
        ),
        width: 100,
        dataIndex: 'priority',
        key: 'priority',
        align: 'center',
        render: (_, record) => {
          return (
            <PriorityDisplay priority={record.priority} placement="right" />
          )
        },
      },
      {
        title: (
          <div>
            <InfoTooltip
              title="任务创建后不能修改"
              iconPosition="right"
              iconClassName="ml-2"
            >
              任务类型
            </InfoTooltip>
          </div>
        ),
        width: 120,
        dataIndex: 'trainingFramework',
        key: 'trainingFramework',
        align: 'center',
        render: (_, record) => {
          return (
            TRAINING_FRAMEWORK_MAP[
              record.trainingFramework as keyof typeof TRAINING_FRAMEWORK_MAP
            ] || record.trainingFramework
          )
        },
      },
      {
        title: '集群 / 命名空间',
        dataIndex: 'cn',
        ellipsis: true,
        width: 200,
        key: 'cn',
        render: (_, record) => {
          return `${record.clusterName} / ${record.namespace}`
        },
      },
      // {
      //   title: (
      //     <InfoTooltip
      //       title={
      //         <>
      //           未结束：排队中 / 运行中
      //           <br />
      //           已结束：手动中断 / 运行成功 / 运行失败
      //         </>
      //       }
      //       iconPosition="right"
      //       iconClassName="ml-2"
      //     >
      //       运行情况（已结束 / 全部）
      //     </InfoTooltip>
      //   ),
      //   width: 240,
      //   dataIndex: 'status',
      //   key: 'status',
      //   align: 'right',
      //   render: (_, record) => {
      //     const obj = TrainTaskExecutionStateMap[record.lastStatus]
      //     return record.triggerCount === 0 ? (
      //       <div className="text-[#999999]">（任务未曾运行）</div>
      //     ) : (
      //       <div className="flex justify-end items-center gap-3">
      //         <div>{obj?.icon}</div>
      //         <div> {`${record.completeCount} / ${record.triggerCount}`}</div>
      //         <ExecutionList
      //           id={record.id}
      //           onExecutionListChange={(list) => {
      //             if (list[0]?.status !== record.lastStatus) {
      //               refetch()
      //             }
      //           }}
      //         />
      //       </div>
      //     )
      //   },
      // },

      {
        title: (
          <InfoTooltip
            title="任务创建后不能修改"
            iconPosition="right"
            iconClassName="ml-2"
          >
            创建方式
          </InfoTooltip>
        ),
        width: 120,
        dataIndex: 'taskType',
        key: 'taskType',
        align: 'center',
        render: (_, record) => {
          return record.taskType === 'FORM' ? (
            <Tag color="#dbd6fa" className="!text-[#666666] w-16 text-center">
              表单
            </Tag>
          ) : (
            <Tag color="#d7f1d4" className="!text-[#666666] w-16 text-center">
              YAML
            </Tag>
          )
        },
      },
      {
        title: '创建人',
        dataIndex: 'createdByUserName',
        key: 'createdByUserName',
        align: 'center',
        width: 100,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        width: 180,
        key: 'createdAt',
        align: 'center',
        render: (_, record) => {
          return dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss')
        },
      },
      {
        title: '更新人',
        dataIndex: 'updatedByUserName',
        key: 'updatedByUserName',
        align: 'center',
        width: 100,

        render: (_, record) => {
          return record.updatedByUserName || '-'
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        align: 'center',
        width: 180,
        render: (_, record) => {
          return dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm:ss')
        },
      },
      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        align: 'center',
        fixed: 'right',
        width: 280,
        render: (_, record) => (
          <div className="flex  justify-around items-center">
            <Button
              size="small"
              type="link"
              icon={<CodeOutlined />}
              onClick={async () => {
                await modal.confirm({
                  title: '提示',
                  centered: true,
                  content: `您确定要运行任务 “${record.taskName}” 吗？`,
                  onOk: async () => {
                    await triggerTrainTask(record.id)
                    message.success('触发任务成功')
                    refetch()
                  },
                })
              }}
            >
              运行
            </Button>

            <Divider type="vertical" className="h-4" />

            <Button
              size="small"
              color="primary"
              variant="link"
              onClick={() => {
                createTaskDrawerRef.current?.showDrawer('read', record)
              }}
            >
              查看
            </Button>
            <Button
              size="small"
              color="primary"
              variant="link"
              onClick={() => {
                createTaskDrawerRef.current?.showDrawer('edit', record)
              }}
            >
              编辑
            </Button>
            <Button
              size="small"
              color="primary"
              variant="link"
              onClick={() => {
                createTaskDrawerRef.current?.showDrawer('copy', record)
              }}
            >
              复制
            </Button>
            <Button
              size="small"
              color="danger"
              variant="link"
              onClick={() => {
                handleDelete(record)
              }}
            >
              删除
            </Button>
          </div>
        ),
      },
    ]
  }, [])
  return (
    <div>
      <Form
        form={form}
        layout="inline"
        initialValues={{ onlyMy: false, onlyUnfinished: false, taskName: '' }}
      >
        <div className="mb-5 flex justify-between items-center w-full">
          <div>
            <Button
              type="primary"
              onClick={() => {
                createTaskDrawerRef.current?.showDrawer('create')
              }}
            >
              创建
            </Button>
            <Form.Item name="onlyMy" className="mb-0 ml-4" noStyle>
              <CachedRadioGroup
                cacheKey="taskManagementList_onlyMy" // 缓存标记
                value={formVal?.onlyMy} // 双向绑定数据
                onChange={(value) => form.setFieldValue('onlyMy', value)} // 更新表单值
                options={[
                  { label: '全部', value: false },
                  { label: '我创建的', value: true },
                ]}
              />
            </Form.Item>
          </div>
          <div className="flex gap-4 items-center">
            <Form.Item
              name="onlyUnfinished"
              valuePropName="checked"
              className="mb-0"
              noStyle
            >
              <Checkbox>只看未结束的任务</Checkbox>
            </Form.Item>
            <Form.Item name="taskName" className="mb-0" noStyle>
              <Input
                className="w-[280px]"
                placeholder="请输入任务名称"
                allowClear
              />
            </Form.Item>
            <Button
              type="primary"
              shape="circle"
              icon={<SearchOutlined />}
              onClick={() => refetch()}
            />
          </div>
        </div>
      </Form>
      <CommonTable<TrainTaskItem>
        className="[&_.ant-table-expanded-row-fixed]:!p-0"
        scroll={{
          x: data?.list && data.list.length > 0 ? '1300px' : undefined,
          y: 'calc(100vh - 316px)',
        }}
        rowKey={(record) => record.id}
        loading={isLoading}
        columns={columns}
        dataSource={data?.list}
        expandable={{
          expandedRowRender: (record) => (
            <TaskExecutionTable taskId={record.id} />
          ),
          rowExpandable: (record) => record.triggerCount > 0, // 只有运行过的任务才能展开
        }}
        pagination={{
          pageSize: size,
          current: page,
          total: data?.total,
          onChange: (page, size) => {
            setPage(page)
            setSize(size)
          },
        }}
      />
      <CreateTaskDrawer
        ref={createTaskDrawerRef}
        onSubmit={async () => {
          refetch()
        }}
        onDelete={async (record) => {
          handleDelete(record)
        }}
      />
      {contextHolder}
    </div>
  )
}
