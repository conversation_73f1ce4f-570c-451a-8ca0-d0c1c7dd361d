import { yaml } from '@codemirror/lang-yaml'
import { vscodeDark } from '@uiw/codemirror-theme-vscode'
import CodeMirror from '@uiw/react-codemirror'
import { Button, Drawer } from 'antd'
import { forwardRef, useImperativeHandle, useState } from 'react'

export interface YamlViewDrawerRef {
  showDrawer: () => void
}

interface YamlViewDrawerProps {
  title?: string
  yamlContent?: string
}

const YamlViewDrawer = forwardRef<YamlViewDrawerRef, YamlViewDrawerProps>(
  ({ title = 'YAML查看', yamlContent = '' }, ref) => {
    const [open, setOpen] = useState(false)

    const showDrawer = () => {
      setOpen(true)
    }

    const onClose = () => {
      setOpen(false)
    }

    useImperativeHandle(ref, () => ({
      showDrawer,
    }))

    return (
      <Drawer
        title={title}
        width={800}
        closable={{ 'aria-label': 'Close Button' }}
        onClose={onClose}
        open={open}
        maskClosable={true}
        footer={
          <div className="flex justify-end gap-3">
            <Button onClick={onClose}>关闭</Button>
          </div>
        }
      >
        <div className="h-full">
          <CodeMirror
            value={yamlContent}
            height="calc(100vh - 200px)"
            theme={vscodeDark}
            extensions={[yaml()]}
            editable={false}
          />
        </div>
      </Drawer>
    )
  },
)

export default YamlViewDrawer
