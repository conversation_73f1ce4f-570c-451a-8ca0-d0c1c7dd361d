export const trainTaskTemplate = `apiVersion: ray.io/v1
kind: RayJob
metadata:
  name: train-task-xxxx # 填你的任务名，长度不能超过30个字符，支持中文（仅作为模板名称，任务运行时，会自动生成任务实例名）
  namespace: # 这里可以置空，系统永远用上面选择的命名空间数据填充
spec:
  entrypoint: # 训练启动命令，例如 bash /app/jarvis/train.sh
  ttlSecondsAfterFinished: 10
  rayClusterSpec:
    enableInTreeAutoscaling: true
    autoscalerOptions:
      upscalingMode: Default
      idleTimeoutSeconds: 60
      imagePullPolicy: IfNotPresent
      securityContext: {}
      env: []
      envFrom: []
      resources:
        limits:
          cpu: '2'
          memory: 4Gi
        requests:
          cpu: '1'
          memory: 1Gi
    headGroupSpec:
      rayStartParams:
        num-gpus: '0'
      template:
        spec:
          containers:
            - name: ray-head
              image: # 填你的训练镜像，例如 cr.ttyuyin.com/yunwei/ray-jarvisfactory:f1a35eb5-20250411
              ports:
                - containerPort: 6379
                  name: gcs-server
                - containerPort: 8265 # Ray dashboard
                  name: dashboard
                - containerPort: 10001
                  name: client
              resources:
                limits:
                  cpu: '1'
                requests:
                  cpu: '200m'
              # 改成你自己的卷挂载信息
              volumeMounts:
                - mountPath: /home/<USER>/.cache/huggingface
                  name: cache-volume
                  subPath: huggingface
          # 改成你自己的卷
          volumes:
            - name: cache-volume
              persistentVolumeClaim:
                claimName: cache-pvc
    workerGroupSpecs:
      - replicas: 0
        minReplicas: 0
        maxReplicas: 2
        groupName: gpu-group
        rayStartParams:
          num-gpus: '1'
        # Pod 模版
        template:
          spec:
            containers:
              - name: ray-worker # must consist of lower case alphanumeric characters or '-', and must start and end with an alphanumeric character (e.g. 'my-name',  or '123-abc'
                image: cr.ttyuyin.com/yunwei/ray-jarvisfactory:f1a35eb5-20250411
                resources:
                  limits:
                    cpu: '32'
                    memory: '64Gi'
                    52tt.com/gpu-core: '100'
                    52tt.com/gpu-memory: '44'
                  requests:
                    cpu: '16'
                    memory: '32Gi'
                    52tt.com/gpu-core: '100'
                    52tt.com/gpu-memory: '44'
                # 改成你自己的卷挂载信息
                volumeMounts:
                  - mountPath: /home/<USER>/.cache/huggingface
                    name: cache-volume
                    subPath: huggingface
            tolerations:
              - effect: NoSchedule
                key: pool-type
                operator: Equal
                value: gpu
            volumes:
              # 改成你自己的卷
              - name: cache-volume
                persistentVolumeClaim:
                  claimName: cache-pvc`
