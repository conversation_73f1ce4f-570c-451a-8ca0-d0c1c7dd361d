export const customYaml = `apiVersion: batch/v1
kind: Job
metadata:
  name: costom-job
  namespace: cicd
spec:
  selector:
    matchLabels:
      batch.kubernetes.io/controller-uid: 6075a798-b32c-4491-9152-53dd0c7f7949
  suspend: false
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        batch.kubernetes.io/controller-uid: 6075a798-b32c-4491-9152-53dd0c7f7949
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: gpu-model
                operator: In
                values:
                - l20
      containers:
      - args:
        - -c
        - |
          . /storage-New/liuboji/.bashrc && cd /storage-New/liuboji/Amphion/preprocessors/Emilia
          bash ./cmd_24-27.sh
        command:
        - /bin/bash
        image: cr.ttyuyin.com/develop-tools/codercom/cuda-code-server-local:cu124
        imagePullPolicy: IfNotPresent
        name: train-task
        resources:
          limits:
            cpu: "32"
            memory: 96Gi
            tke.cloud.tencent.com/qgpu-core: "200"
          requests:
            cpu: "24"
            memory: 72Gi
            tke.cloud.tencent.com/qgpu-core: "200"
        volumeMounts:
        - mountPath: /dev/shm
          name: dshm
      tolerations:
      - effect: NoSchedule
        key: pool-type
        operator: Equal
        value: gpu
      - effect: NoSchedule
        key: biz
        operator: Equal
        value: mlops
      volumes:
      - emptyDir:
          medium: Memory
          sizeLimit: 36Gi
        name: dshm`
