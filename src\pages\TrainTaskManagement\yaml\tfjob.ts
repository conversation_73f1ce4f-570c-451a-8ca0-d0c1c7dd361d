export const tfjobYaml = `apiVersion: kubeflow.org/v1
kind: TFJob
metadata:
  name: tensorflow-dist-mnist
  namespace: cicd
  labels:
    kueue.x-k8s.io/queue-name: default-local-queue
spec:
  tfReplicaSpecs:
    PS:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          containers:
            - name: tensorflow
              image: kubeflow/tf-dist-mnist-test:latest
              resources:
                requests:
                  cpu: 1
                  memory: "200Mi"
    Worker:
      replicas: 2
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          containers:
            - name: tensorflow
              image: kubeflow/tf-dist-mnist-test:latest
              resources:
                requests:
                  cpu: 1
                  memory: "200Mi"`

