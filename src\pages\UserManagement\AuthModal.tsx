import {
  type Team,
  getTeams,
  getUserInfo,
  setAdmin,
  setConsoleAdmin,
  setKnowledgebaseAdmin,
  setTeamAuth,
  teamTypeMap,
  unsetTeamAuth,
} from '@/api/user'
import CommonTable from '@/components/common/CommonTable'
import useUserStore from '@/stores/user'
import { Button, Checkbox, Modal, Radio, Table, message } from 'antd'
import { useState } from 'react'

const AuthModal = ({
  userId,
  onClose,
  userName,
}: {
  userId: number
  onClose: () => void
  userName: string
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)
  const [teamInfo, setTeamInfo] = useState<Team[]>([])

  const showModal = async () => {
    const data = await getTeams(userId)
    const userInfo = await getUserInfo(userId)
    setIsAdmin(userInfo.role.includes('admin'))
    setTeamInfo(data.teams)
    setIsModalOpen(true)
  }

  const handleCancel = () => {
    setIsModalOpen(false)
    onClose()
  }

  const handleUnset = async (
    teamId: number,
    teamRoleKind: 'admin' | 'user',
  ) => {
    await unsetTeamAuth({
      userId,
      teamId,
      teamRoleKind,
    })
    setTeamInfo((prev) => {
      const newTeamInfo = [...prev]
      const index = newTeamInfo.findIndex((item) => item.id === teamId)
      newTeamInfo[index].role = ''
      return newTeamInfo
    })
    message.success('取消授权成功')
  }

  return (
    <>
      <Button size="small" color="primary" variant="link" onClick={showModal}>
        授权
      </Button>
      <Modal
        width={700}
        title={'角色授权 - ' + userName}
        open={isModalOpen}
        footer={null}
        onCancel={handleCancel}
        centered={true}
        maskClosable={false}
      >
        <div>
          <div className="font-bold my-5">权限</div>
          <div>
            <Checkbox
              checked={isAdmin}
              onChange={async () => {
                await setAdmin({ userId, isAdmin: !isAdmin })
                message.success('授权成功')
                setIsAdmin(!isAdmin)
              }}
            >
              管理员
            </Checkbox>
            {/* <Checkbox
              checked={isKnowledgebaseAdmin}
              onChange={async () => {
                await setKnowledgebaseAdmin({
                  userId,
                  isKnowledgebaseAdmin: !isKnowledgebaseAdmin,
                })
                setIsKnowledgebaseAdmin(!isKnowledgebaseAdmin)
              }}
            >
              知识库管理员
            </Checkbox>
            <Checkbox
              checked={isConsoleAdmin}
              onChange={async () => {
                await setConsoleAdmin({
                  userId,
                  isConsoleAdmin: !isConsoleAdmin,
                })
                setIsConsoleAdmin(!isConsoleAdmin)
              }}
            >
              控制台管理员
            </Checkbox> */}
          </div>
          <div>
            <div className="font-bold my-5">团队权限</div>
            <CommonTable<Team>
              size="small"
              scroll={{ y: 500 }}
              columns={[
                {
                  title: '团队名称',
                  dataIndex: 'teamName',
                  key: 'teamName',
                  sortDirections: ['descend', 'ascend'],
                  defaultSortOrder: 'ascend',
                  sorter: (a, b) =>
                    (a?.teamName ?? '')
                      .toLowerCase()
                      .localeCompare((b?.teamName ?? '').toLowerCase()),
                },
                {
                  title: '类型',
                  dataIndex: 'teamType',
                  key: 'teamType',
                  width: 120,
                  render: (val) => {
                    return `${
                      teamTypeMap[val as 'feature' | 'organization']
                    }团队`
                  },
                },
                {
                  title: '角色',
                  dataIndex: 'role',
                  key: 'role',
                  align: 'right',
                  render: (_, team) => {
                    return (
                      <Radio.Group
                        onChange={async (e) => {
                          const role = e.target.value
                          await setTeamAuth({
                            userId,
                            teamId: team.id,
                            teamRoleKind: role,
                          })
                          setTeamInfo((prev) => {
                            const newTeamInfo = [...prev]
                            const index = newTeamInfo.findIndex(
                              (item) => item.id === team.id,
                            )
                            newTeamInfo[index].role = role
                            return newTeamInfo
                          })
                          message.success('授权成功')
                        }}
                        value={team.role}
                      >
                        <Radio
                          value={'admin'}
                          onClick={() => {
                            if (team.role === 'admin') {
                              handleUnset(team.id, 'admin')
                            }
                          }}
                        >
                          admin
                        </Radio>
                        <Radio
                          value={'user'}
                          onClick={() => {
                            if (team.role === 'user') {
                              handleUnset(team.id, 'user')
                            }
                          }}
                        >
                          user
                        </Radio>
                      </Radio.Group>
                    )
                  },
                },
              ]}
              dataSource={teamInfo}
              pagination={false}
            />
          </div>
        </div>
      </Modal>
    </>
  )
}

export default AuthModal
