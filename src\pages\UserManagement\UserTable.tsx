import {
  type UserProfile,
  roleMap,
  syncTeam,
  useUserList,
  useUserProfile,
} from '@/api/user'
import CommonTable from '@/components/common/CommonTable'
import { SyncOutlined } from '@ant-design/icons'
import { Button, Input, Space, Table, Tag, message } from 'antd'
import type { TableProps } from 'antd'
import type React from 'react'
import { useMemo, useState } from 'react'
import AuthModal from './AuthModal'

const { Search } = Input

const UserTable: React.FC = () => {
  const [search, setSearch] = useState('')
  const [page, setPage] = useState(1)
  const [size, setSize] = useState(10)
  const { data, refetch, isLoading } = useUserList({
    page,
    size,
    search,
  })
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const columns = useMemo<TableProps<UserProfile>['columns']>(() => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 40,
      },
      {
        title: 'UID',
        dataIndex: 'uid',
        key: 'uid',
      },
      {
        title: '姓名',
        dataIndex: 'chineseName',
        key: 'chineseName',
      },
      {
        title: '用户名',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
      },
      {
        title: '工号',
        dataIndex: 'employeeNo',
        key: 'employeeNo',
      },
      // {
      //   title: '角色',
      //   key: 'role',
      //   dataIndex: 'role',
      //   width: 200,
      //   render: (_, { role }) => (
      //     <>
      //       {role
      //         ?.filter((item) => item !== 'user')
      //         ?.map((role, i) => {
      //           return (
      //             // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
      //             <Tag color={'success'} key={i}>
      //               {roleMap[role]}
      //             </Tag>
      //           )
      //         })}
      //     </>
      //   ),
      // },
      {
        title: '操作',
        key: 'ops',
        render: (_, record) => (
          <Space size="middle">
            <AuthModal
              userId={record.id}
              onClose={refetch}
              userName={record.chineseName}
            />
          </Space>
        ),
      },
    ]
  }, [])
  return (
    <div>
      <div className="flex justify-between py-4">
        <div className="flex items-center">
          <div className="w-[100px] font-semibold">用户名</div>
          <Input
            placeholder="Enter keyword to search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') refetch()
            }}
          />
        </div>
        <Button
          color="primary"
          variant="solid"
          icon={<SyncOutlined />}
          onClick={async () => {
            await syncTeam()
            refetch()
            message.success('团队同步成功')
          }}
        >
          同步团队
        </Button>
      </div>
      <CommonTable<UserProfile>
        loading={isLoading}
        columns={columns}
        dataSource={data?.list}
        pagination={{
          total: data?.total,
          onChange: (page, size) => {
            setPage(page)
            setSize(size)
          },
        }}
      />
    </div>
  )
}

export default UserTable
