import pureLogo from '@/assets/pureLogo.png'
import { Card } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { processLinkTo } from '@/utils/navigation'
import { DesktopOutlined } from '@ant-design/icons'
// Home.tsx
import {
  Button,
  Divider,
  Empty,
  Row,
  Select,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd'
import type React from 'react'
import { NavLink } from 'react-router-dom'
import {
  renderEnvironment,
  renderStatus,
  useEnterControll,
} from '../OnlineDev/util'
import {
  EnumTrainTaskExecutionState,
  TrainTaskExecutionStateMap,
  TriggerSourceEnumToLabel,
  formatDuration,
} from '../TrainTaskExecutionManagement/setting'
import useWorkspaceService from './useWorkspaceService'

const { Text } = Typography
export default function Dashboard() {
  const {
    onlineDevList,
    onlineDevListLoading,
    trainTeam,
    setTrainTeam,
    trainTaskExecutionList,
    trainTaskExecutionListLoading,
    teamList,
    setOnlineDevTeam,
    onlineDevTeam,
    refetch,
  } = useWorkspaceService()
  const { enteringDevId, handleEnter } = useEnterControll(async () => {
    refetch()
  })

  const renderItem = (label: string, content: React.ReactNode | string) => {
    return (
      <div className="w-1/3 flex">
        <div className="text-[#999999] text-right w-[80px] whitespace-nowrap">
          {label}：
        </div>
        {content}
      </div>
    )
  }
  return (
    <div className="flex mx-auto  flex-row flex-wrap gap-4">
      <Card className="relative overflow-hidden w-full p-4 bg-gradient-to-r from-[#7569F0] to-[#958CF1] text-[#F0F4FE] h-48 flex flex-col items-center justify-center border-none">
        <div className="flex items-center justify-center  h-16 w-16 bg-[#7367EF] rounded-full  mb-4">
          <DesktopOutlined className="text-3xl" />
        </div>
        <div className=" text-2xl mb-2 ">欢迎使用 锻星 MLOps 平台！</div>
        <div>提供从数据接入、模型训练、模型管理到模型服务的全流程开发</div>
        <img
          className="w-56 absolute right-[-32px] bottom-[-32px] opacity-15"
          src={pureLogo}
          alt="logo"
        />
      </Card>

      <div className="flex flex-col md:flex-row gap-4 w-full h-[calc(100vh-320px)]">
        <Card className="w-full md:w-1/2 border-none p-4 text-[#333333] ">
          <div className="flex items-center justify-between mb-4">
            <Space className="text-[#333333]">
              我的实验
              <Divider type="vertical" />
              <Select
                className="w-36"
                size="small"
                defaultValue={'all'}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={[
                  { value: 'all', label: '全部' },
                  ...teamList.map((team) => ({
                    value: team.id,
                    label: team.teamName,
                  })),
                ]}
                value={onlineDevTeam}
                onChange={(value) => setOnlineDevTeam(value)}
              />
            </Space>
            <NavLink
              className="text-[#7569f0]"
              to={processLinkTo('/online-dev') || ''}
            >
              查看更多
            </NavLink>
          </div>

          <ScrollArea className="h-[calc(100%-48px)]">
            <Spin spinning={onlineDevListLoading}>
              {onlineDevList?.map((item) => (
                <Card
                  key={item.id}
                  className="mb-4 last:mb-0 border-dashed p-4 text-[#333333]"
                >
                  <Row className="flex items-center justify-between mb-4">
                    <Space className="flex items-center flex-nowrap">
                      <NavLink
                        className="text-[#7569f0]"
                        to={
                          processLinkTo(
                            `/online-dev?team=${item.teamId}&id=${item.id}`,
                          ) || ''
                        }
                      >
                        {item.devName}
                      </NavLink>
                      <Tag color="#d7f1d4" className="!text-[#666666]">
                        {item.teamName || '暂无团队'}
                      </Tag>
                    </Space>
                    <Button
                      className="!p-0"
                      type="link"
                      disabled={enteringDevId === item.id}
                      loading={
                        enteringDevId === item.id || item.status === 'pending'
                      }
                      onClick={() => handleEnter(item)}
                    >
                      进入
                    </Button>
                  </Row>
                  <Row className="flex items-center   mb-4">
                    {renderItem('运行状态', renderStatus(item.status))}
                    {renderItem('开发环境', renderEnvironment(item.type))}
                    {renderItem(
                      '镜像地址',
                      <Text ellipsis={{ tooltip: item.imageUrl }}>
                        {item.imageUrl}
                      </Text>,
                    )}
                  </Row>
                  <Row className="flex items-center ">
                    {renderItem('创建人', item.createdByUserName)}
                    {renderItem('创建时间', item.createdAt)}
                  </Row>
                </Card>
              ))}
              {onlineDevList?.length ? (
                <div className="h-8 w-full text-center text-[#999999] mt-4 ">
                  仅展示前 5 条数据，查看更多请前往
                  <NavLink
                    className="text-[#7569f0]"
                    to={processLinkTo('/online-dev') || ''}
                  >
                    &nbsp;模型实验-在线开发页面
                  </NavLink>
                </div>
              ) : (
                <Empty
                  description={
                    <>
                      <div className="mb-2">您尚无在线实验的记录</div>
                      <div>
                        请先移步
                        <NavLink
                          className="text-[#7569f0]"
                          to={processLinkTo('/online-dev') || ''}
                        >
                          &nbsp;模型训练-在线开发页面&nbsp;
                        </NavLink>
                        处创建开发机
                      </div>
                    </>
                  }
                />
              )}
            </Spin>
          </ScrollArea>
        </Card>

        <Card className="w-full md:w-1/2 border-none p-4 ">
          <div className="flex items-center justify-between mb-4">
            <Space className="text-[#333333]">
              我的训练
              <Divider type="vertical" />
              <Select
                className="w-36"
                size="small"
                options={[
                  { value: 'all', label: '全部' },
                  ...teamList.map((team) => ({
                    value: team.id,
                    label: team.teamName,
                  })),
                ]}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                value={trainTeam}
                onChange={(value) => setTrainTeam(value)}
              />
            </Space>
            <NavLink
              className="text-[#7569f0]"
              to={processLinkTo('/train/task-execution') || ''}
            >
              查看更多
            </NavLink>
          </div>

          <ScrollArea className="h-[calc(100%-48px)] ">
            <Spin spinning={trainTaskExecutionListLoading}>
              {trainTaskExecutionList?.map((item) => (
                <Card
                  key={item.id}
                  className="mb-4 last:mb-0 border-dashed p-4 text-[#333333]"
                >
                  <Row className="flex items-center justify-between mb-4">
                    <Space className="flex items-center flex-nowrap">
                      <NavLink
                        className="text-[#7569f0]"
                        to={
                          processLinkTo(
                            `/train/task-execution?team=${item.teamId}&id=${item.id}`,
                          ) || ''
                        }
                      >
                        {item.taskName}
                      </NavLink>

                      <Tag color="#d7f1d4" className="!text-[#666666]">
                        {item.teamName || '暂无团队'}
                      </Tag>
                    </Space>
                    <Button
                      className="!p-0"
                      type="link"
                      onClick={() => window.open(item.dashboardUrl, '_blank')}
                      disabled={
                        item.status === EnumTrainTaskExecutionState.CANCELLED
                      }
                    >
                      实时监控
                    </Button>
                  </Row>
                  <Row className="flex items-center mb-4 ">
                    {renderItem(
                      '状态',
                      <Space className="flex  justify-center">
                        {TrainTaskExecutionStateMap[item.status]?.label}
                        {TrainTaskExecutionStateMap[item.status]?.icon}
                      </Space>,
                    )}
                    {renderItem(
                      '运行时长',
                      item.startTime ? (
                        formatDuration(
                          item.startTime,
                          item.endTime ?? undefined,
                        )
                      ) : (
                        <span className="text-[#999999]">
                          （预计排队 X 小时）
                        </span>
                      ),
                    )}
                  </Row>
                  <Row className="flex items-center">
                    {renderItem('触发人', item.triggeredByUserName)}
                    {renderItem(
                      '触发类型',
                      TriggerSourceEnumToLabel[item.triggerSource],
                    )}
                    {renderItem('触发时间', item.triggerTime)}
                  </Row>
                </Card>
              ))}
              {trainTaskExecutionList?.length ? (
                <div className="h-8 w-full text-center text-[#999999] mt-4 ">
                  仅展示前 5 条数据，查看更多请前往
                  <NavLink
                    className="text-[#7569f0]"
                    to={processLinkTo('/train/task-execution') || ''}
                  >
                    &nbsp;模型训练-任务运行页面
                  </NavLink>
                </div>
              ) : (
                <Empty
                  description={
                    <>
                      <div className="mb-2">您尚无训练过的任务</div>
                      <div>
                        请先移步
                        <NavLink
                          className="text-[#7569f0]"
                          to={processLinkTo('/train/task-execution') || ''}
                        >
                          &nbsp;模型训练-任务运行页面&nbsp;
                        </NavLink>
                        处运行任务
                      </div>
                    </>
                  }
                />
              )}
            </Spin>
          </ScrollArea>
        </Card>
      </div>
    </div>
  )
}
