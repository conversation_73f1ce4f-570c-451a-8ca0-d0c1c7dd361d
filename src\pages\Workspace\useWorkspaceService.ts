import {
  type OnlineDevItem,
  getOnlineDevList,
  startOnlineDev,
} from '@/api/onlinedev'
import { getTrainTaskExecutionList as getTrainTaskExecutionListApi } from '@/api/traintask'
import useUserStore from '@/stores/user'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'

export default function useWorkspaceService() {
  const [trainTeam, setTrainTeam] = useState<'all' | number>('all')
  const [onlineDevTeam, setOnlineDevTeam] = useState<'all' | number>('all')
  const userInfo = useUserStore((state) => state.userInfo)

  const teamList = userInfo?.team || []

  const {
    data: trainTaskExecutionList,
    isLoading: trainTaskExecutionListLoading,
  } = useQuery({
    queryKey: ['teamData', trainTeam, userInfo?.employeeNo],
    enabled: !!userInfo?.employeeNo, // 确保 userInfo 已加载
    queryFn: async () => {
      const res = await getTrainTaskExecutionListApi({
        params: {
          page: 1,
          pageSize: 5,
          triggeredByEmployeeNo: userInfo?.employeeNo,
          ...(trainTeam === 'all' ? null : { teamId: trainTeam }),
        },
      })
      return res.list
    }, // 请求函数
  })

  const {
    refetch,
    data: onlineDevList,
    isLoading: onlineDevListLoading,
  } = useQuery({
    queryKey: ['teamData', onlineDevTeam],
    enabled: !!userInfo?.employeeNo, // 确保 userInfo 已加载
    queryFn: async () => {
      const res = await getOnlineDevList({
        page: 1,
        pageSize: 5,
        onlyMy: true,
        ...(onlineDevTeam === 'all' ? null : { teamId: onlineDevTeam }),
      })
      return res.list
    }, // 请求函数
  })

  return {
    onlineDevList,
    onlineDevListLoading,
    setTrainTeam,
    trainTeam,
    trainTaskExecutionList,
    trainTaskExecutionListLoading,
    teamList,

    setOnlineDevTeam,
    onlineDevTeam,
    refetch,
  }
}
