import { type TokenCreate, type TokenItem, createToken } from '@/api/token'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, Input, Modal, Select } from 'antd'
import form from 'antd/es/form'
import {
  type Ref,
  forwardRef,
  memo,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'

type Props = {
  onSuccess?: () => void
}
export type SettingModalHandle = {
  showModal: (type: 'create' | 'edit', data?: TokenItem) => void
}
export default memo(
  forwardRef<SettingModalHandle, Props>(({ onSuccess }, ref) => {
    const [form] = Form.useForm<TokenCreate>()
    const [isModalVisible, setIsModalVisible] = useState(false)
    const id = useRef<number>()
    const type = useRef<'create' | 'edit'>('create')
    const data = {
      name: '',
      desc: '',
    }

    // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
    useImperativeHandle(
      ref,
      () => ({
        showModal: (t: 'create' | 'edit', data?: TokenItem) => {
          setIsModalVisible(true)
          type.current = t
          if (data) {
            id.current = data.id
            form.setFieldsValue(data)
          } else {
            form.resetFields()
          }
        },
      }),
      [],
    )

    const onFinish = (values: any) => {
      // handleConfig(id ? { id, ...values } : values, () => setModalVisible(false))
      console.log(values)
    }

    const onFinishFailed = (errorInfo: any) => {
      console.log('Failed:', errorInfo)
    }
    const handleOk = async () => {
      await form.validateFields()
      const data = form.getFieldsValue()
      if (type.current === 'create') await createToken(data)
      if (onSuccess) onSuccess()
      setIsModalVisible(false)
    }

    const handleCancel = () => {
      setIsModalVisible(false)
    }
    return (
      <Modal
        title={`${type.current === 'create' ? '添加' : '修改'}OpenAPI Token`}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          name="configForm"
          layout="vertical"
          initialValues={data}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: 'Please input the name!' }]}
          >
            <Input disabled={type.current === 'edit'} />
          </Form.Item>

          <Form.Item
            label="Description"
            name="desc"
            rules={[
              { required: true, message: 'Please input the description!' },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    )
  }),
)
