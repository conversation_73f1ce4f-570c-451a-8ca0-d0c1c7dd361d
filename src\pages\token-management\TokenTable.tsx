import { type TokenItem, delToken, useTokenList } from '@/api/token'
import CommonTable from '@/components/common/CommonTable'
import { CopyOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons'
import { Button, Input, Popconfirm, Space, Table, Tag, message } from 'antd'
import type { TableProps } from 'antd'
import type React from 'react'
import { useMemo, useRef, useState } from 'react'
import TokenModal, { type SettingModalHandle } from './TokenModal'

const { Search } = Input

const TokenTable: React.FC = () => {
  const modalRef = useRef<SettingModalHandle>(null)
  const [search, setSearch] = useState('')
  const [page, setPage] = useState(1)
  const [size, setSize] = useState(10)
  const { data, refetch, isLoading } = useTokenList({
    page,
    size,
    s_name: search,
  })
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const columns = useMemo<TableProps<TokenItem>['columns']>(() => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 40,
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '描述',
        dataIndex: 'desc',
        key: 'desc',
      },
      {
        title: 'Token',
        dataIndex: 'token',
        key: 'token',
        render: (_, record) => (
          <div>
            <Button
              size="small"
              color="primary"
              variant="text"
              icon={<CopyOutlined />}
              onClick={() => {
                navigator.clipboard.writeText(record.token!)
                message.success('Token copied')
              }}
            />
            <span>**********************</span>
          </div>
        ),
      },
      {
        title: '操作',
        key: 'ops',
        width: 80,
        render: (_, record) => (
          <Space size="middle">
            <Popconfirm
              title="删除"
              description="确认是否删除?"
              onConfirm={async () => {
                await delToken(record.id)
                refetch()
              }}
              okText="确认"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        ),
      },
    ]
  }, [])
  return (
    <div>
      <TokenModal ref={modalRef} onSuccess={() => refetch()} />
      <Button
        color="primary"
        variant="solid"
        icon={<PlusOutlined />}
        onClick={async () => {
          modalRef.current?.showModal('create')
        }}
      >
        新增
      </Button>
      <div className="flex justify-between py-4">
        <div className="flex items-center">
          <div className="w-[100px] font-semibold">名称</div>
          <Input
            placeholder="Enter keyword to search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') refetch()
            }}
          />
        </div>
      </div>
      <CommonTable<TokenItem>
        rowKey="id"
        loading={isLoading}
        columns={columns}
        dataSource={data?.list}
        pagination={{
          current: page,
          total: data?.total,
          onChange: (page, size) => {
            setPage(page)
            setSize(size)
          },
        }}
      />
    </div>
  )
}

export default TokenTable
