import Error403 from '@/components/layout/Error403'
import MemberManagement from '@/pages/MemberManagement'
import { OnlineDevList } from '@/pages/OnlineDev/OnlineDevList'
import AdminResourceDashboard from '@/pages/ResourceDashboard/AdminResourceDashboard'
import TeamResourceDashboard from '@/pages/ResourceDashboard/TeamResourceDashboard'
import PrometheusChartDemo from '@/pages/ResourceDashboard/components/PrometheusChartDemo'
import ResourceManagement from '@/pages/ResourceManagement/ResourceManagement'
import TeamResourceManagement from '@/pages/ResourceManagement/TeamResourceManagement'
import SystemConfig from '@/pages/SysConfig/SystemConfig'
import TrainTaskExecutionDetail from '@/pages/TrainTaskExecutionDetail'
import TrainTaskExecutionManagement from '@/pages/TrainTaskExecutionManagement'
import { TaskManagementList } from '@/pages/TrainTaskManagement/TaskManagementList'
import UserTable from '@/pages/UserManagement/UserTable'
import Workspace from '@/pages/Workspace'
import TokenTable from '@/pages/token-management/TokenTable'
import {
  BlockOutlined,
  CodeOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  DesktopOutlined,
  KeyOutlined,
  OrderedListOutlined,
  PieChartOutlined,
  ProductOutlined,
  ProjectOutlined,
  RocketOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons'
import {
  type RouteObject,
  createBrowserRouter,
  replace,
} from 'react-router-dom'
import Error404 from '../components/layout/Error404'
import Root from '../components/layout/Root'
import useUserStore from '../stores/user'

export type RouteHandle = {
  name: string
  icon?: React.ReactNode
  teamAdminOnly?: boolean
}
type CustomRouteObject = RouteObject & {
  handle?: RouteHandle
}
const indexPath = '/workspace' // 默认页面路径

export const user = [
  {
    index: true,
    loader: () => replace(indexPath),
  },
  {
    path: '/workspace',
    element: <Workspace />,
    handle: {
      name: '工作台',
      icon: <ProductOutlined />,
    },
  },
  {
    path: '/resource-dashboard',
    element: <TeamResourceDashboard />,
    handle: {
      name: '资源大盘',
      icon: <DashboardOutlined />,
    },
  },
  //   {
  //     path: '/prometheus-chart-demo',
  //     element: <PrometheusChartDemo />,
  //     handle: {
  //       name: 'Prometheus 图表演示',
  //       icon: <PieChartOutlined />,
  //     },
  //   },

  {
    handle: {
      name: '模型实验',
      icon: <RocketOutlined />,
    },
    children: [
      {
        path: '/online-dev',
        element: <OnlineDevList />,
        handle: {
          name: '在线开发',
          icon: <DesktopOutlined />,
        },
      },
    ],
  },
  {
    handle: {
      name: '模型训练',
    },
    children: [
      {
        path: '/train/task-management',
        element: <TaskManagementList />,
        handle: {
          name: '任务管理',
          icon: <OrderedListOutlined />,
        },
      },
      {
        path: '/train/task-execution',
        element: <TrainTaskExecutionManagement />,
        handle: {
          name: '任务运行',
          icon: <CodeOutlined />,
        },
      },
      //   {
      //     path: '/model/tool',
      //     element: <TaskManagementList />,
      //     handle: {
      //       name: '监控看板',
      //       icon: <SettingOutlined />,
      //     },
      //   },
    ],
  },

  {
    handle: {
      name: '团队设置',
    },
    children: [
      {
        path: '/team/member-management',
        element: <MemberManagement />,
        handle: {
          name: '成员管理',
          icon: <UserOutlined />,
        },
      },
      {
        path: '/team/resource-management',
        element: <TeamResourceManagement />,
        handle: {
          name: '资源管理',
          icon: <DatabaseOutlined />,
          // teamAdminOnly: true,
        },
      },
    ],
  },
]

export const admin = [
  {
    handle: {
      name: '管理后台',
    },
    children: [
      {
        path: '/admin/resource-dashboard',
        element: <AdminResourceDashboard />,
        handle: {
          name: '集团资源大盘',
          icon: <ProjectOutlined />,
        },
      },
      {
        path: '/admin/resource-management',
        element: <ResourceManagement />,
        handle: {
          name: '集团资源管理',
          icon: <DatabaseOutlined />,
        },
      },
      {
        path: '/admin/user-management',
        element: <UserTable />,
        handle: {
          name: '用户管理',
          icon: <BlockOutlined />,
        },
      },
      {
        path: '/admin/sys-config',
        element: <SystemConfig />,
        handle: {
          name: '系统配置',
          icon: <SettingOutlined />,
        },
      },
      {
        path: '/admin/token-management',
        element: <TokenTable />,
        handle: {
          name: 'Token 管理',
          icon: <KeyOutlined />,
        },
      },
    ],
  },
]

// 过滤路由的函数，应用与LeftMenu.tsx相同的逻辑
const filterRoutes = (
  routes: CustomRouteObject[],
  currentRoleAtTeam: string,
): CustomRouteObject[] => {
  return routes.map((route) => {
    if (route.children) {
      const filteredChildren = route.children.filter((child) => {
        if (!child.handle) return false
        return child.handle.teamAdminOnly ? currentRoleAtTeam === 'admin' : true
      })
      return {
        ...route,
        children: filteredChildren,
      }
    }
    return route
  })
}

// 创建动态路由的函数
export const createDynamicRouter = () => {
  const currentRoleAtTeam = useUserStore.getState().currentRoleAtTeam
  const userInfo = useUserStore.getState().userInfo
  const isAdmin = userInfo?.role.includes('admin')
  // 防止数据没到直接全拦了
  const menu = !userInfo || isAdmin ? [...user, ...admin] : user
  const filteredRoutes = filterRoutes(menu, currentRoleAtTeam)

  const routes: CustomRouteObject[] = [
    {
      path: '/',
      element: <Root />,
      children: filteredRoutes,
      errorElement: <Error404 />,
    },
    {
      path: '/train/task-execution/:id',
      element: <TrainTaskExecutionDetail />,
    },
    {
      path: '/403',
      element: <Error403 />,
    },
    {
      path: '*',
      element: <Error404 />,
    },
  ]

  return createBrowserRouter(routes)
}
