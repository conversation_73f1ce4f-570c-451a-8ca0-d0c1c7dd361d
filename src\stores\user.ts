import type { UserProfile } from '@/api/user'
import type { EnumUserRole } from '@/pages/MemberManagement/setting'
import { getCookie } from '@/utils/getCookie'
import { create } from 'zustand'

type State = {
  userInfo?: UserProfile
  token: string
  role: string
  currentTeam: number
  currentRoleAtTeam: EnumUserRole | '' // 如果用户不在这个团队中，那么 currentRoleAtTeam 为空字符串
  teamAndRoleMap: Record<number, string>
}

type Actions = {
  setUserInfo: (userInfo: UserProfile) => void
  setToken: (token: string) => void
  setRole: (role: string) => void
  clearToken: () => void
  setCurrentTeam: (currentTeam: number) => void
  getCurrentTeam: () => number
}

const useUserStore = create<State & Actions>((set, get) => ({
  userInfo: undefined,
  teamAndRoleMap: {}, // 存储团队长id和角色的映射
  token: getCookie('Authorization') || '',
  role: '',
  currentTeam: 0,
  getCurrentTeam: () => {
    const { currentTeam } = get()

    // 如果当前 team 有效，直接返回
    if (currentTeam && currentTeam !== 0) {
      return currentTeam
    }

    // 尝试从 URL 查询参数中获取 team
    const urlParams = new URLSearchParams(window.location.search)
    const teamFromQuery = urlParams.get('team')

    if (teamFromQuery) {
      const teamId = Number(teamFromQuery)
      if (!Number.isNaN(teamId) && teamId > 0) {
        return teamId
      }
    }

    return currentTeam
  },
  currentRoleAtTeam: '',
  setUserInfo: (userInfo) => {
    const teamAndRoleMap = userInfo.team.reduce<Record<number, string>>(
      (map, team) => {
        map[team.id] = team.role
        return map
      },
      {},
    )
    set({ userInfo, teamAndRoleMap })
  },
  setToken: (token) => set({ token }),
  setRole: (role) => set({ role }),
  setCurrentTeam: (currentTeam) => {
    console.log('setCurrentTeam', currentTeam)
    const { teamAndRoleMap } = get()
    const currentRoleAtTeam =
      (teamAndRoleMap[currentTeam] as EnumUserRole) || ''

    set({ currentTeam, currentRoleAtTeam })
  },
  clearToken: () => set({ token: '' }),
}))

export default useUserStore
