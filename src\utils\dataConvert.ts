import type { OnlineDevDetail } from '@/api/onlinedev'
import type {
  TrainTaskClusterResource,
  TrainTaskDetail,
  TrainTaskVolumeMount,
  TrainingFramework,
} from '@/api/traintask'
import type { OnlineDevFormData } from '@/components/forms/OnlineDevForm'
import type { TaskFormData } from '@/components/forms/TaskForm'

const gpuCoreMap: Record<string, number> = {
  '1/4': 25,
  '1/2': 50,
  '1': 100,
  '2': 200,
  '3': 300,
  '4': 400,
  '5': 500,
  '6': 600,
  '7': 700,
  '8': 800,
}

export const convertToTrainTaskDetail = (
  values: TaskFormData,
  team: number,
  clusterId: number,
): TrainTaskDetail => {
  // 构建 clusterResource
  const gpuCore = values.gpuCore ? gpuCoreMap[values.gpuCore] : 0 // 核心传到后端要乘 100
  const clusterResource: TrainTaskClusterResource = {
    limitCpu: values.cpu?.limits?.toString() || '0',
    limitGpuCore: gpuCore,
    limitGpuMemory: (values.gpuMemory || 0).toString(),
    limitMemory: (values.memory?.limits || 0) + 'Gi',
    requestCpu: values.cpu?.requests?.toString() || '0',
    requestGpuCore: gpuCore,
    requestGpuMemory: values.gpuMemory,
    requestMemory: (values.memory?.requests || 0) + 'Gi',
    maxReplicas: values.maxReplicas || 0,
    minReplicas: 0,
    gpuType: values.gpuType || '',
  }

  const volumeMounts: TrainTaskVolumeMount[] =
    values.volumeMounts?.map((mount) => ({
      mountPath: mount.mountPath || '',
      name: mount.name || '',
      subPath: mount.subPath || '',
      volumeName: mount.volumeName || '',
      volumeType: mount.volumeType || '',
    })) || []

  return {
    clusterId,
    clusterName: values.clusterNs[0] || '',
    clusterResource,
    imageUrl: values.imageUrl || '',
    namespace: values.clusterNs[1] || '',
    priority: values.priority || 'P1',
    startCmd: values.startCmd || '',
    taskName: values.taskName || '',
    taskType: values.taskType || 'FORM',
    trainingFramework: values.trainingFramework || 'RAY',
    taskYaml: values.taskType === 'YAML' ? values.taskYaml || '{}' : '{}',
    teamId: Number(team) || 0,
    volumeMounts,
    envVars:
      values.envVars?.reduce(
        (acc, cur) => {
          acc[cur.key!] = cur.value || ''
          return acc
        },
        {} as Record<string, string>,
      ) || {},
    appName: values.appName,
    cmdbId: values.cmdbId,
  }
}

export const convertTrainTaskDetailToFormData = (
  detail: TrainTaskDetail,
): TaskFormData => {
  return {
    taskName: detail.taskName,
    namespace: detail.namespace,
    clusterNs: [detail.clusterName, detail.namespace],
    priority: detail.priority as 'P0' | 'P1' | 'P2' | 'P3',
    taskType: detail.taskType as 'FORM' | 'YAML',
    trainingFramework: detail.trainingFramework as TrainingFramework,
    appName: detail.appName,
    cmdbId: detail.cmdbId,
    imageUrl: detail.imageUrl,
    startCmd: detail.startCmd,
    cpu: {
      requests: detail.clusterResource?.requestCpu
        ? Number.parseFloat(detail.clusterResource.requestCpu)
        : undefined,
      limits: detail.clusterResource?.limitCpu
        ? Number.parseFloat(detail.clusterResource.limitCpu)
        : undefined,
    },
    memory: {
      requests: detail.clusterResource?.requestMemory
        ? Number.parseFloat(detail.clusterResource.requestMemory)
        : undefined,
      limits: detail.clusterResource?.limitMemory
        ? Number.parseFloat(detail.clusterResource.limitMemory)
        : undefined,
    },
    gpuCore: detail.clusterResource?.requestGpuCore
      ? Object.keys(gpuCoreMap).find((key) => {
        return gpuCoreMap[key] === +detail.clusterResource.requestGpuCore!
      })
      : undefined,
    gpuMemory: detail.clusterResource?.requestGpuMemory,
    gpuType: detail.clusterResource?.gpuType,
    maxReplicas: detail.clusterResource?.maxReplicas,
    volumeMounts: detail.volumeMounts?.map((mount) => ({
      volumeType: mount.volumeType as 'pvc' | 'configmap' | 'secret',
      name: mount.name,
      volumeName: mount.volumeName,
      mountPath: mount.mountPath,
      subPath: mount.subPath,
    })),
    taskYaml: detail.taskYaml || '{}',
    envVars: Object.entries(detail.envVars || {}).map(([key, value]) => ({
      key,
      value,
    })),
  }
}

export const convertToOnlineDevDetail = (
  values: OnlineDevFormData,
  team: number,
  clusterId: number,
): OnlineDevDetail => {
  // 构建 clusterResource
  const gpuCore = values.gpuCore ? gpuCoreMap[values.gpuCore] : 0 // 核心传到后端要乘 100
  const clusterResource: TrainTaskClusterResource = {
    limitCpu: values.cpu?.limits?.toString() || '0',
    limitGpuCore: gpuCore,
    limitGpuMemory: (values.gpuMemory || 0).toString(),
    limitMemory: (values.memory?.limits || 0) + 'Gi',
    requestCpu: values.cpu?.requests?.toString() || '0',
    requestGpuCore: gpuCore,
    requestGpuMemory: values.gpuMemory,
    requestMemory: (values.memory?.requests || 0) + 'Gi',
    maxReplicas: values.maxReplicas || 0,
    minReplicas: 0,
    gpuType: values.gpuType || '',
  }

  const volumeMounts: TrainTaskVolumeMount[] =
    values.volumeMounts?.map((mount) => ({
      mountPath: mount.mountPath || '',
      name: mount.name || '',
      subPath: mount.subPath || '',
      volumeName: mount.volumeName || '',
      volumeType: mount.volumeType || '',
    })) || []

  return {
    clusterId,
    clusterName: values.clusterNs[0] || '',
    clusterResource,
    imageUrl: values.imageUrl || '',
    namespace: values.clusterNs[1] || '',
    devName: values.devName || '',
    type: values.type || 'jupyter',
    teamId: Number(team) || 0,
    volumeMounts,
    envVars:
      values.envVars?.reduce(
        (acc, cur) => {
          acc[cur.key!] = cur.value || ''
          return acc
        },
        {} as Record<string, string>,
      ) || {},
    appName: values.appName,
    cmdbId: values.cmdbId,
    devUrl: '',
  }
}

export const convertOnlineDevDetailToFormData = (
  detail: OnlineDevDetail,
): OnlineDevFormData => {
  return {
    devName: detail.devName,
    namespace: detail.namespace,
    clusterNs: [detail.clusterName, detail.namespace],
    type: detail.type as 'jupyter' | 'code-server',
    appName: detail.appName,
    cmdbId: detail.cmdbId,
    imageUrl: detail.imageUrl,
    cpu: {
      requests: detail.clusterResource?.requestCpu
        ? Number.parseFloat(detail.clusterResource.requestCpu)
        : undefined,
      limits: detail.clusterResource?.limitCpu
        ? Number.parseFloat(detail.clusterResource.limitCpu)
        : undefined,
    },
    memory: {
      requests: detail.clusterResource?.requestMemory
        ? Number.parseFloat(detail.clusterResource.requestMemory)
        : undefined,
      limits: detail.clusterResource?.limitMemory
        ? Number.parseFloat(detail.clusterResource.limitMemory)
        : undefined,
    },
    gpuCore: detail.clusterResource?.requestGpuCore
      ? Object.keys(gpuCoreMap).find((key) => {
        return gpuCoreMap[key] === +detail.clusterResource.requestGpuCore!
      })
      : undefined,
    gpuMemory: detail.clusterResource?.requestGpuMemory,
    gpuType: detail.clusterResource?.gpuType,
    maxReplicas: detail.clusterResource?.maxReplicas,
    volumeMounts: detail.volumeMounts?.map((mount) => ({
      volumeType: mount.volumeType as 'pvc' | 'configmap' | 'secret',
      name: mount.name,
      volumeName: mount.volumeName,
      mountPath: mount.mountPath,
      subPath: mount.subPath,
    })),
    envVars: Object.entries(detail.envVars || {}).map(([key, value]) => ({
      key,
      value,
    })),
  }
}
