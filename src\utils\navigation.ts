import useUserStore from '@/stores/user'
import type { NavigateOptions, To } from 'react-router-dom'

/**
 * 封装的导航函数，确保在所有导航操作中都包含team参数
 * @param navigate 原始的navigate函数
 * @param to 目标路径
 * @param options 导航选项
 */
export function navigateWithTeam(
  navigate: (to: string, options?: NavigateOptions) => void,
  to: string,
  options?: NavigateOptions,
): void {
  // 解析目标路径
  let path = to
  let query = ''

  // 分离路径和查询参数
  if (to.includes('?')) {
    const [pathPart, queryPart] = to.split('?')
    path = pathPart
    query = queryPart
  }

  // 创建或使用现有的URLSearchParams
  const searchParams = query
    ? new URLSearchParams(query)
    : new URLSearchParams(window.location.search)

  // 确保team参数存在
  const lastSelectedTeam = useUserStore.getState().currentTeam
  if (lastSelectedTeam) {
    searchParams.set('team', lastSelectedTeam + '')
  }

  // 构建最终的URL
  const finalUrl = searchParams.toString()
    ? `${path}?${searchParams.toString()}`
    : path

  // 调用原始的navigate函数
  navigate(finalUrl, options)
}

/**
 * 处理Link组件的to属性，确保包含team参数
 * @param to 目标路径
 * @returns 处理后的路径
 */
export function processLinkTo(to: To): To {
  // 如果to是字符串类型
  if (typeof to === 'string') {
    let path = to
    let query = ''

    // 分离路径和查询参数
    if (to.includes('?')) {
      const [pathPart, queryPart] = to.split('?')
      path = pathPart
      query = queryPart
    }

    // 创建或使用现有的URLSearchParams
    const searchParams = query
      ? new URLSearchParams(query)
      : new URLSearchParams(window.location.search)

    // 确保team参数存在
    if (!searchParams.has('team')) {
      const lastSelectedTeam = useUserStore.getState().currentTeam
      if (lastSelectedTeam) {
        searchParams.set('team', lastSelectedTeam + '')
      }
    }

    // 构建最终的URL
    return searchParams.toString() ? `${path}?${searchParams.toString()}` : path
  }

  // 如果to是对象类型
  if (typeof to === 'object' && to !== null) {
    const newTo = { ...to }
    const search = newTo.search || ''
    const searchParams = new URLSearchParams(search)

    // 确保team参数存在
    if (!searchParams.has('team')) {
      // 从localStorage获取team，如果存在
      const lastSelectedTeam = useUserStore.getState().currentTeam
      if (lastSelectedTeam) {
        searchParams.set('team', lastSelectedTeam + '')
      }
    }

    // 更新search属性
    newTo.search = searchParams.toString()
    return newTo
  }

  // 如果是其他类型，直接返回
  return to
}
