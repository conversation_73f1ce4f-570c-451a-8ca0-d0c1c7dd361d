import { Button, DatePicker, Input, Space } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import type { FilterDropdownProps } from 'antd/es/table/interface'
import type { Dayjs } from 'dayjs'

// 自定义筛选项功能具体实现请参考 https://ant.design/components/table-cn/#components-table-demo-custom-filter-panel
export const renderCustomFilterDropdown: React.FC<
  FilterDropdownProps & {
    type: 'RangePicker' | 'Input'
  }
> = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, type }) => (
  <div className="w-80 ">
    <div className="w-full p-4" onKeyDown={(e) => e.stopPropagation()}>
      {type === 'RangePicker' ? (
        <DatePicker.RangePicker
          value={selectedKeys as unknown as RangePickerProps['value']}
          onChange={(dates) => setSelectedKeys(dates as unknown as string[])}
        />
      ) : (
        <Input
          placeholder="请输入"
          value={selectedKeys[0] as string}
          onChange={(e) => setSelectedKeys([e.target.value])}
        />
      )}
    </div>
    <hr />
    <Space className="w-full p-2 flex justify-end">
      <Button
        onClick={
          clearFilters
            ? () => clearFilters({ closeDropdown: true, confirm: true })
            : undefined
        }
        size="small"
      >
        重置
      </Button>
      <Button
        type="primary"
        onClick={() => confirm({ closeDropdown: true })}
        size="small"
      >
        确定
      </Button>
    </Space>
  </div>
)

export const formatDateRange = (key: string, filter: Record<string, any>) => {
  const value = filter?.[key] as [Dayjs, Dayjs]
  return value
    ? {
        [key]: [
          value[0].format('YYYY-MM-DD 00:00:00'),
          value[1].format('YYYY-MM-DD 23:59:59'),
        ],
      }
    : {}
}
